<template>
	<view class="container">
		<!-- 用户信息头部 -->
		<view class="user-header" @click="handleHeaderClick">
			<view class="user-avatar">
				<image v-if="isLoggedIn" :src="userInfo.avatar" mode="aspectFill"></image>
				<text v-else class="avatar-placeholder">?</text>
			</view>
			<view class="user-info">
				<text class="user-name">{{ isLoggedIn ? userInfo.name : '立即登录' }}</text>
				<text class="user-desc">{{ isLoggedIn ? userInfo.description : '登录后享受更多功能' }}</text>
			</view>
			<view class="user-stats" v-if="isLoggedIn">
				<view class="stat-item">
					<text class="stat-number">{{ userInfo.favoriteCount }}</text>
					<text class="stat-label">收藏</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ userInfo.viewCount }}</text>
					<text class="stat-label">浏览</text>
				</view>
			</view>
			<view class="login-prompt" v-else>
				<text class="login-text">点击登录</text>
			</view>
		</view>
		
		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-group">
				<view class="menu-item" @click="goToFavorites">
					<text class="menu-icon">❤️</text>
					<text class="menu-text">我的收藏</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @click="goToHistory">
					<text class="menu-icon">📖</text>
					<text class="menu-text">浏览历史</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @click="goToNotes">
					<text class="menu-icon">📝</text>
					<text class="menu-text">我的笔记</text>
					<text class="menu-arrow">></text>
				</view>
			</view>
			
			<view class="menu-group">
				<view class="menu-item" @click="goToSettings">
					<text class="menu-icon">⚙️</text>
					<text class="menu-text">设置</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @click="goToHelp">
					<text class="menu-icon">❓</text>
					<text class="menu-text">帮助与反馈</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @click="goToAbout">
					<text class="menu-icon">ℹ️</text>
					<text class="menu-text">关于我们</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item logout-item" @click="logout" v-if="isLoggedIn">
					<text class="menu-icon logout-icon">🚪</text>
					<text class="menu-text logout-text">退出登录</text>
				</view>
			</view>
		</view>
		
		<!-- 最近收藏 -->
		<view class="recent-section">
			<view class="section-header">
				<text class="section-title">最近收藏</text>
				<text class="section-more" @click="goToFavorites">查看全部 ></text>
			</view>
			<view class="recent-list">
				<view class="recent-item" v-for="item in recentFavorites" :key="item.id" @click="goToDetail(item)">
					<image class="recent-image" :src="item.image" mode="aspectFill"></image>
					<view class="recent-info">
						<text class="recent-title">{{ item.title }}</text>
						<text class="recent-type">{{ item.type }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部导航 -->
		<view class="bottom-nav">
			<view class="nav-item" @click="goToPage('home')">
				<text class="nav-icon">🏠</text>
				<text class="nav-text">首页</text>
			</view>
			<view class="nav-item" @click="goToPage('ingredients')">
				<text class="nav-icon">🌿</text>
				<text class="nav-text">药材食材</text>
			</view>
			<view class="nav-item add-btn">
				<text class="nav-icon add-icon">+</text>
			</view>
			<view class="nav-item" @click="goToPage('recipe')">
				<text class="nav-icon">📋</text>
				<text class="nav-text">食疗方案</text>
			</view>
			<view class="nav-item active" @click="goToPage('profile')">
				<text class="nav-icon">👤</text>
				<text class="nav-text">我的</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isLoggedIn: false,
			userInfo: {
				name: '养生达人',
				description: '关注健康，享受生活',
				avatar: '/static/logo.png',
				favoriteCount: 28,
				viewCount: 156
			},
			recentFavorites: [
				{
					id: 1,
					title: '补气养生汤',
					type: '食疗方案',
					image: '/static/logo.png'
				},
				{
					id: 2,
					title: '黄芪',
					type: '中药材',
					image: '/static/logo.png'
				},
				{
					id: 3,
					title: '清热解暑粥',
					type: '食疗方案',
					image: '/static/logo.png'
				}
			]
		}
	},

	onLoad() {
		this.checkLoginStatus();
	},

	onShow() {
		// 每次显示页面时检查登录状态
		this.checkLoginStatus();
	},

	methods: {
		// 检查登录状态
		checkLoginStatus() {
			const userInfo = uni.getStorageSync('userInfo');
			const token = uni.getStorageSync('token');

			if (userInfo && token) {
				this.isLoggedIn = true;
				this.userInfo = {
					...this.userInfo,
					...userInfo
				};
			} else {
				this.isLoggedIn = false;
			}
		},

		// 处理头部点击事件
		handleHeaderClick() {
			if (!this.isLoggedIn) {
				this.goToLogin();
			}
		},

		// 跳转到登录页面
		goToLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			});
		},
		// 跳转到收藏页面
		goToFavorites() {
			uni.showToast({
				title: '跳转到收藏页面',
				icon: 'none'
			});
		},
		
		// 跳转到历史记录
		goToHistory() {
			uni.showToast({
				title: '跳转到浏览历史',
				icon: 'none'
			});
		},
		
		// 跳转到笔记
		goToNotes() {
			uni.showToast({
				title: '跳转到我的笔记',
				icon: 'none'
			});
		},
		
		// 跳转到设置
		goToSettings() {
			uni.showToast({
				title: '跳转到设置页面',
				icon: 'none'
			});
		},
		
		// 跳转到帮助
		goToHelp() {
			uni.showToast({
				title: '跳转到帮助页面',
				icon: 'none'
			});
		},
		
		// 跳转到关于
		goToAbout() {
			uni.showToast({
				title: '跳转到关于页面',
				icon: 'none'
			});
		},

		// 退出登录
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				confirmText: '退出',
				cancelText: '取消',
				confirmColor: '#ff4757',
				success: (res) => {
					if (res.confirm) {
						// 清除本地存储的用户信息
						uni.removeStorageSync('userInfo');
						uni.removeStorageSync('token');

						// 更新登录状态
						this.isLoggedIn = false;

						// 显示退出成功提示
						uni.showToast({
							title: '已退出登录',
							icon: 'success'
						});
					}
				}
			});
		},
		
		// 跳转到详情
		goToDetail(item) {
			if (item.type === '食疗方案') {
				uni.navigateTo({
					url: `/pages/recipe-detail/recipe-detail?id=${item.id}`
				});
			} else if (item.type === '中药材') {
				uni.navigateTo({
					url: `/pages/ingredient-detail/ingredient-detail?id=${item.id}`
				});
			}
		},
		
		// 导航跳转
		goToPage(page) {
			switch(page) {
				case 'home':
					uni.navigateTo({
						url: '/pages/index/index'
					});
					break;
				case 'ingredients':
					uni.navigateTo({
						url: '/pages/ingredients/ingredients'
					});
					break;
				case 'recipe':
					uni.navigateTo({
						url: '/pages/recipe/recipe'
					});
					break;
				case 'profile':
					// 当前页面
					break;
			}
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 用户信息头部 */
.user-header {
	background: linear-gradient(135deg, #4CAF50, #66BB6A);
	padding: 60rpx 40rpx 40rpx;
	display: flex;
	align-items: center;
	gap: 30rpx;
	cursor: pointer;
}

.user-header:active {
	opacity: 0.9;
}

.user-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	overflow: hidden;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-avatar image {
	width: 100%;
	height: 100%;
}

.avatar-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 48rpx;
	color: white;
	font-weight: bold;
}

.user-info {
	flex: 1;
}

.user-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #fff;
	display: block;
	margin-bottom: 10rpx;
}

.user-desc {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
}

.user-stats {
	display: flex;
	gap: 30rpx;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.stat-number {
	font-size: 32rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 5rpx;
}

.stat-label {
	font-size: 22rpx;
	color: rgba(255, 255, 255, 0.8);
}

.login-prompt {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 40rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 30rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.login-text {
	color: white;
	font-size: 28rpx;
	font-weight: 500;
}

/* 功能菜单 */
.menu-section {
	margin-top: 20rpx;
}

.menu-group {
	background-color: #fff;
	margin-bottom: 20rpx;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-icon {
	font-size: 36rpx;
	margin-right: 30rpx;
}

.menu-text {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.menu-arrow {
	font-size: 28rpx;
	color: #ccc;
}

/* 退出登录按钮样式 */
.logout-item {
	margin-top: 20rpx;
	border-top: 1rpx solid #f0f0f0;
	padding-top: 30rpx;
}

.logout-icon {
	color: #ff4757;
}

.logout-text {
	color: #ff4757;
	font-weight: 500;
}

.logout-item:active {
	background-color: #fff5f5;
}

/* 最近收藏 */
.recent-section {
	background-color: #fff;
	margin-top: 20rpx;
	padding: 40rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.section-more {
	font-size: 26rpx;
	color: #4CAF50;
}

.recent-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.recent-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.recent-image {
	width: 80rpx;
	height: 80rpx;
	border-radius: 10rpx;
}

.recent-info {
	flex: 1;
}

.recent-title {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.recent-type {
	font-size: 24rpx;
	color: #666;
}

/* 底部导航 */
.bottom-nav {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-around;
	border-top: 1rpx solid #eee;
	z-index: 999;
}

.nav-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
}

.nav-item.active .nav-text {
	color: #4CAF50;
}

.nav-item.add-btn {
	position: relative;
}

.nav-icon {
	font-size: 40rpx;
	margin-bottom: 8rpx;
}

.add-icon {
	background-color: #4CAF50;
	color: #fff;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 50rpx;
	font-weight: bold;
}

.nav-text {
	font-size: 20rpx;
	color: #666;
}
</style>
