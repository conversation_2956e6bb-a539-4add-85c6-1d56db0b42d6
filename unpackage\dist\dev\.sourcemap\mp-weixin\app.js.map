{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n\r\n\t/* 全局动画 */\r\n\t.fade-in {\r\n\t\tanimation: fadeIn 0.3s ease-in-out;\r\n\t}\r\n\r\n\t@keyframes fadeIn {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(20rpx);\r\n\t\t}\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t.scale-in {\r\n\t\tanimation: scaleIn 0.2s ease-out;\r\n\t}\r\n\r\n\t@keyframes scaleIn {\r\n\t\tfrom {\r\n\t\t\ttransform: scale(0.9);\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\t\tto {\r\n\t\t\ttransform: scale(1);\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n\r\n\t/* 全局按钮样式 */\r\n\t.btn-hover {\r\n\t\ttransition: all 0.2s ease;\r\n\t}\r\n\r\n\t.btn-hover:active {\r\n\t\ttransform: scale(0.95);\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t/* 全局卡片样式 */\r\n\t.card-hover {\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.card-hover:active {\r\n\t\ttransform: translateY(2rpx);\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t/* 全局文字样式 */\r\n\t.text-primary {\r\n\t\tcolor: #4CAF50;\r\n\t}\r\n\r\n\t.text-secondary {\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.text-muted {\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t/* 全局间距 */\r\n\t.mt-10 { margin-top: 10rpx; }\r\n\t.mt-20 { margin-top: 20rpx; }\r\n\t.mt-30 { margin-top: 30rpx; }\r\n\t.mb-10 { margin-bottom: 10rpx; }\r\n\t.mb-20 { margin-bottom: 20rpx; }\r\n\t.mb-30 { margin-bottom: 30rpx; }\r\n\t.p-20 { padding: 20rpx; }\r\n\t.p-30 { padding: 30rpx; }\r\n\t.p-40 { padding: 40rpx; }\r\n</style>\r\n", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nexport function createApp() {\n  const app = createSSRApp(App)\n  return {\n    app\n  }\n}\n// #endif"], "names": ["uni", "createSSRApp", "App"], "mappings": ";;;;;;;;;;;;;AACC,MAAK,YAAU;AAAA,EACd,UAAU,WAAW;AACpBA,kBAAAA,MAAA,MAAA,OAAA,gBAAY,YAAY;AAAA,EACxB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,gBAAA,UAAU;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACvB;AACD;ACIM,SAAS,YAAY;AAC1B,QAAM,MAAMC,cAAY,aAACC,SAAG;AAC5B,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}