{"version": 3, "file": "api.js", "sources": ["utils/api.js"], "sourcesContent": ["// API配置文件\r\nimport config from './config.js';\r\n\r\nconst BASE_URL = config.baseURL;\r\nconsole.log('API BASE_URL:', BASE_URL);\r\n\r\n// 通用请求方法\r\nconst request = (options) => {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tconst fullUrl = BASE_URL + options.url;\r\n\t\tconsole.log('发起请求:', fullUrl);\r\n\t\tconsole.log('请求参数:', options);\r\n\r\n\t\tuni.request({\r\n\t\t\turl: fullUrl,\r\n\t\t\tmethod: options.method || 'GET',\r\n\t\t\tdata: options.data || {},\r\n\t\t\theader: {\r\n\t\t\t\t'Content-Type': 'application/json',\r\n\t\t\t\t'Accept': 'application/json',\r\n\t\t\t\t...options.header\r\n\t\t\t},\r\n\t\t\ttimeout: 15000, // 15秒超时\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tconsole.log('请求成功:', res);\r\n\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\tresolve(res.data);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.error('请求失败:', res);\r\n\t\t\t\t\treject(new Error(`请求失败: ${res.statusCode} - ${res.errMsg || '未知错误'}`));\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfail: (err) => {\r\n\t\t\t\tconsole.error('请求错误:', err);\r\n\t\t\t\treject(new Error(`网络错误: ${err.errMsg || '请求失败'}`));\r\n\t\t\t}\r\n\t\t});\r\n\t});\r\n};\r\n\r\n// API接口定义\r\nconst api = {\r\n\t// 获取药材食材列表\r\n\tgetMedicinalList() {\r\n\t\treturn request({\r\n\t\t\turl: '/medicinal/medicinalList',\r\n\t\t\tmethod: 'GET'\r\n\t\t});\r\n\t},\r\n\r\n\t// 根据ID获取药材详情\r\n\tgetMedicinalById(id) {\r\n\t\treturn request({\r\n\t\t\turl: `/medicinal/medicinalById?id=${id}`,\r\n\t\t\tmethod: 'GET'\r\n\t\t});\r\n\t},\r\n\r\n\t// 获取药材食材分类列表\r\n\tgetTypeList() {\r\n\t\treturn request({\r\n\t\t\turl: '/medicinal/TypeList',\r\n\t\t\tmethod: 'GET'\r\n\t\t});\r\n\t},\r\n\r\n\t// 根据分类ID获取药材列表\r\n\tgetMedicinalByTypeId(typeId) {\r\n\t\treturn request({\r\n\t\t\turl: `/medicinal/medicinalTypeId?id=${typeId}`,\r\n\t\t\tmethod: 'GET'\r\n\t\t});\r\n\t},\r\n\r\n\t// 根据名称搜索药材\r\n\tgetMedicinalByName(name) {\r\n\t\tconsole.log('API调用 - 搜索药材，原始关键词:', name);\r\n\t\tconst encodedName = encodeURIComponent(name);\r\n\t\tconsole.log('API调用 - 编码后的关键词:', encodedName);\r\n\t\tconst url = `/medicinal/medicinalByName?name=${encodedName}`;\r\n\t\tconsole.log('API调用 - 完整URL:', url);\r\n\r\n\t\treturn request({\r\n\t\t\turl: url,\r\n\t\t\tmethod: 'GET'\r\n\t\t});\r\n\t},\r\n\r\n\t// 获取轮播图列表\r\n\tgetCarouselList() {\r\n\t\treturn request({\r\n\t\t\turl: '/carousel/list',\r\n\t\t\tmethod: 'GET'\r\n\t\t});\r\n\t}\r\n};\r\n\r\nexport default api;\r\n"], "names": ["config", "uni"], "mappings": ";;;AAGA,MAAM,WAAWA,aAAM,OAAC;AACxBC,cAAY,MAAA,MAAA,OAAA,qBAAA,iBAAiB,QAAQ;AAGrC,MAAM,UAAU,CAAC,YAAY;AAC5B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,UAAM,UAAU,WAAW,QAAQ;AACnCA,2DAAY,SAAS,OAAO;AAC5BA,2DAAY,SAAS,OAAO;AAE5BA,kBAAAA,MAAI,QAAQ;AAAA,MACX,KAAK;AAAA,MACL,QAAQ,QAAQ,UAAU;AAAA,MAC1B,MAAM,QAAQ,QAAQ,CAAE;AAAA,MACxB,QAAQ;AAAA,QACP,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,GAAG,QAAQ;AAAA,MACX;AAAA,MACD,SAAS;AAAA;AAAA,MACT,SAAS,CAAC,QAAQ;AACjBA,+DAAY,SAAS,GAAG;AACxB,YAAI,IAAI,eAAe,KAAK;AAC3B,kBAAQ,IAAI,IAAI;AAAA,QACrB,OAAW;AACNA,wBAAc,MAAA,MAAA,SAAA,sBAAA,SAAS,GAAG;AAC1B,iBAAO,IAAI,MAAM,SAAS,IAAI,UAAU,MAAM,IAAI,UAAU,MAAM,EAAE,CAAC;AAAA,QACrE;AAAA,MACD;AAAA,MACD,MAAM,CAAC,QAAQ;AACdA,iEAAc,SAAS,GAAG;AAC1B,eAAO,IAAI,MAAM,SAAS,IAAI,UAAU,MAAM,EAAE,CAAC;AAAA,MACjD;AAAA,IACJ,CAAG;AAAA,EACH,CAAE;AACF;AAGK,MAAC,MAAM;AAAA;AAAA,EAEX,mBAAmB;AAClB,WAAO,QAAQ;AAAA,MACd,KAAK;AAAA,MACL,QAAQ;AAAA,IACX,CAAG;AAAA,EACD;AAAA;AAAA,EAGD,iBAAiB,IAAI;AACpB,WAAO,QAAQ;AAAA,MACd,KAAK,+BAA+B,EAAE;AAAA,MACtC,QAAQ;AAAA,IACX,CAAG;AAAA,EACD;AAAA;AAAA,EAGD,cAAc;AACb,WAAO,QAAQ;AAAA,MACd,KAAK;AAAA,MACL,QAAQ;AAAA,IACX,CAAG;AAAA,EACD;AAAA;AAAA,EAGD,qBAAqB,QAAQ;AAC5B,WAAO,QAAQ;AAAA,MACd,KAAK,iCAAiC,MAAM;AAAA,MAC5C,QAAQ;AAAA,IACX,CAAG;AAAA,EACD;AAAA;AAAA,EAGD,mBAAmB,MAAM;AACxBA,kBAAA,MAAA,MAAA,OAAA,sBAAY,uBAAuB,IAAI;AACvC,UAAM,cAAc,mBAAmB,IAAI;AAC3CA,kBAAA,MAAA,MAAA,OAAA,sBAAY,oBAAoB,WAAW;AAC3C,UAAM,MAAM,mCAAmC,WAAW;AAC1DA,kBAAY,MAAA,MAAA,OAAA,sBAAA,kBAAkB,GAAG;AAEjC,WAAO,QAAQ;AAAA,MACd;AAAA,MACA,QAAQ;AAAA,IACX,CAAG;AAAA,EACD;AAAA;AAAA,EAGD,kBAAkB;AACjB,WAAO,QAAQ;AAAA,MACd,KAAK;AAAA,MACL,QAAQ;AAAA,IACX,CAAG;AAAA,EACD;AACF;;"}