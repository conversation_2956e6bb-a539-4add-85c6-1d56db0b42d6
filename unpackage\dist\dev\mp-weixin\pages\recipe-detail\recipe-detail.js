"use strict";
const common_vendor = require("../../common/vendor.js");
const api_recipe = require("../../api/recipe.js");
const _sfc_main = {
  data() {
    return {
      recipeId: "",
      recipeDetail: {
        id: null,
        title: "",
        suitableFor: "",
        description: "",
        effects: "",
        method: "",
        precautions: "",
        imageUrl: "/static/logo.png",
        doctor: {
          name: "",
          title: "",
          avatar: "/static/logo.png"
        },
        rating: 0,
        viewCount: 0,
        isFavorite: false
      },
      loading: false,
      userId: 2
      // 临时用户ID，实际应该从登录状态获取
    };
  },
  onLoad(options) {
    if (options.id) {
      this.recipeId = options.id;
      this.loadRecipeDetail();
    }
  },
  methods: {
    /**
     * 加载食疗方案详情
     */
    async loadRecipeDetail() {
      if (this.loading)
        return;
      this.loading = true;
      try {
        const response = await api_recipe.getRecipeDetail(this.recipeId);
        if (response.code === 200 && response.data) {
          common_vendor.index.__f__("log", "at pages/recipe-detail/recipe-detail.vue:125", "获取到的详情数据:", response.data);
          common_vendor.index.__f__("log", "at pages/recipe-detail/recipe-detail.vue:126", "图片URL:", response.data.imageUrl);
          this.recipeDetail = {
            id: response.data.id,
            title: response.data.title || "",
            suitableFor: response.data.suitableFor || "",
            description: response.data.description || "",
            effects: response.data.efficacy || "",
            // 使用功效字段
            method: response.data.steps || "",
            // 使用制作步骤字段
            precautions: "请根据个人体质和医生建议使用",
            // 注意事项
            imageUrl: response.data.imageUrl || "/static/logo.png",
            doctor: response.data.doctor || {
              name: "",
              title: "",
              avatar: "/static/logo.png"
            },
            rating: response.data.rating || 0,
            viewCount: response.data.viewCount || 0,
            isFavorite: response.data.isFavorite || false
          };
          if (this.recipeDetail.imageUrl && this.recipeDetail.imageUrl !== "/static/logo.png") {
            this.testImageUrl(this.recipeDetail.imageUrl);
          }
          common_vendor.index.__f__("log", "at pages/recipe-detail/recipe-detail.vue:153", "设置后的详情数据:", this.recipeDetail);
          common_vendor.index.__f__("log", "at pages/recipe-detail/recipe-detail.vue:154", "最终图片URL:", this.recipeDetail.imageUrl);
        } else {
          common_vendor.index.showToast({
            title: response.message || "获取详情失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/recipe-detail/recipe-detail.vue:162", "获取食疗方案详情失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请稍后重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    /**
     * 图片加载错误处理
     */
    onImageError(e) {
      common_vendor.index.__f__("error", "at pages/recipe-detail/recipe-detail.vue:176", "图片加载失败:", e);
      common_vendor.index.__f__("log", "at pages/recipe-detail/recipe-detail.vue:177", "当前图片URL:", this.recipeDetail.imageUrl);
      this.recipeDetail.imageUrl = "/static/logo.png";
    },
    /**
     * 图片加载成功处理
     */
    onImageLoad(e) {
      common_vendor.index.__f__("log", "at pages/recipe-detail/recipe-detail.vue:186", "图片加载成功:", e);
      common_vendor.index.__f__("log", "at pages/recipe-detail/recipe-detail.vue:187", "图片URL:", this.recipeDetail.imageUrl);
    },
    /**
     * 测试图片URL是否可访问
     */
    testImageUrl(url) {
      common_vendor.index.__f__("log", "at pages/recipe-detail/recipe-detail.vue:194", "测试图片URL:", url);
      common_vendor.index.request({
        url,
        method: "HEAD",
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/recipe-detail/recipe-detail.vue:199", "图片URL测试成功:", res);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/recipe-detail/recipe-detail.vue:202", "图片URL测试失败:", err);
          this.recipeDetail.imageUrl = "/static/logo.png";
        }
      });
    },
    /**
     * 切换收藏状态
     */
    async toggleFavorite() {
      try {
        const response = await api_recipe.toggleFavorite({
          planId: this.recipeDetail.id,
          userId: this.userId
        });
        if (response.code === 200) {
          this.recipeDetail.isFavorite = response.data.isFavorite;
          common_vendor.index.showToast({
            title: response.data.isFavorite ? "已收藏" : "已取消收藏",
            icon: "none",
            duration: 1500
          });
        } else {
          common_vendor.index.showToast({
            title: response.message || "操作失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/recipe-detail/recipe-detail.vue:235", "收藏操作失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请稍后重试",
          icon: "none"
        });
      }
    },
    // 分享方案
    shareRecipe() {
      common_vendor.index.showActionSheet({
        itemList: ["分享到微信", "分享到朋友圈", "复制链接"],
        success: (res) => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    },
    // 开始制作
    startCooking() {
      common_vendor.index.showToast({
        title: "开始制作指导",
        icon: "success"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    b: $data.recipeDetail.imageUrl,
    c: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args)),
    d: common_vendor.o((...args) => $options.onImageLoad && $options.onImageLoad(...args)),
    e: common_vendor.n($data.recipeDetail.isFavorite ? "favorited" : ""),
    f: common_vendor.o((...args) => $options.toggleFavorite && $options.toggleFavorite(...args)),
    g: common_vendor.t($data.recipeDetail.title),
    h: common_vendor.t($data.recipeDetail.suitableFor),
    i: $data.recipeDetail.doctor && $data.recipeDetail.doctor.name
  }, $data.recipeDetail.doctor && $data.recipeDetail.doctor.name ? {
    j: $data.recipeDetail.doctor.avatar || "/static/logo.png",
    k: common_vendor.t($data.recipeDetail.doctor.name),
    l: common_vendor.t($data.recipeDetail.doctor.title || "中医师"),
    m: common_vendor.t($data.recipeDetail.rating || 0),
    n: common_vendor.t($data.recipeDetail.viewCount || 0)
  } : {}, {
    o: common_vendor.t($data.recipeDetail.description),
    p: common_vendor.t($data.recipeDetail.effects),
    q: common_vendor.t($data.recipeDetail.method),
    r: common_vendor.t($data.recipeDetail.precautions),
    s: common_vendor.o((...args) => $options.shareRecipe && $options.shareRecipe(...args)),
    t: common_vendor.o((...args) => $options.startCooking && $options.startCooking(...args))
  }));
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fc6387aa"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/recipe-detail/recipe-detail.js.map
