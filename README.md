# 中医食疗 uni-app 应用

一个基于 uni-app 开发的中医食疗应用，提供专业的食疗方案和中药材知识。

## 功能特色

### 🍲 食疗方案
- **专业配方**：由专业中医师提供的食疗方案
- **详细介绍**：包含功效、制作方法、注意事项等完整信息
- **智能搜索**：支持按名称、功效、适用人群搜索
- **分类筛选**：补益类、清热类、调理类、美容类等分类
- **收藏功能**：收藏喜欢的食疗方案

### 🌿 药材食材
- **丰富资源**：涵盖常用中药材和药食同源食材
- **详细信息**：包含别名、功效、性味、使用方法等
- **分类管理**：中药材、食材、补益类等多维度分类
- **相关推荐**：推荐相关的食疗方案

### 👤 个人中心
- **用户管理**：个人信息和统计数据
- **收藏管理**：管理收藏的方案和药材
- **浏览历史**：查看最近浏览的内容
- **设置中心**：应用设置和帮助信息

## 技术特点

### 🎨 现代化设计
- **卡片式布局**：清晰的信息层次和视觉效果
- **绿色主题**：符合健康养生的色彩搭配
- **响应式设计**：适配不同屏幕尺寸
- **动画效果**：流畅的页面切换和交互动画

### 🔧 技术架构
- **框架**：uni-app (Vue 3)
- **样式**：SCSS + 原生CSS
- **组件化**：模块化的页面和组件设计
- **数据管理**：响应式数据绑定和计算属性

## 页面结构

```
pages/
├── index/                 # 首页
│   └── index.vue
├── recipe/                # 食疗方案列表
│   └── recipe.vue
├── recipe-detail/         # 食疗方案详情
│   └── recipe-detail.vue
├── ingredients/           # 药材食材列表
│   └── ingredients.vue
├── ingredient-detail/     # 药材详情
│   └── ingredient-detail.vue
└── profile/               # 个人中心
    └── profile.vue
```

## 核心功能

### 搜索和筛选
- 实时搜索功能
- 多维度筛选条件
- 智能匹配算法

### 收藏系统
- 一键收藏/取消收藏
- 收藏列表管理
- 收藏状态同步

### 导航系统
- 底部导航栏
- 页面间流畅跳转
- 面包屑导航

## 数据结构

### 食疗方案
```javascript
{
  id: Number,           // 方案ID
  title: String,        // 方案名称
  suitableFor: String,  // 适用人群
  description: String,  // 详细描述
  category: String,     // 分类
  tags: Array,          // 标签
  doctor: Object,       // 医生信息
  rating: Number,       // 评分
  viewCount: Number,    // 浏览量
  isFavorite: Boolean   // 收藏状态
}
```

### 药材食材
```javascript
{
  id: Number,           // 药材ID
  name: String,         // 药材名称
  alias: String,        // 别名
  mainEffect: String,   // 主要功效
  type: String,         // 类型
  nature: String,       // 性味
  category: String,     // 分类
  tags: Array,          // 标签
  description: String   // 详细描述
}
```

## 开发指南

### 环境要求
- Node.js 14+
- HBuilderX 或 VS Code
- uni-app 开发环境

### 安装运行
1. 克隆项目到本地
2. 使用 HBuilderX 打开项目
3. 选择运行到浏览器或模拟器
4. 开始开发调试

### 自定义配置
- 修改 `uni.scss` 调整全局样式变量
- 更新 `static/` 目录中的图片资源
- 在各页面的 data 中修改模拟数据

## 后续优化

### 功能扩展
- [ ] 用户登录注册系统
- [ ] 评论和评分功能
- [ ] 社区交流模块
- [ ] 个性化推荐算法
- [ ] 离线缓存功能

### 性能优化
- [ ] 图片懒加载
- [ ] 数据分页加载
- [ ] 组件按需加载
- [ ] 缓存策略优化

### 体验优化
- [ ] 更多动画效果
- [ ] 手势操作支持
- [ ] 语音搜索功能
- [ ] 深色模式支持

## 许可证

MIT License

## 联系方式

如有问题或建议，欢迎联系开发团队。
