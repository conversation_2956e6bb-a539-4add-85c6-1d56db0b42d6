<template>
	<view class="container">
		<!-- 页面标题 -->
		<view class="header">
			<text class="header-title">食疗方案</text>
			<text class="header-subtitle">更多方案 ></text>
		</view>

		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input class="search-input" placeholder="搜索食疗方案" v-model="searchKeyword" @input="onSearch" />
			</view>
		</view>

		<!-- 分类筛选 -->
		<view class="filter-section">
			<scroll-view class="filter-scroll" scroll-x="true">
				<view class="filter-list">
					<view
						class="filter-item"
						:class="activeFilter === item.id ? 'active' : ''"
						v-for="item in filterList"
						:key="item.id"
						@click="selectFilter(item.id)"
					>
						<text class="filter-text">{{ item.name }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 食疗方案列表 -->
		<view class="recipe-list">
			<!-- 加载状态 -->
			<view v-if="loading && recipeList.length === 0" class="loading-container">
				<text class="loading-text">加载中...</text>
			</view>
			
			<!-- 空状态 -->
			<view v-else-if="!loading && recipeList.length === 0" class="empty-container">
				<text class="empty-text">暂无食疗方案</text>
			</view>
			
			<!-- 方案列表 -->
			<view v-else>
				<view class="recipe-card fade-in card-hover" v-for="(item, index) in filteredRecipes" :key="item.id" @click="goToDetail(item)" :style="{ 'animation-delay': (index * 0.1) + 's' }">
					<!-- 食疗方案图片 -->
					<image class="recipe-image" :src="item.imageUrl || '/static/logo.png'" mode="aspectFill"></image>
					
					<!-- 收藏按钮 -->
					<view class="favorite-btn btn-hover scale-in" @click.stop="toggleFavorite(item, index)">
						<text class="favorite-icon" :class="item.isFavorite ? 'favorited' : ''">♥</text>
					</view>
					
					<!-- 食疗方案信息 -->
					<view class="recipe-info">
						<text class="recipe-title">{{ item.title }}</text>
						<text class="recipe-suitable">适用人群：{{ item.suitableFor }}</text>
						<text class="recipe-description">{{ item.description }}</text>
						
						<!-- 医生信息 -->
						<view class="doctor-section" v-if="item.doctor">
							<view class="doctor-avatar">
								<image :src="item.doctor.avatar || '/static/logo.png'" mode="aspectFill"></image>
							</view>
							<text class="doctor-name">{{ item.doctor.name }}</text>
						</view>
						
						<!-- 方案统计信息 -->
						<view class="stats-info">
							<view class="rating">
								<text class="star">★</text>
								<text class="rating-score">{{ item.rating || 4.5 }}</text>
							</view>
							<view class="view-count">
								<text class="view-icon">👁</text>
								<text class="view-number">{{ item.viewCount || 0 }}</text>
							</view>
							<view class="like-count" @click.stop="toggleRecipeLike(item, index)">
								<text class="like-icon" :class="item.isLiked ? 'liked' : ''">❤️</text>
								<text class="like-number">{{ item.likeCount || 0 }}</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 加载更多 -->
				<view v-if="loading && recipeList.length > 0" class="loading-more">
					<text class="loading-text">加载更多...</text>
				</view>
				
				<!-- 没有更多数据 -->
				<view v-if="!hasMore && recipeList.length > 0" class="no-more">
					<text class="no-more-text">没有更多数据了</text>
				</view>
			</view>
		</view>
		
		<!-- 底部导航 -->
		<view class="bottom-nav">
			<view class="nav-item" @click="goToPage('home')">
				<text class="nav-icon">🏠</text>
				<text class="nav-text">首页</text>
			</view>
			<view class="nav-item" @click="goToPage('ingredients')">
				<text class="nav-icon">🌿</text>
				<text class="nav-text">药材食材</text>
			</view>
			<view class="nav-item add-btn">
				<text class="nav-icon add-icon">+</text>
			</view>
			<view class="nav-item active" @click="goToPage('recipe')">
				<text class="nav-icon">📋</text>
				<text class="nav-text">食疗方案</text>
			</view>
			<view class="nav-item" @click="goToPage('profile')">
				<text class="nav-icon">👤</text>
				<text class="nav-text">我的</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getRecipeList, toggleFavorite, incrementViewCount, recordSearchLog, toggleLike, getBatchLikeStatus } from '../../api/recipe.js';

export default {
	data() {
		return {
			searchKeyword: '',
			activeFilter: 'all',
			filterList: [
				{ id: 'all', name: '全部' },
				{ id: 'tonify', name: '补益类' },
				{ id: 'clear', name: '清热类' },
				{ id: 'regulate', name: '调理类' },
				{ id: 'beauty', name: '美容类' },
				{ id: 'weight', name: '减肥类' }
			],
			recipeList: [], // 改为空数组，从后端获取数据
			loading: false, // 加载状态
			page: 1, // 当前页码
			size: 10, // 每页大小
			hasMore: true, // 是否还有更多数据
			userId: 2 // 临时用户ID，实际应该从登录状态获取
		}
	},
	computed: {
		filteredRecipes() {
			// 由于现在从后端获取数据，筛选逻辑移到后端处理
			return this.recipeList;
		}
	},
	/**
	 * 页面加载时获取数据
	 */
	async onLoad() {
		console.log('页面onLoad - 开始加载数据');
		await this.loadRecipeList();
	},
	
	/**
	 * 页面显示时刷新数据（如果需要）
	 */
	async onShow() {
		console.log('页面onShow - 页面显示');
		// 如果还没有数据，则加载数据
		if (this.recipeList.length === 0 && !this.loading) {
			console.log('页面onShow - 数据为空，重新加载');
			await this.loadRecipeList();
		}
	},
	methods: {
		
		/**
		 * 加载食疗方案列表
		 */
		async loadRecipeList(isRefresh = true) {
			if (this.loading) return;
			
			console.log('loadRecipeList - 开始加载', { isRefresh, activeFilter: this.activeFilter, searchKeyword: this.searchKeyword });
			
			this.loading = true;
			
			try {
				// 如果是刷新，重置页码
				if (isRefresh) {
					this.page = 1;
					this.recipeList = [];
				}
				
				const params = {
					page: this.page,
					size: this.size,
					userId: this.userId
				};
				
				// 添加搜索关键词
				if (this.searchKeyword) {
					params.keyword = this.searchKeyword;
				}
				
				// 添加分类筛选
				if (this.activeFilter !== 'all') {
					params.category = this.activeFilter;
				}
				
				console.log('loadRecipeList - 请求参数', params);
				
				const response = await getRecipeList(params);
				
				console.log('loadRecipeList - 响应结果', response);
				
				if (response.code === 200 && response.data) {
					const newData = response.data.records || [];
					
					if (isRefresh) {
						this.recipeList = newData;
					} else {
						this.recipeList = [...this.recipeList, ...newData];
					}
					
					// 判断是否还有更多数据
					this.hasMore = newData.length === this.size;
					
					// 批量获取点赞状态
					await this.loadLikeStatus();
					
					// 记录搜索日志
					if (this.searchKeyword) {
						this.recordSearchLog();
					}
				} else {
					uni.showToast({
						title: response.message || '获取数据失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取食疗方案列表失败:', error);
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		/**
		 * 搜索功能
		 */
		async onSearch() {
			// 重置页码并重新加载数据
			this.page = 1;
			await this.loadRecipeList(true);
		},

		/**
		 * 选择筛选条件
		 */
		async selectFilter(filterId) {
			this.activeFilter = filterId;
			// 重置页码并重新加载数据
			this.page = 1;
			await this.loadRecipeList(true);
		},
		
		/**
		 * 切换收藏状态
		 */
		async toggleFavorite(item, index) {
			try {
				const response = await toggleFavorite({
					planId: item.id,
					userId: this.userId
				});
				
				if (response.code === 200) {
					// 更新本地状态
					this.recipeList[index].isFavorite = response.data.isFavorite;
					
					uni.showToast({
						title: response.data.isFavorite ? '已收藏' : '已取消收藏',
						icon: 'none',
						duration: 1500
					});
				} else {
					uni.showToast({
						title: response.message || '操作失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('收藏操作失败:', error);
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				});
			}
		},
		
		/**
		 * 跳转到详情页
		 */
		async goToDetail(item) {
			try {
				// 增加浏览量（每个用户只能贡献1次）
				const response = await incrementViewCount(item.id, this.userId);
				
				if (response && response.code === 200) {
					console.log('浏览量统计结果:', response.data);
					if (response.data.isFirstView) {
						console.log('首次浏览，浏览量+1');
					} else {
						console.log('已浏览过，不重复计算');
					}
				}
			} catch (error) {
				console.error('增加浏览量失败:', error);
			}
			
			// 跳转到详情页
			uni.navigateTo({
				url: `/pages/recipe-detail/recipe-detail?id=${item.id}`
			});
		},
		
		/**
		 * 记录搜索日志
		 */
		async recordSearchLog() {
			try {
				await recordSearchLog({
					userId: this.userId,
					queryText: this.searchKeyword,
					resultCount: this.recipeList.length
				});
			} catch (error) {
				console.error('记录搜索日志失败:', error);
			}
		},
		
		/**
		 * 下拉刷新
		 */
		async onPullDownRefresh() {
			await this.loadRecipeList(true);
			uni.stopPullDownRefresh();
		},
		
		/**
		 * 上拉加载更多
		 */
		async onReachBottom() {
			if (this.hasMore && !this.loading) {
				this.page++;
				await this.loadRecipeList(false);
			}
		},
		
		/**
		 * 点赞/取消点赞食疗方案
		 */
		async toggleRecipeLike(item, index) {
			try {
				// 防止快速点击
				if (item.liking) return;
				item.liking = true;
				
				const response = await toggleLike(item.id, this.userId);
				
				if (response.code === 200) {
					// 更新本地状态
					this.recipeList[index].isLiked = response.data.isLiked;
					this.recipeList[index].likeCount = response.data.likeCount;
					
					uni.showToast({
						title: response.data.message || (response.data.isLiked ? '点赞成功' : '取消点赞'),
						icon: 'none',
						duration: 1500
					});
				} else {
					uni.showToast({
						title: response.message || '操作失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('点赞操作失败:', error);
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				});
			} finally {
				item.liking = false;
			}
		},
		
		/**
		 * 批量获取点赞状态
		 */
		async loadLikeStatus() {
			try {
				if (this.recipeList.length === 0) return;
				
				const planIds = this.recipeList.map(item => item.id);
				const response = await getBatchLikeStatus(this.userId, planIds);
				
				if (response.code === 200) {
					// 更新点赞状态
					this.recipeList.forEach(item => {
						const likeStatus = response.data[item.id.toString()];
						if (likeStatus !== undefined) {
							item.isLiked = likeStatus;
						}
					});
				}
			} catch (error) {
				console.error('获取点赞状态失败:', error);
			}
		},
		
		/**
		 * 导航跳转
		 */
		goToPage(page) {
			switch(page) {
				case 'home':
					uni.navigateTo({
						url: '/pages/index/index'
					});
					break;
				case 'ingredients':
					uni.navigateTo({
						url: '/pages/ingredients/ingredients'
					});
					break;
				case 'recipe':
					// 当前页面
					break;
				case 'profile':
					uni.navigateTo({
						url: '/pages/profile/profile'
					});
					break;
			}
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 页面头部 */
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx 20rpx;
	background-color: #fff;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.header-subtitle {
	font-size: 28rpx;
	color: #4CAF50;
}

/* 搜索栏 */
.search-section {
	background-color: #fff;
	padding: 30rpx 40rpx;
}

.search-box {
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
}

.search-icon {
	font-size: 32rpx;
	color: #999;
	margin-right: 20rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

/* 筛选标签 */
.filter-section {
	background-color: #fff;
	padding: 20rpx 0;
	margin-bottom: 20rpx;
}

.filter-scroll {
	white-space: nowrap;
}

.filter-list {
	display: flex;
	padding: 0 40rpx;
}

.filter-item {
	flex-shrink: 0;
	padding: 15rpx 30rpx;
	margin-right: 20rpx;
	background-color: #f5f5f5;
	border-radius: 40rpx;
}

.filter-item.active {
	background-color: #4CAF50;
}

.filter-text {
	font-size: 26rpx;
	color: #666;
}

.filter-item.active .filter-text {
	color: #fff;
}

/* 食疗方案列表 */
.recipe-list {
	padding: 20rpx;
}

.recipe-card {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	position: relative;
}

.recipe-image {
	width: 100%;
	height: 400rpx;
}

.favorite-btn {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	width: 60rpx;
	height: 60rpx;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.favorite-icon {
	font-size: 32rpx;
	color: #ccc;
}

.favorite-icon.favorited {
	color: #4CAF50;
}

.recipe-info {
	padding: 30rpx;
}

.recipe-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}

.recipe-suitable {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.recipe-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	display: block;
	margin-bottom: 25rpx;
}

/* 医生信息区域 */
.doctor-section {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 15rpx;
}

.doctor-avatar {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	overflow: hidden;
}

.doctor-avatar image {
	width: 100%;
	height: 100%;
}

.doctor-name {
	font-size: 24rpx;
	color: #666;
}

/* 统计信息区域 */
.stats-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.rating {
	display: flex;
	align-items: center;
}

.star {
	color: #FFD700;
	font-size: 24rpx;
	margin-right: 5rpx;
}

.rating-score {
	font-size: 24rpx;
	color: #666;
}

.view-count {
	display: flex;
	align-items: center;
}

.view-icon {
	font-size: 24rpx;
	margin-right: 5rpx;
}

.view-number {
	font-size: 24rpx;
	color: #666;
}

.like-count {
	display: flex;
	align-items: center;
	cursor: pointer;
}

.like-icon {
	font-size: 24rpx;
	margin-right: 5rpx;
	color: #ccc;
	transition: all 0.3s ease;
}

.like-icon.liked {
	color: #ff4757;
	transform: scale(1.2);
}

.like-number {
	font-size: 24rpx;
	color: #666;
}

/* 底部导航 */
.bottom-nav {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-around;
	border-top: 1rpx solid #eee;
	z-index: 999;
}

.nav-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
}

.nav-item.active .nav-text {
	color: #4CAF50;
}

.nav-item.add-btn {
	position: relative;
}

.nav-icon {
	font-size: 40rpx;
	margin-bottom: 8rpx;
}

.add-icon {
	background-color: #4CAF50;
	color: #fff;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 50rpx;
	font-weight: bold;
}

.nav-text {
	font-size: 20rpx;
	color: #666;
}

/* 加载状态样式 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

/* 空状态样式 */
.empty-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

/* 加载更多样式 */
.loading-more {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 30rpx 0;
}

.no-more {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 30rpx 0;
}

.no-more-text {
	font-size: 24rpx;
	color: #ccc;
}
</style>
