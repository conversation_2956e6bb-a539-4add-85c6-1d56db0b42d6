"use strict";
const utils_api = require("../../utils/api.js");
const utils_config = require("../../utils/config.js");
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      loading: false,
      result: "",
      logs: [],
      baseUrl: utils_config.config.baseURL,
      currentUrl: ""
    };
  },
  onLoad() {
    this.currentUrl = typeof window !== "undefined" && window.location ? window.location.href : "未知";
    this.addLog("页面加载完成");
    this.addLog(`BASE_URL: ${this.baseUrl}`);
    this.addLog(`当前页面: ${this.currentUrl}`);
  },
  methods: {
    addLog(message) {
      const timestamp = (/* @__PURE__ */ new Date()).toLocaleTimeString();
      this.logs.push(`[${timestamp}] ${message}`);
    },
    async testAPI() {
      this.loading = true;
      this.result = "";
      this.addLog("开始测试API...");
      try {
        this.addLog("调用 api.getMedicinalList()");
        const response = await utils_api.api.getMedicinalList();
        this.addLog("API调用成功");
        this.result = `成功！获取到 ${response.length} 条数据`;
        this.addLog(`响应数据: ${JSON.stringify(response.slice(0, 2))}`);
      } catch (error) {
        this.addLog("API调用失败");
        this.result = `失败：${error.message || error}`;
        this.addLog(`错误详情: ${JSON.stringify(error)}`);
      } finally {
        this.loading = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.baseUrl),
    b: common_vendor.t($data.currentUrl),
    c: common_vendor.t($data.loading ? "测试中..." : "测试API连接"),
    d: common_vendor.o((...args) => $options.testAPI && $options.testAPI(...args)),
    e: $data.loading,
    f: $data.result
  }, $data.result ? {
    g: common_vendor.t($data.result)
  } : {}, {
    h: $data.logs.length > 0
  }, $data.logs.length > 0 ? {
    i: common_vendor.f($data.logs, (log, index, i0) => {
      return {
        a: common_vendor.t(log),
        b: index
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-727d09f0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/test.js.map
