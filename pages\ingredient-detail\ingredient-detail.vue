<template>
	<view class="container">
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<text class="loading-text">正在加载药材详情...</text>
		</view>

		<!-- 药材头部信息 -->
		<view v-else class="ingredient-header">
			<!-- 显示图片，如果没有图片或加载失败则显示图标 -->
			<view v-if="showImage && ingredientDetail.avatar" class="ingredient-image-container">
				<image class="ingredient-image" :src="ingredientDetail.avatar" mode="aspectFill" @error="onImageError" @load="onImageLoad"></image>
			</view>
			<view v-else class="ingredient-icon">
				<text class="icon-text">{{ getIconText(ingredientDetail.name) }}</text>
			</view>
			<view class="header-info">
				<text class="ingredient-name">{{ ingredientDetail.name || '未知药材' }}</text>
				<text class="ingredient-alias">别名：{{ ingredientDetail.rname || '无别名' }}</text>
				<view class="ingredient-tags">
					<text class="tag" v-for="tag in parsedTags" :key="tag">{{ tag }}</text>
				</view>
			</view>
			<view class="ingredient-meta">
				<view class="meta-item">
					<text class="meta-label">类型</text>
					<text class="meta-value">{{ ingredientDetail.name1 || '中药材' }}</text>
				</view>
				<view class="meta-item">
					<text class="meta-label">性味</text>
					<text class="meta-value">{{ formatNature(ingredientDetail.meridian) }}</text>
				</view>
			</view>
		</view>
		
		<!-- 详细信息 -->
		<view v-if="!loading" class="detail-content">
			<view class="section">
				<text class="section-title">主要功效</text>
				<text class="section-content">{{ ingredientDetail.efficacy || '功效信息待补充' }}</text>
			</view>

			<view class="section">
				<text class="section-title">详细介绍</text>
				<text class="section-content">{{ ingredientDetail.description || getDefaultDescription() }}</text>
			</view>

			<view class="section">
				<text class="section-title">适用人群</text>
				<text class="section-content">{{ ingredientDetail.applicablepopulation || getSuitableFor() }}</text>
			</view>

			<view class="section">
				<text class="section-title">使用方法</text>
				<text class="section-content">{{ ingredientDetail.dosage || getDefaultUsageMethod() }}</text>
			</view>

			<view class="section">
				<text class="section-title">注意事项</text>
				<text class="section-content">{{ ingredientDetail.taboo || getDefaultPrecautions() }}</text>
			</view>
			
			<!-- 相关食疗方案 -->
			<view class="section">
				<text class="section-title">相关食疗方案</text>
				<view class="related-recipes">
					<view class="recipe-item" v-for="recipe in ingredientDetail.relatedRecipes" :key="recipe.id" @click="goToRecipe(recipe.id)">
						<image class="recipe-thumb" :src="recipe.image" mode="aspectFill"></image>
						<view class="recipe-info">
							<text class="recipe-name">{{ recipe.name }}</text>
							<text class="recipe-effect">{{ recipe.effect }}</text>
						</view>
						<text class="arrow">></text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="addToCollection">加入收藏</button>
			<button class="action-btn primary" @click="viewRecipes">查看食疗方案</button>
		</view>
	</view>
</template>

<script>
import api from '@/utils/api.js';

export default {
	data() {
		return {
			ingredientId: '',
			loading: true,
			showImage: true, // 控制是否显示图片
			ingredientDetail: {}
		}
	},
	computed: {
		parsedTags() {
			return this.getTagsByEffect(this.ingredientDetail.efficacy || '');
		}
	},
	onLoad(options) {
		if (options.id) {
			this.ingredientId = options.id;
			this.loadIngredientDetail();
		}
	},
	methods: {
		// 加载药材详情
		async loadIngredientDetail() {
			try {
				this.loading = true;
				this.showImage = true; // 重置图片显示状态
				console.log('加载药材详情，ID:', this.ingredientId);

				const response = await api.getMedicinalById(this.ingredientId);
				console.log('药材详情API响应:', response);

				if (response && response.code === 200 && response.data) {
					this.ingredientDetail = response.data;
					console.log('药材详情加载成功:', this.ingredientDetail);
					console.log('dosage字段:', this.ingredientDetail.dosage);
					console.log('taboo字段:', this.ingredientDetail.taboo);
					console.log('applicablepopulation字段:', this.ingredientDetail.applicablepopulation);
					console.log('avatar图片字段:', this.ingredientDetail.avatar);
				} else {
					throw new Error('获取药材详情失败');
				}
			} catch (error) {
				console.error('加载药材详情失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 图片加载成功
		onImageLoad() {
			console.log('图片加载成功:', this.ingredientDetail.avatar);
		},

		// 图片加载错误处理
		onImageError() {
			console.log('图片加载失败，将显示图标:', this.ingredientDetail.avatar);
			// 图片加载失败时，设置showImage为false，这样会显示图标
			this.showImage = false;
		},

		// 获取图标文字
		getIconText(name) {
			if (!name) return '药';
			return name.charAt(0);
		},

		// 格式化性味
		formatNature(meridian) {
			if (!meridian) return '甘，微温';

			const cleanMeridian = meridian.replace(/\r\n/g, '').replace(/\n/g, '').trim();

			// 提取性味信息
			if (cleanMeridian.includes('甘') && cleanMeridian.includes('温')) return '甘，微温';
			if (cleanMeridian.includes('苦') && cleanMeridian.includes('寒')) return '苦，寒';
			if (cleanMeridian.includes('辛') && cleanMeridian.includes('温')) return '辛，温';
			if (cleanMeridian.includes('酸') && cleanMeridian.includes('平')) return '酸，平';
			if (cleanMeridian.includes('咸') && cleanMeridian.includes('寒')) return '咸，寒';

			return '甘，微温';
		},

		// 根据功效生成标签
		getTagsByEffect(effect) {
			if (!effect) return ['功效'];
			const tags = [];

			if (effect.includes('补气')) tags.push('补气');
			if (effect.includes('补血')) tags.push('补血');
			if (effect.includes('增强免疫') || effect.includes('免疫')) tags.push('增强免疫');
			if (effect.includes('抗疲劳') || effect.includes('疲劳')) tags.push('抗疲劳');
			if (effect.includes('利尿')) tags.push('利尿');

			if (tags.length === 0) {
				if (effect.includes('补') || effect.includes('益')) tags.push('补益');
				if (effect.includes('清') || effect.includes('热')) tags.push('清热');
				if (effect.includes('调') || effect.includes('理')) tags.push('调理');
			}

			return tags.slice(0, 4);
		},

		// 获取默认描述
		getDefaultDescription() {
			const name = this.ingredientDetail.name || '该药材';
			const efficacy = this.ingredientDetail.efficacy || '具有多种功效';

			return `${name}为常用中药材，${efficacy}。${name}味甘，性微温，归肺、脾经。常用于气虚乏力、食少便溏、中气下陷、久泻脱肛、便血崩漏、表虚自汗、气虚水肿、痈疽难溃、久溃不敛、血虚萎黄、内热消渴等症。`;
		},

		// 获取适用人群
		getSuitableFor() {
			const efficacy = this.ingredientDetail.efficacy || '';

			if (efficacy.includes('补气')) {
				return '适用于气虚体质、免疫力低下、容易感冒、疲劳乏力、食欲不振、消化不良、水肿、自汗盗汗等人群。特别适合体质虚弱、病后康复期的人群使用。';
			} else if (efficacy.includes('补血')) {
				return '适用于血虚体质、面色苍白、头晕心悸、失眠多梦、月经不调等人群。特别适合女性朋友和贫血患者使用。';
			} else if (efficacy.includes('清热')) {
				return '适用于热性体质、口干舌燥、便秘、上火、咽喉肿痛等人群。特别适合夏季使用或体内有热的人群。';
			}

			return '适用于相应体质和症状的人群，建议在专业医师指导下使用。';
		},

		// 获取默认使用方法（当dosage字段为空时使用）
		getDefaultUsageMethod() {
			return `1. 煎汤：常用量9-30g，大剂量可用30-120g
2. 泡茶：3-6g，开水冲泡代茶饮
3. 炖汤：与鸡肉、排骨等一起炖煮
4. 研粉：研成细粉，每次3-6g，温水送服
5. 制膏：与其他药材制成膏方服用`;
		},

		// 获取默认注意事项（当taboo字段为空时使用）
		getDefaultPrecautions() {
			return `1. 表实邪盛、气滞湿阻、食积停滞、痈疽初起或溃后热毒尚盛等实证，以及阴虚阳亢者，均不宜服用
2. 感冒发热期间暂停使用
3. 高血压患者应在医生指导下使用
4. 孕妇慎用
5. 服用期间忌食萝卜、绿豆等`;
		},

		// 加入收藏
		addToCollection() {
			uni.showToast({
				title: '已加入收藏',
				icon: 'success'
			});
		},
		
		// 查看相关食疗方案
		viewRecipes() {
			uni.navigateTo({
				url: '/pages/recipe/recipe'
			});
		},
		
		// 跳转到具体食疗方案
		goToRecipe(recipeId) {
			uni.navigateTo({
				url: `/pages/recipe-detail/recipe-detail?id=${recipeId}`
			});
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.loading-text {
	color: #666;
	font-size: 28rpx;
}

/* 药材头部信息 */
.ingredient-header {
	background-color: #fff;
	padding: 40rpx;
	display: flex;
	align-items: flex-start;
	gap: 30rpx;
}

.ingredient-image-container {
	width: 160rpx;
	height: 160rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.ingredient-image {
	width: 100%;
	height: 100%;
	border-radius: 20rpx;
}

.ingredient-icon {
	width: 160rpx;
	height: 160rpx;
	border-radius: 20rpx;
	background-color: #4CAF50;
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-text {
	color: white;
	font-size: 60rpx;
	font-weight: bold;
}

.header-info {
	flex: 1;
}

.ingredient-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}

.ingredient-alias {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 20rpx;
}

.ingredient-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
}

.tag {
	background-color: #E8F5E8;
	color: #4CAF50;
	font-size: 22rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.ingredient-meta {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	margin-top: -20rpx; /* 向上移动类型区域 */
}

.meta-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.meta-label {
	font-size: 22rpx;
	color: #999;
	margin-bottom: 5rpx;
}

.meta-value {
	font-size: 24rpx;
	color: #333;
	background-color: #f5f5f5;
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
}

/* 详细内容 */
.detail-content {
	margin-top: 20rpx;
}

.section {
	background-color: #fff;
	padding: 40rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.section-content {
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
	white-space: pre-line;
}

/* 相关食疗方案 */
.related-recipes {
	margin-top: 20rpx;
}

.recipe-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.recipe-item:last-child {
	border-bottom: none;
}

.recipe-thumb {
	width: 80rpx;
	height: 80rpx;
	border-radius: 10rpx;
	margin-right: 20rpx;
}

.recipe-info {
	flex: 1;
}

.recipe-name {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.recipe-effect {
	font-size: 24rpx;
	color: #666;
}

.arrow {
	font-size: 28rpx;
	color: #ccc;
}

/* 底部操作按钮 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	border-top: 1rpx solid #eee;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
}

.action-btn.primary {
	background-color: #4CAF50;
	color: #fff;
}

.action-btn.secondary {
	background-color: #f5f5f5;
	color: #666;
}
</style>
