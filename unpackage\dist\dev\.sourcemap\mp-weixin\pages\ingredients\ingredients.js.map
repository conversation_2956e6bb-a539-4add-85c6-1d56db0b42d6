{"version": 3, "file": "ingredients.js", "sources": ["pages/ingredients/ingredients.vue", "../产出物/安装包/HBuilderX/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5ncmVkaWVudHMvaW5ncmVkaWVudHMudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 搜索栏 -->\r\n\t\t<view class=\"search-section\">\r\n\t\t\t<view class=\"search-box\">\r\n\t\t\t\t<text class=\"search-icon\">🔍</text>\r\n\t\t\t\t<input class=\"search-input\" placeholder=\"搜索药材食材\" v-model=\"searchKeyword\" @input=\"onSearch\" />\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 分类标签 -->\r\n\t\t<view class=\"category-section\">\r\n\t\t\t<scroll-view class=\"category-scroll\" scroll-x=\"true\">\r\n\t\t\t\t<view class=\"category-list\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"category-item\" \r\n\t\t\t\t\t\t:class=\"activeCategory === item.id ? 'active' : ''\"\r\n\t\t\t\t\t\tv-for=\"item in categoryList\" \r\n\t\t\t\t\t\t:key=\"item.id\"\r\n\t\t\t\t\t\t@click=\"selectCategory(item.id)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text class=\"category-text\">{{ item.name }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 药材食材列表 -->\r\n\t\t<view class=\"ingredients-list\">\r\n\t\t\t<!-- 加载状态 -->\r\n\t\t\t<view v-if=\"loading\" class=\"loading-container\">\r\n\t\t\t\t<text class=\"loading-text\">{{ isSearching ? '正在搜索药材...' : '正在加载药材数据...' }}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 数据列表 -->\r\n\t\t\t<template v-else>\r\n\t\t\t\t<!-- 搜索无结果提示 -->\r\n\t\t\t\t<view v-if=\"isSearching && filteredIngredients.length === 0\" class=\"no-result-container\">\r\n\t\t\t\t\t<text class=\"no-result-text\">未找到相关药材</text>\r\n\t\t\t\t\t<text class=\"no-result-tip\">请尝试其他关键词</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 药材列表 -->\r\n\t\t\t\t<view class=\"ingredient-card fade-in card-hover\" v-for=\"(item, index) in filteredIngredients\" :key=\"index\" @click=\"goToDetail(item)\" :style=\"{ 'animation-delay': (index * 0.1) + 's' }\">\r\n\t\t\t\t<image class=\"ingredient-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"ingredient-info\">\r\n\t\t\t\t\t<text class=\"ingredient-name\">{{ item.name }}</text>\r\n\t\t\t\t\t<text class=\"ingredient-alias\">别名：{{ item.alias }}</text>\r\n\t\t\t\t\t<text class=\"ingredient-effect\">功效：{{ item.mainEffect }}</text>\r\n\t\t\t\t\t<view class=\"ingredient-tags\">\r\n\t\t\t\t\t\t<text class=\"tag\" v-for=\"tag in item.tags\" :key=\"tag\">{{ tag }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"ingredient-meta\">\r\n\t\t\t\t\t<text class=\"ingredient-type\">{{ item.type }}</text>\r\n\t\t\t\t\t<text class=\"ingredient-nature\">{{ item.nature }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部导航 -->\r\n\t\t<view class=\"bottom-nav\">\r\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('home')\">\r\n\t\t\t\t<text class=\"nav-icon\">🏠</text>\r\n\t\t\t\t<text class=\"nav-text\">首页</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item active\" @click=\"goToPage('ingredients')\">\r\n\t\t\t\t<text class=\"nav-icon\">🌿</text>\r\n\t\t\t\t<text class=\"nav-text\">药材食材</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item add-btn\">\r\n\t\t\t\t<text class=\"nav-icon add-icon\">+</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('recipe')\">\r\n\t\t\t\t<text class=\"nav-icon\">📋</text>\r\n\t\t\t\t<text class=\"nav-text\">食疗方案</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('profile')\">\r\n\t\t\t\t<text class=\"nav-icon\">👤</text>\r\n\t\t\t\t<text class=\"nav-text\">我的</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport api from '@/utils/api.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tsearchKeyword: '',\r\n\t\t\tactiveCategory: 'all',\r\n\t\t\tcategoryList: [\r\n\t\t\t\t{ id: 'all', name: '全部' }\r\n\t\t\t], // 动态从后端加载分类数据\r\n\t\t\tingredientsList: [],\r\n\t\t\tloading: false,\r\n\t\t\tsearchTimer: null, // 搜索防抖定时器\r\n\t\t\tisSearching: false, // 是否正在搜索状态\r\n\t\t\toriginalIngredientsList: [ // 保留原始静态数据作为备用\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t\tname: '黄芪',\r\n\t\t\t\t\talias: '北芪、绵芪',\r\n\t\t\t\t\tmainEffect: '补气固表，利尿托毒',\r\n\t\t\t\t\ttype: '中药材',\r\n\t\t\t\t\tnature: '温性',\r\n\t\t\t\t\tcategory: 'herb',\r\n\t\t\t\t\tsubCategory: 'tonify',\r\n\t\t\t\t\timage: '/static/logo.png',\r\n\t\t\t\t\ttags: ['补气', '增强免疫', '抗疲劳'],\r\n\t\t\t\t\tdescription: '黄芪是常用的补气中药，具有补气固表、利尿托毒、排脓、敛疮生肌的功效。'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 2,\r\n\t\t\t\t\tname: '枸杞',\r\n\t\t\t\t\talias: '枸杞子、红耳坠',\r\n\t\t\t\t\tmainEffect: '滋补肝肾，益精明目',\r\n\t\t\t\t\ttype: '药食同源',\r\n\t\t\t\t\tnature: '平性',\r\n\t\t\t\t\tcategory: 'food',\r\n\t\t\t\t\tsubCategory: 'tonify',\r\n\t\t\t\t\timage: '/static/logo.png',\r\n\t\t\t\t\ttags: ['滋阴', '明目', '抗衰老'],\r\n\t\t\t\t\tdescription: '枸杞具有滋补肝肾、益精明目的功效，是药食同源的佳品。'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 3,\r\n\t\t\t\t\tname: '绿豆',\r\n\t\t\t\t\talias: '青小豆',\r\n\t\t\t\t\tmainEffect: '清热解毒，消暑利尿',\r\n\t\t\t\t\ttype: '食材',\r\n\t\t\t\t\tnature: '凉性',\r\n\t\t\t\t\tcategory: 'food',\r\n\t\t\t\t\tsubCategory: 'clear',\r\n\t\t\t\t\timage: '/static/logo.png',\r\n\t\t\t\t\ttags: ['清热', '解毒', '消暑'],\r\n\t\t\t\t\tdescription: '绿豆具有清热解毒、消暑利尿的功效，夏季常用。'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 4,\r\n\t\t\t\t\tname: '党参',\r\n\t\t\t\t\talias: '上党参、防风党参',\r\n\t\t\t\t\tmainEffect: '补中益气，健脾益肺',\r\n\t\t\t\t\ttype: '中药材',\r\n\t\t\t\t\tnature: '平性',\r\n\t\t\t\t\tcategory: 'herb',\r\n\t\t\t\t\tsubCategory: 'tonify',\r\n\t\t\t\t\timage: '/static/logo.png',\r\n\t\t\t\t\ttags: ['补气', '健脾', '益肺'],\r\n\t\t\t\t\tdescription: '党参具有补中益气、健脾益肺的功效，是常用的补气药。'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 5,\r\n\t\t\t\t\tname: '莲子',\r\n\t\t\t\t\talias: '莲实、莲米',\r\n\t\t\t\t\tmainEffect: '补脾止泻，益肾涩精',\r\n\t\t\t\t\ttype: '药食同源',\r\n\t\t\t\t\tnature: '平性',\r\n\t\t\t\t\tcategory: 'food',\r\n\t\t\t\t\tsubCategory: 'regulate',\r\n\t\t\t\t\timage: '/static/logo.png',\r\n\t\t\t\t\ttags: ['健脾', '安神', '止泻'],\r\n\t\t\t\t\tdescription: '莲子具有补脾止泻、益肾涩精、养心安神的功效。'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 6,\r\n\t\t\t\t\tname: '百合',\r\n\t\t\t\t\talias: '强瞿、番韭',\r\n\t\t\t\t\tmainEffect: '养阴润肺，清心安神',\r\n\t\t\t\t\ttype: '药食同源',\r\n\t\t\t\t\tnature: '微寒',\r\n\t\t\t\t\tcategory: 'food',\r\n\t\t\t\t\tsubCategory: 'clear',\r\n\t\t\t\t\timage: '/static/logo.png',\r\n\t\t\t\t\ttags: ['润肺', '安神', '美容'],\r\n\t\t\t\t\tdescription: '百合具有养阴润肺、清心安神的功效，常用于肺燥咳嗽。'\r\n\t\t\t\t}\r\n\t\t\t]\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tfilteredIngredients() {\r\n\t\t\tconsole.log('filteredIngredients计算 - isSearching:', this.isSearching, 'searchKeyword:', this.searchKeyword, 'activeCategory:', this.activeCategory, 'ingredientsList长度:', this.ingredientsList.length);\r\n\r\n\t\t\t// 如果正在搜索状态，直接返回搜索结果，不进行本地筛选\r\n\t\t\t// 因为搜索结果已经是从后端API获取的精确匹配数据\r\n\t\t\tif (this.isSearching && this.searchKeyword.trim()) {\r\n\t\t\t\tconsole.log('搜索状态，直接返回API结果:', this.ingredientsList);\r\n\t\t\t\treturn this.ingredientsList;\r\n\t\t\t}\r\n\r\n\t\t\t// 非搜索状态，直接返回当前的药材列表\r\n\t\t\t// 因为：\r\n\t\t\t// 1. 如果是\"全部\"分类，loadMedicinalList() 已经加载了所有药材\r\n\t\t\t// 2. 如果是特定分类，loadMedicinalByType() 已经加载了该分类的药材\r\n\t\t\t// 不需要再进行本地筛选，避免ID不匹配的问题\r\n\t\t\tconsole.log('非搜索状态，直接返回当前药材列表:', this.ingredientsList);\r\n\t\t\treturn this.ingredientsList;\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.loadTypeList(); // 先加载分类数据\r\n\t\tthis.loadMedicinalList(); // 再加载药材数据\r\n\t},\r\n\tmethods: {\r\n\t\t// 加载分类列表\r\n\t\tasync loadTypeList() {\r\n\t\t\ttry {\r\n\t\t\t\tconst response = await api.getTypeList();\r\n\t\t\t\tconsole.log('分类API响应:', response);\r\n\r\n\t\t\t\tif (response && response.data) {\r\n\t\t\t\t\t// 重置分类列表，保留\"全部\"选项\r\n\t\t\t\t\tthis.categoryList = [{ id: 'all', name: '全部' }];\r\n\r\n\t\t\t\t\t// 添加从后端获取的分类数据\r\n\t\t\t\t\tresponse.data.forEach(item => {\r\n\t\t\t\t\t\tthis.categoryList.push({\r\n\t\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\t\tname: item.name\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tconsole.log('分类列表:', this.categoryList);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('加载分类列表失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '加载分类失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 加载药材食材列表\r\n\t\tasync loadMedicinalList() {\r\n\t\t\tthis.loading = true;\r\n\t\t\ttry {\r\n\t\t\t\tconst response = await api.getMedicinalList();\r\n\t\t\t\tconsole.log('API响应:', response);\r\n\r\n\t\t\t\t// 将后端数据转换为前端需要的格式，保持与图片中一致的简洁显示\r\n\t\t\t\t// 注意：getMedicinalList返回的是数组，不是Result包装的格式\r\n\t\t\t\tthis.ingredientsList = response.map(item => ({\r\n\t\t\t\t\tid: item.id,\r\n\t\t\t\t\tname: item.name || '未知',\r\n\t\t\t\t\talias: item.rname || '无别名', // 使用rname字段作为别名\r\n\t\t\t\t\tmainEffect: item.efficacy || '功效待补充', // 使用efficacy字段作为功效\r\n\t\t\t\t\ttype: item.name1 || '中药材', // 使用分类名称\r\n\t\t\t\t\tnature: this.formatMeridian(item.meridian), // 使用meridian字段作为归经显示\r\n\t\t\t\t\tcategory: this.getCategoryByType(item.name1),\r\n\t\t\t\t\tsubCategory: this.getSubCategoryByEffect(item.efficacy), // 使用efficacy字段\r\n\t\t\t\t\timage: item.avatar || '/static/logo.png', // 使用avatar字段作为图片，如果没有则使用默认图片\r\n\t\t\t\t\ttags: this.getTagsByEffect(item.efficacy), // 使用efficacy字段生成标签\r\n\t\t\t\t\tdescription: item.description || item.efficacy || '详细信息待补充'\r\n\t\t\t\t}));\r\n\r\n\t\t\t\tconsole.log('转换后的数据:', this.ingredientsList);\r\n\t\t\t\tconsole.log('所有药材 ingredientsList 长度:', this.ingredientsList.length);\r\n\r\n\t\t\t\t// 强制触发视图更新\r\n\t\t\t\tthis.$forceUpdate();\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('加载药材列表失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '加载失败，使用本地数据',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\t// 如果API调用失败，使用静态数据\r\n\t\t\t\tthis.ingredientsList = this.originalIngredientsList;\r\n\t\t\t} finally {\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 格式化性味显示，保持简洁\r\n\t\tformatNature(nature) {\r\n\t\t\tif (!nature) return '平性';\r\n\t\t\t// 简化性味显示，只保留主要信息\r\n\t\t\tif (nature.includes('温') || nature.includes('热')) return '温性';\r\n\t\t\tif (nature.includes('凉') || nature.includes('寒')) return '凉性';\r\n\t\t\tif (nature.includes('平')) return '平性';\r\n\t\t\treturn '平性';\r\n\t\t},\r\n\r\n\t\t// 格式化归经显示，提取主要功效关键词\r\n\t\tformatMeridian(meridian) {\r\n\t\t\tif (!meridian) return '补气';\r\n\r\n\t\t\t// 清理换行符和空格\r\n\t\t\tconst cleanMeridian = meridian.replace(/\\r\\n/g, '').replace(/\\n/g, '').trim();\r\n\r\n\t\t\t// 优先匹配具体功效关键词\r\n\t\t\tif (cleanMeridian.includes('补气')) return '补气';\r\n\t\t\tif (cleanMeridian.includes('补血')) return '补血';\r\n\t\t\tif (cleanMeridian.includes('增强免疫')) return '增强免疫';\r\n\t\t\tif (cleanMeridian.includes('抗疲劳')) return '抗疲劳';\r\n\t\t\tif (cleanMeridian.includes('滋阴')) return '滋阴';\r\n\t\t\tif (cleanMeridian.includes('温阳')) return '温阳';\r\n\t\t\tif (cleanMeridian.includes('清热')) return '清热';\r\n\t\t\tif (cleanMeridian.includes('解毒')) return '解毒';\r\n\t\t\tif (cleanMeridian.includes('润燥')) return '润燥';\r\n\t\t\tif (cleanMeridian.includes('安神')) return '安神';\r\n\t\t\tif (cleanMeridian.includes('利尿')) return '利尿';\r\n\t\t\tif (cleanMeridian.includes('明目')) return '明目';\r\n\r\n\t\t\t// 根据归经推断功效\r\n\t\t\tif (cleanMeridian.includes('脾') || cleanMeridian.includes('胃')) return '健脾';\r\n\t\t\tif (cleanMeridian.includes('肝')) return '养肝';\r\n\t\t\tif (cleanMeridian.includes('肺')) return '润肺';\r\n\t\t\tif (cleanMeridian.includes('肾')) return '益肾';\r\n\t\t\tif (cleanMeridian.includes('心')) return '安神';\r\n\r\n\t\t\t// 如果没有匹配到关键词，返回默认值\r\n\t\t\treturn '补气';\r\n\t\t},\r\n\r\n\t\t// 根据功效获取子分类\r\n\t\tgetSubCategoryByEffect(effect) {\r\n\t\t\tif (!effect) return 'tonify';\r\n\t\t\tif (effect.includes('补气') || effect.includes('补血') || effect.includes('滋补')) return 'tonify';\r\n\t\t\tif (effect.includes('清热') || effect.includes('解毒') || effect.includes('消暑')) return 'clear';\r\n\t\t\tif (effect.includes('调理') || effect.includes('健脾') || effect.includes('安神')) return 'regulate';\r\n\t\t\treturn 'tonify';\r\n\t\t},\r\n\r\n\t\t// 根据分类名称获取category\r\n\t\tgetCategoryByType(typeName) {\r\n\t\t\tif (!typeName) return 'herb';\r\n\t\t\tif (typeName.includes('食材') || typeName.includes('食物')) return 'food';\r\n\t\t\treturn 'herb';\r\n\t\t},\r\n\r\n\t\t// 根据功效生成标签，最多3个，保持与图片显示一致\r\n\t\tgetTagsByEffect(effect) {\r\n\t\t\tif (!effect) return ['功效'];\r\n\t\t\tconst tags = [];\r\n\r\n\t\t\t// 根据功效关键词生成对应标签\r\n\t\t\tif (effect.includes('补气')) tags.push('补气');\r\n\t\t\tif (effect.includes('补血')) tags.push('补血');\r\n\t\t\tif (effect.includes('增强免疫') || effect.includes('免疫')) tags.push('增强免疫');\r\n\t\t\tif (effect.includes('抗疲劳') || effect.includes('疲劳')) tags.push('抗疲劳');\r\n\t\t\tif (effect.includes('滋阴')) tags.push('滋阴');\r\n\t\t\tif (effect.includes('明目')) tags.push('明目');\r\n\t\t\tif (effect.includes('抗衰老') || effect.includes('衰老')) tags.push('抗衰老');\r\n\t\t\tif (effect.includes('清热')) tags.push('清热');\r\n\t\t\tif (effect.includes('解毒')) tags.push('解毒');\r\n\t\t\tif (effect.includes('消暑')) tags.push('消暑');\r\n\t\t\tif (effect.includes('润肺')) tags.push('润肺');\r\n\t\t\tif (effect.includes('安神')) tags.push('安神');\r\n\t\t\tif (effect.includes('美容')) tags.push('美容');\r\n\t\t\tif (effect.includes('健脾')) tags.push('健脾');\r\n\t\t\tif (effect.includes('益肾') || effect.includes('补肾')) tags.push('益肾');\r\n\t\t\tif (effect.includes('健胃')) tags.push('健胃');\r\n\r\n\t\t\t// 如果没有匹配的标签，根据功效内容生成通用标签\r\n\t\t\tif (tags.length === 0) {\r\n\t\t\t\tif (effect.includes('补') || effect.includes('益')) tags.push('补益');\r\n\t\t\t\tif (effect.includes('清') || effect.includes('热')) tags.push('清热');\r\n\t\t\t\tif (effect.includes('调') || effect.includes('理')) tags.push('调理');\r\n\t\t\t}\r\n\r\n\t\t\t// 最多返回3个标签，与图片显示保持一致\r\n\t\t\treturn tags.length > 0 ? tags.slice(0, 3) : ['功效'];\r\n\t\t},\r\n\r\n\t\t// 格式化功效显示（用于搜索结果）\r\n\t\tformatEffect(effect) {\r\n\t\t\tif (!effect) return '功效待补充';\r\n\t\t\t// 简化功效显示，保持简洁\r\n\t\t\treturn effect.length > 20 ? effect.substring(0, 20) + '...' : effect;\r\n\t\t},\r\n\r\n\t\t// 格式化性味显示（用于搜索结果）\r\n\t\tformatProperty(property) {\r\n\t\t\tif (!property) return '平性';\r\n\t\t\t// 简化性味显示，只保留主要信息\r\n\t\t\tif (property.includes('温') || property.includes('热')) return '温性';\r\n\t\t\tif (property.includes('凉') || property.includes('寒')) return '凉性';\r\n\t\t\tif (property.includes('平')) return '平性';\r\n\t\t\treturn '平性';\r\n\t\t},\r\n\r\n\t\t// 从功效中提取标签（用于搜索结果）\r\n\t\textractTags(effect) {\r\n\t\t\tif (!effect) return ['功效'];\r\n\t\t\tconst tags = [];\r\n\r\n\t\t\t// 根据功效关键词生成对应标签\r\n\t\t\tif (effect.includes('补气')) tags.push('补气');\r\n\t\t\tif (effect.includes('补血')) tags.push('补血');\r\n\t\t\tif (effect.includes('增强免疫') || effect.includes('免疫')) tags.push('增强免疫');\r\n\t\t\tif (effect.includes('抗疲劳') || effect.includes('疲劳')) tags.push('抗疲劳');\r\n\t\t\tif (effect.includes('滋阴')) tags.push('滋阴');\r\n\t\t\tif (effect.includes('明目')) tags.push('明目');\r\n\t\t\tif (effect.includes('抗衰老') || effect.includes('衰老')) tags.push('抗衰老');\r\n\t\t\tif (effect.includes('清热')) tags.push('清热');\r\n\t\t\tif (effect.includes('解毒')) tags.push('解毒');\r\n\t\t\tif (effect.includes('消暑')) tags.push('消暑');\r\n\t\t\tif (effect.includes('润肺')) tags.push('润肺');\r\n\t\t\tif (effect.includes('安神')) tags.push('安神');\r\n\t\t\tif (effect.includes('美容')) tags.push('美容');\r\n\t\t\tif (effect.includes('健脾')) tags.push('健脾');\r\n\t\t\tif (effect.includes('益肾') || effect.includes('补肾')) tags.push('益肾');\r\n\t\t\tif (effect.includes('健胃')) tags.push('健胃');\r\n\r\n\t\t\t// 如果没有匹配的标签，根据功效内容生成通用标签\r\n\t\t\tif (tags.length === 0) {\r\n\t\t\t\tif (effect.includes('补') || effect.includes('益')) tags.push('补益');\r\n\t\t\t\tif (effect.includes('清') || effect.includes('热')) tags.push('清热');\r\n\t\t\t\tif (effect.includes('调') || effect.includes('理')) tags.push('调理');\r\n\t\t\t}\r\n\r\n\t\t\t// 最多返回3个标签，与图片显示保持一致\r\n\t\t\treturn tags.length > 0 ? tags.slice(0, 3) : ['功效'];\r\n\t\t},\r\n\r\n\t\t// 搜索功能\r\n\t\tonSearch() {\r\n\t\t\tconsole.log('用户输入搜索关键词:', this.searchKeyword);\r\n\r\n\t\t\t// 清除之前的定时器\r\n\t\t\tif (this.searchTimer) {\r\n\t\t\t\tclearTimeout(this.searchTimer);\r\n\t\t\t}\r\n\r\n\t\t\t// 设置防抖，500ms后执行搜索\r\n\t\t\tthis.searchTimer = setTimeout(() => {\r\n\t\t\t\tthis.performSearch();\r\n\t\t\t}, 500);\r\n\t\t},\r\n\r\n\t\t// 执行搜索\r\n\t\tasync performSearch() {\r\n\t\t\tconst keyword = this.searchKeyword.trim();\r\n\t\t\tconsole.log('执行搜索 - 原始输入:', this.searchKeyword);\r\n\t\t\tconsole.log('执行搜索 - 处理后关键词:', keyword);\r\n\t\t\tconsole.log('执行搜索 - 关键词长度:', keyword.length);\r\n\t\t\tconsole.log('执行搜索 - 关键词类型:', typeof keyword);\r\n\t\t\tconsole.log('执行搜索 - 当前isSearching状态:', this.isSearching);\r\n\r\n\t\t\t// 如果搜索关键词为空，重新加载所有数据\r\n\t\t\tif (!keyword) {\r\n\t\t\t\tconsole.log('搜索关键词为空，加载所有数据');\r\n\t\t\t\tthis.isSearching = false;\r\n\t\t\t\tthis.loadMedicinalList();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\ttry {\r\n\t\t\t\tthis.isSearching = true;\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tconsole.log('开始搜索药材，关键词:', keyword);\r\n\t\t\t\tconsole.log('调用API: getMedicinalByName，参数:', keyword);\r\n\r\n\t\t\t\tconst response = await api.getMedicinalByName(keyword);\r\n\t\t\t\tconsole.log('后端API返回结果:', response);\r\n\r\n\t\t\t\t// 检查响应格式：后端返回的是Result包装的格式\r\n\t\t\t\tif (response && response.code === 200 && response.data && Array.isArray(response.data)) {\r\n\t\t\t\t\tconsole.log('搜索数据解析成功，数据条数:', response.data.length);\r\n\t\t\t\t\t// 处理搜索结果数据\r\n\t\t\t\t\tthis.ingredientsList = response.data.map(item => ({\r\n\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\tname: item.name,\r\n\t\t\t\t\t\talias: item.alias || '暂无别名',\r\n\t\t\t\t\t\tmainEffect: this.formatEffect(item.effect),\r\n\t\t\t\t\t\ttype: item.type || '中药材',\r\n\t\t\t\t\t\tnature: this.formatProperty(item.property),\r\n\t\t\t\t\t\tcategory: this.getCategoryByType(item.type),\r\n\t\t\t\t\t\tsubCategory: this.getSubCategoryByEffect(item.effect),\r\n\t\t\t\t\t\timage: item.avatar || '/static/logo.png',\r\n\t\t\t\t\t\ttags: this.extractTags(item.effect),\r\n\t\t\t\t\t\toriginalData: item\r\n\t\t\t\t\t}));\r\n\r\n\t\t\t\t\tconsole.log('搜索结果处理完成:', this.ingredientsList);\r\n\t\t\t\t\tconsole.log('设置isSearching为true，当前状态:', this.isSearching);\r\n\t\t\t\t\t// 强制触发视图更新\r\n\t\t\t\t\tthis.$forceUpdate();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('搜索无结果或数据格式错误');\r\n\t\t\t\t\tconsole.log('响应详情:', response);\r\n\t\t\t\t\tthis.ingredientsList = [];\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('搜索失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '搜索失败，请重试',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\t// 搜索失败时保持当前数据不变\r\n\t\t\t} finally {\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 选择分类\r\n\t\tasync selectCategory(categoryId) {\r\n\t\t\tconsole.log('选择分类，ID:', categoryId);\r\n\t\t\tthis.activeCategory = categoryId;\r\n\r\n\t\t\t// 清除搜索状态和关键词\r\n\t\t\tthis.searchKeyword = '';\r\n\t\t\tthis.isSearching = false;\r\n\t\t\tif (this.searchTimer) {\r\n\t\t\t\tclearTimeout(this.searchTimer);\r\n\t\t\t\tthis.searchTimer = null;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果选择\"全部\"，加载所有药材\r\n\t\t\tif (categoryId === 'all') {\r\n\t\t\t\tconsole.log('加载所有药材');\r\n\t\t\t\tthis.loadMedicinalList();\r\n\t\t\t} else {\r\n\t\t\t\t// 根据分类ID加载对应的药材\r\n\t\t\t\tconsole.log('根据分类ID加载药材:', categoryId);\r\n\t\t\t\tthis.loadMedicinalByType(categoryId);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 根据分类ID加载药材\r\n\t\tasync loadMedicinalByType(typeId) {\r\n\t\t\tconsole.log('开始加载分类药材，分类ID:', typeId);\r\n\t\t\tthis.loading = true;\r\n\t\t\ttry {\r\n\t\t\t\tconst response = await api.getMedicinalByTypeId(typeId);\r\n\t\t\t\tconsole.log('分类药材API响应:', response);\r\n\r\n\t\t\t\t// 检查响应格式：后端返回的是Result包装的格式\r\n\t\t\t\tif (response && response.code === 200 && response.data) {\r\n\t\t\t\t\tconsole.log(`成功获取分类${typeId}的药材，共${response.data.length}个`);\r\n\r\n\t\t\t\t\t// 将后端数据转换为前端需要的格式\r\n\t\t\t\t\tthis.ingredientsList = response.data.map(item => ({\r\n\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\tname: item.name || '未知',\r\n\t\t\t\t\t\talias: item.rname || '无别名',\r\n\t\t\t\t\t\tmainEffect: item.efficacy || '功效待补充',\r\n\t\t\t\t\t\ttype: item.name1 || '中药材',\r\n\t\t\t\t\t\tnature: this.formatMeridian(item.meridian),\r\n\t\t\t\t\t\tcategory: this.getCategoryByType(item.name1),\r\n\t\t\t\t\t\tsubCategory: this.getSubCategoryByEffect(item.efficacy),\r\n\t\t\t\t\t\timage: item.avatar || '/static/logo.png',\r\n\t\t\t\t\t\ttags: this.getTagsByEffect(item.efficacy),\r\n\t\t\t\t\t\tdescription: item.description || item.efficacy || '详细信息待补充'\r\n\t\t\t\t\t}));\r\n\r\n\t\t\t\t\tconsole.log('转换后的药材列表:', this.ingredientsList);\r\n\t\t\t\t\tconsole.log('ingredientsList 长度:', this.ingredientsList.length);\r\n\r\n\t\t\t\t\t// 强制触发视图更新\r\n\t\t\t\t\tthis.$forceUpdate();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.error('API响应格式错误:', response);\r\n\t\t\t\t\tthis.ingredientsList = [];\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '数据格式错误',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('加载分类药材失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '加载失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\t// 如果API调用失败，显示空列表\r\n\t\t\t\tthis.ingredientsList = [];\r\n\t\t\t} finally {\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 跳转到详情页\r\n\t\tgoToDetail(item) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/ingredient-detail/ingredient-detail?id=${item.id}`\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 导航跳转\r\n\t\tgoToPage(page) {\r\n\t\t\tswitch(page) {\r\n\t\t\t\tcase 'home':\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'ingredients':\r\n\t\t\t\t\t// 当前页面\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'recipe':\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/recipe/recipe'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'profile':\r\n\t\t\t\t\t// 跳转到个人中心页面\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 120rpx;\r\n}\r\n\r\n/* 搜索栏 */\r\n.search-section {\r\n\tbackground-color: #fff;\r\n\tpadding: 30rpx 40rpx;\r\n}\r\n\r\n.search-box {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tbackground-color: #f5f5f5;\r\n\tborder-radius: 50rpx;\r\n\tpadding: 20rpx 30rpx;\r\n}\r\n\r\n.search-icon {\r\n\tfont-size: 32rpx;\r\n\tcolor: #999;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.search-input {\r\n\tflex: 1;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n/* 分类标签 */\r\n.category-section {\r\n\tbackground-color: #fff;\r\n\tpadding: 20rpx 0;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.category-scroll {\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.category-list {\r\n\tdisplay: flex;\r\n\tpadding: 0 40rpx;\r\n}\r\n\r\n.category-item {\r\n\tflex-shrink: 0;\r\n\tpadding: 15rpx 30rpx;\r\n\tmargin-right: 20rpx;\r\n\tbackground-color: #f5f5f5;\r\n\tborder-radius: 40rpx;\r\n}\r\n\r\n.category-item.active {\r\n\tbackground-color: #4CAF50;\r\n}\r\n\r\n.category-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.category-item.active .category-text {\r\n\tcolor: #fff;\r\n}\r\n\r\n/* 药材食材列表 */\r\n.ingredients-list {\r\n\tpadding: 0 20rpx;\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 100rpx 0;\r\n}\r\n\r\n.loading-text {\r\n\tcolor: #999;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n/* 搜索无结果提示 */\r\n.no-result-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 100rpx 0;\r\n}\r\n\r\n.no-result-text {\r\n\tcolor: #666;\r\n\tfont-size: 32rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.no-result-tip {\r\n\tcolor: #999;\r\n\tfont-size: 26rpx;\r\n}\r\n\r\n.ingredient-card {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding: 30rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.ingredient-image {\r\n\twidth: 120rpx;\r\n\theight: 120rpx;\r\n\tborder-radius: 15rpx;\r\n\tmargin-right: 30rpx;\r\n}\r\n\r\n.ingredient-info {\r\n\tflex: 1;\r\n}\r\n\r\n.ingredient-name {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.ingredient-alias {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.ingredient-effect {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.ingredient-tags {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 10rpx;\r\n}\r\n\r\n.tag {\r\n\tbackground-color: #E8F5E8;\r\n\tcolor: #4CAF50;\r\n\tfont-size: 20rpx;\r\n\tpadding: 5rpx 15rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.ingredient-meta {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tgap: 10rpx;\r\n}\r\n\r\n.ingredient-type,\r\n.ingredient-nature {\r\n\tfont-size: 22rpx;\r\n\tcolor: #666;\r\n\tbackground-color: #f5f5f5;\r\n\tpadding: 8rpx 15rpx;\r\n\tborder-radius: 15rpx;\r\n}\r\n\r\n/* 底部导航 */\r\n.bottom-nav {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 120rpx;\r\n\tbackground-color: #fff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-around;\r\n\tborder-top: 1rpx solid #eee;\r\n\tz-index: 999;\r\n}\r\n\r\n.nav-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tflex: 1;\r\n}\r\n\r\n.nav-item.active .nav-text {\r\n\tcolor: #4CAF50;\r\n}\r\n\r\n.nav-item.add-btn {\r\n\tposition: relative;\r\n}\r\n\r\n.nav-icon {\r\n\tfont-size: 40rpx;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.add-icon {\r\n\tbackground-color: #4CAF50;\r\n\tcolor: #fff;\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 50rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.nav-text {\r\n\tfont-size: 20rpx;\r\n\tcolor: #666;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/桌面/Medicine-uniapp/pages/ingredients/ingredients.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "api"], "mappings": ";;;AAyFA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,cAAc;AAAA,QACb,EAAE,IAAI,OAAO,MAAM,KAAK;AAAA,MACxB;AAAA;AAAA,MACD,iBAAiB,CAAE;AAAA,MACnB,SAAS;AAAA,MACT,aAAa;AAAA;AAAA,MACb,aAAa;AAAA;AAAA,MACb,yBAAyB;AAAA;AAAA,QACxB;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM,CAAC,MAAM,QAAQ,KAAK;AAAA,UAC1B,aAAa;AAAA,QACb;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM,CAAC,MAAM,MAAM,KAAK;AAAA,UACxB,aAAa;AAAA,QACb;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,UACvB,aAAa;AAAA,QACb;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,UACvB,aAAa;AAAA,QACb;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,UACvB,aAAa;AAAA,QACb;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,UACvB,aAAa;AAAA,QACd;AAAA,MACD;AAAA,IACD;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,sBAAsB;AACrBA,oBAAY,MAAA,MAAA,OAAA,4CAAA,wCAAwC,KAAK,aAAa,kBAAkB,KAAK,eAAe,mBAAmB,KAAK,gBAAgB,sBAAsB,KAAK,gBAAgB,MAAM;AAIrM,UAAI,KAAK,eAAe,KAAK,cAAc,KAAI,GAAI;AAClDA,sBAAA,MAAA,MAAA,OAAA,4CAAY,mBAAmB,KAAK,eAAe;AACnD,eAAO,KAAK;AAAA,MACb;AAOAA,oBAAY,MAAA,MAAA,OAAA,4CAAA,qBAAqB,KAAK,eAAe;AACrD,aAAO,KAAK;AAAA,IACb;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,aAAY;AACjB,SAAK,kBAAiB;AAAA,EACtB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,eAAe;AACpB,UAAI;AACH,cAAM,WAAW,MAAMC,cAAI;AAC3BD,sBAAY,MAAA,MAAA,OAAA,4CAAA,YAAY,QAAQ;AAEhC,YAAI,YAAY,SAAS,MAAM;AAE9B,eAAK,eAAe,CAAC,EAAE,IAAI,OAAO,MAAM,KAAG,CAAG;AAG9C,mBAAS,KAAK,QAAQ,UAAQ;AAC7B,iBAAK,aAAa,KAAK;AAAA,cACtB,IAAI,KAAK;AAAA,cACT,MAAM,KAAK;AAAA,YACZ,CAAC;AAAA,UACF,CAAC;AAEDA,uFAAY,SAAS,KAAK,YAAY;AAAA,QACvC;AAAA,MACC,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,4CAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACzB,WAAK,UAAU;AACf,UAAI;AACH,cAAM,WAAW,MAAMC,cAAI;AAC3BD,sBAAY,MAAA,MAAA,OAAA,4CAAA,UAAU,QAAQ;AAI9B,aAAK,kBAAkB,SAAS,IAAI,WAAS;AAAA,UAC5C,IAAI,KAAK;AAAA,UACT,MAAM,KAAK,QAAQ;AAAA,UACnB,OAAO,KAAK,SAAS;AAAA;AAAA,UACrB,YAAY,KAAK,YAAY;AAAA;AAAA,UAC7B,MAAM,KAAK,SAAS;AAAA;AAAA,UACpB,QAAQ,KAAK,eAAe,KAAK,QAAQ;AAAA;AAAA,UACzC,UAAU,KAAK,kBAAkB,KAAK,KAAK;AAAA,UAC3C,aAAa,KAAK,uBAAuB,KAAK,QAAQ;AAAA;AAAA,UACtD,OAAO,KAAK,UAAU;AAAA;AAAA,UACtB,MAAM,KAAK,gBAAgB,KAAK,QAAQ;AAAA;AAAA,UACxC,aAAa,KAAK,eAAe,KAAK,YAAY;AAAA,QAClD,EAAC;AAEFA,sBAAY,MAAA,MAAA,OAAA,4CAAA,WAAW,KAAK,eAAe;AAC3CA,4BAAA,MAAA,OAAA,4CAAY,4BAA4B,KAAK,gBAAgB,MAAM;AAGnE,aAAK,aAAY;AAAA,MAChB,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,4CAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAED,aAAK,kBAAkB,KAAK;AAAA,MAC7B,UAAU;AACT,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,aAAa,QAAQ;AACpB,UAAI,CAAC;AAAQ,eAAO;AAEpB,UAAI,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG;AAAG,eAAO;AACzD,UAAI,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG;AAAG,eAAO;AACzD,UAAI,OAAO,SAAS,GAAG;AAAG,eAAO;AACjC,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,eAAe,UAAU;AACxB,UAAI,CAAC;AAAU,eAAO;AAGtB,YAAM,gBAAgB,SAAS,QAAQ,SAAS,EAAE,EAAE,QAAQ,OAAO,EAAE,EAAE,KAAI;AAG3E,UAAI,cAAc,SAAS,IAAI;AAAG,eAAO;AACzC,UAAI,cAAc,SAAS,IAAI;AAAG,eAAO;AACzC,UAAI,cAAc,SAAS,MAAM;AAAG,eAAO;AAC3C,UAAI,cAAc,SAAS,KAAK;AAAG,eAAO;AAC1C,UAAI,cAAc,SAAS,IAAI;AAAG,eAAO;AACzC,UAAI,cAAc,SAAS,IAAI;AAAG,eAAO;AACzC,UAAI,cAAc,SAAS,IAAI;AAAG,eAAO;AACzC,UAAI,cAAc,SAAS,IAAI;AAAG,eAAO;AACzC,UAAI,cAAc,SAAS,IAAI;AAAG,eAAO;AACzC,UAAI,cAAc,SAAS,IAAI;AAAG,eAAO;AACzC,UAAI,cAAc,SAAS,IAAI;AAAG,eAAO;AACzC,UAAI,cAAc,SAAS,IAAI;AAAG,eAAO;AAGzC,UAAI,cAAc,SAAS,GAAG,KAAK,cAAc,SAAS,GAAG;AAAG,eAAO;AACvE,UAAI,cAAc,SAAS,GAAG;AAAG,eAAO;AACxC,UAAI,cAAc,SAAS,GAAG;AAAG,eAAO;AACxC,UAAI,cAAc,SAAS,GAAG;AAAG,eAAO;AACxC,UAAI,cAAc,SAAS,GAAG;AAAG,eAAO;AAGxC,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,uBAAuB,QAAQ;AAC9B,UAAI,CAAC;AAAQ,eAAO;AACpB,UAAI,OAAO,SAAS,IAAI,KAAK,OAAO,SAAS,IAAI,KAAK,OAAO,SAAS,IAAI;AAAG,eAAO;AACpF,UAAI,OAAO,SAAS,IAAI,KAAK,OAAO,SAAS,IAAI,KAAK,OAAO,SAAS,IAAI;AAAG,eAAO;AACpF,UAAI,OAAO,SAAS,IAAI,KAAK,OAAO,SAAS,IAAI,KAAK,OAAO,SAAS,IAAI;AAAG,eAAO;AACpF,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,kBAAkB,UAAU;AAC3B,UAAI,CAAC;AAAU,eAAO;AACtB,UAAI,SAAS,SAAS,IAAI,KAAK,SAAS,SAAS,IAAI;AAAG,eAAO;AAC/D,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,gBAAgB,QAAQ;AACvB,UAAI,CAAC;AAAQ,eAAO,CAAC,IAAI;AACzB,YAAM,OAAO,CAAA;AAGb,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,MAAM,KAAK,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,MAAM;AACtE,UAAI,OAAO,SAAS,KAAK,KAAK,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,KAAK;AACpE,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,KAAK,KAAK,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,KAAK;AACpE,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI,KAAK,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AAClE,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AAGzC,UAAI,KAAK,WAAW,GAAG;AACtB,YAAI,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG;AAAG,eAAK,KAAK,IAAI;AAChE,YAAI,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG;AAAG,eAAK,KAAK,IAAI;AAChE,YAAI,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG;AAAG,eAAK,KAAK,IAAI;AAAA,MACjE;AAGA,aAAO,KAAK,SAAS,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI;AAAA,IACjD;AAAA;AAAA,IAGD,aAAa,QAAQ;AACpB,UAAI,CAAC;AAAQ,eAAO;AAEpB,aAAO,OAAO,SAAS,KAAK,OAAO,UAAU,GAAG,EAAE,IAAI,QAAQ;AAAA,IAC9D;AAAA;AAAA,IAGD,eAAe,UAAU;AACxB,UAAI,CAAC;AAAU,eAAO;AAEtB,UAAI,SAAS,SAAS,GAAG,KAAK,SAAS,SAAS,GAAG;AAAG,eAAO;AAC7D,UAAI,SAAS,SAAS,GAAG,KAAK,SAAS,SAAS,GAAG;AAAG,eAAO;AAC7D,UAAI,SAAS,SAAS,GAAG;AAAG,eAAO;AACnC,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,YAAY,QAAQ;AACnB,UAAI,CAAC;AAAQ,eAAO,CAAC,IAAI;AACzB,YAAM,OAAO,CAAA;AAGb,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,MAAM,KAAK,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,MAAM;AACtE,UAAI,OAAO,SAAS,KAAK,KAAK,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,KAAK;AACpE,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,KAAK,KAAK,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,KAAK;AACpE,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI,KAAK,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AAClE,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AAGzC,UAAI,KAAK,WAAW,GAAG;AACtB,YAAI,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG;AAAG,eAAK,KAAK,IAAI;AAChE,YAAI,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG;AAAG,eAAK,KAAK,IAAI;AAChE,YAAI,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG;AAAG,eAAK,KAAK,IAAI;AAAA,MACjE;AAGA,aAAO,KAAK,SAAS,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI;AAAA,IACjD;AAAA;AAAA,IAGD,WAAW;AACVA,oBAAA,MAAA,MAAA,OAAA,4CAAY,cAAc,KAAK,aAAa;AAG5C,UAAI,KAAK,aAAa;AACrB,qBAAa,KAAK,WAAW;AAAA,MAC9B;AAGA,WAAK,cAAc,WAAW,MAAM;AACnC,aAAK,cAAa;AAAA,MAClB,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACrB,YAAM,UAAU,KAAK,cAAc,KAAI;AACvCA,oBAAA,MAAA,MAAA,OAAA,4CAAY,gBAAgB,KAAK,aAAa;AAC9CA,oBAAY,MAAA,MAAA,OAAA,4CAAA,kBAAkB,OAAO;AACrCA,oBAAA,MAAA,MAAA,OAAA,4CAAY,iBAAiB,QAAQ,MAAM;AAC3CA,oBAAA,MAAA,MAAA,OAAA,4CAAY,iBAAiB,OAAO,OAAO;AAC3CA,oBAAY,MAAA,MAAA,OAAA,4CAAA,2BAA2B,KAAK,WAAW;AAGvD,UAAI,CAAC,SAAS;AACbA,sBAAAA,MAAA,MAAA,OAAA,4CAAY,gBAAgB;AAC5B,aAAK,cAAc;AACnB,aAAK,kBAAiB;AACtB;AAAA,MACD;AAEA,UAAI;AACH,aAAK,cAAc;AACnB,aAAK,UAAU;AACfA,sBAAA,MAAA,MAAA,OAAA,4CAAY,eAAe,OAAO;AAClCA,sBAAA,MAAA,MAAA,OAAA,4CAAY,iCAAiC,OAAO;AAEpD,cAAM,WAAW,MAAMC,UAAAA,IAAI,mBAAmB,OAAO;AACrDD,sBAAA,MAAA,MAAA,OAAA,4CAAY,cAAc,QAAQ;AAGlC,YAAI,YAAY,SAAS,SAAS,OAAO,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI,GAAG;AACvFA,uFAAY,kBAAkB,SAAS,KAAK,MAAM;AAElD,eAAK,kBAAkB,SAAS,KAAK,IAAI,WAAS;AAAA,YACjD,IAAI,KAAK;AAAA,YACT,MAAM,KAAK;AAAA,YACX,OAAO,KAAK,SAAS;AAAA,YACrB,YAAY,KAAK,aAAa,KAAK,MAAM;AAAA,YACzC,MAAM,KAAK,QAAQ;AAAA,YACnB,QAAQ,KAAK,eAAe,KAAK,QAAQ;AAAA,YACzC,UAAU,KAAK,kBAAkB,KAAK,IAAI;AAAA,YAC1C,aAAa,KAAK,uBAAuB,KAAK,MAAM;AAAA,YACpD,OAAO,KAAK,UAAU;AAAA,YACtB,MAAM,KAAK,YAAY,KAAK,MAAM;AAAA,YAClC,cAAc;AAAA,UACd,EAAC;AAEFA,wBAAY,MAAA,MAAA,OAAA,4CAAA,aAAa,KAAK,eAAe;AAC7CA,uFAAY,4BAA4B,KAAK,WAAW;AAExD,eAAK,aAAY;AAAA,eACX;AACNA,wBAAAA,+DAAY,cAAc;AAC1BA,wBAAA,MAAA,MAAA,OAAA,4CAAY,SAAS,QAAQ;AAC7B,eAAK,kBAAkB;QACxB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,4CAAc,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MAEF,UAAU;AACT,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,eAAe,YAAY;AAChCA,oBAAA,MAAA,MAAA,OAAA,4CAAY,YAAY,UAAU;AAClC,WAAK,iBAAiB;AAGtB,WAAK,gBAAgB;AACrB,WAAK,cAAc;AACnB,UAAI,KAAK,aAAa;AACrB,qBAAa,KAAK,WAAW;AAC7B,aAAK,cAAc;AAAA,MACpB;AAGA,UAAI,eAAe,OAAO;AACzBA,sBAAAA,MAAA,MAAA,OAAA,4CAAY,QAAQ;AACpB,aAAK,kBAAiB;AAAA,aAChB;AAENA,sBAAA,MAAA,MAAA,OAAA,4CAAY,eAAe,UAAU;AACrC,aAAK,oBAAoB,UAAU;AAAA,MACpC;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,oBAAoB,QAAQ;AACjCA,oBAAY,MAAA,MAAA,OAAA,4CAAA,kBAAkB,MAAM;AACpC,WAAK,UAAU;AACf,UAAI;AACH,cAAM,WAAW,MAAMC,UAAAA,IAAI,qBAAqB,MAAM;AACtDD,sBAAA,MAAA,MAAA,OAAA,4CAAY,cAAc,QAAQ;AAGlC,YAAI,YAAY,SAAS,SAAS,OAAO,SAAS,MAAM;AACvDA,wBAAAA,MAAA,MAAA,OAAA,4CAAY,SAAS,MAAM,QAAQ,SAAS,KAAK,MAAM,GAAG;AAG1D,eAAK,kBAAkB,SAAS,KAAK,IAAI,WAAS;AAAA,YACjD,IAAI,KAAK;AAAA,YACT,MAAM,KAAK,QAAQ;AAAA,YACnB,OAAO,KAAK,SAAS;AAAA,YACrB,YAAY,KAAK,YAAY;AAAA,YAC7B,MAAM,KAAK,SAAS;AAAA,YACpB,QAAQ,KAAK,eAAe,KAAK,QAAQ;AAAA,YACzC,UAAU,KAAK,kBAAkB,KAAK,KAAK;AAAA,YAC3C,aAAa,KAAK,uBAAuB,KAAK,QAAQ;AAAA,YACtD,OAAO,KAAK,UAAU;AAAA,YACtB,MAAM,KAAK,gBAAgB,KAAK,QAAQ;AAAA,YACxC,aAAa,KAAK,eAAe,KAAK,YAAY;AAAA,UAClD,EAAC;AAEFA,wBAAY,MAAA,MAAA,OAAA,4CAAA,aAAa,KAAK,eAAe;AAC7CA,8BAAA,MAAA,OAAA,4CAAY,uBAAuB,KAAK,gBAAgB,MAAM;AAG9D,eAAK,aAAY;AAAA,eACX;AACNA,wBAAc,MAAA,MAAA,SAAA,4CAAA,cAAc,QAAQ;AACpC,eAAK,kBAAkB;AACvBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,4CAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAED,aAAK,kBAAkB;MACxB,UAAU;AACT,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,WAAW,MAAM;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,iDAAiD,KAAK,EAAE;AAAA,MAC9D,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,SAAS,MAAM;AACd,cAAO,MAAI;AAAA,QACV,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,UACN,CAAC;AACD;AAAA,QACD,KAAK;AAEJ;AAAA,QACD,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,UACN,CAAC;AACD;AAAA,MAIF;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9lBA,GAAG,WAAW,eAAe;"}