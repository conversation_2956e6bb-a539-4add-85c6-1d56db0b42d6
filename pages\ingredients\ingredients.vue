<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input class="search-input" placeholder="搜索药材食材" v-model="searchKeyword" @input="onSearch" />
			</view>
		</view>
		
		<!-- 分类标签 -->
		<view class="category-section">
			<scroll-view class="category-scroll" scroll-x="true">
				<view class="category-list">
					<view 
						class="category-item" 
						:class="activeCategory === item.id ? 'active' : ''"
						v-for="item in categoryList" 
						:key="item.id"
						@click="selectCategory(item.id)"
					>
						<text class="category-text">{{ item.name }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 药材食材列表 -->
		<view class="ingredients-list">
			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<text class="loading-text">{{ isSearching ? '正在搜索药材...' : '正在加载药材数据...' }}</text>
			</view>

			<!-- 数据列表 -->
			<template v-else>
				<!-- 搜索无结果提示 -->
				<view v-if="isSearching && filteredIngredients.length === 0" class="no-result-container">
					<text class="no-result-text">未找到相关药材</text>
					<text class="no-result-tip">请尝试其他关键词</text>
				</view>

				<!-- 药材列表 -->
				<view class="ingredient-card fade-in card-hover" v-for="(item, index) in filteredIngredients" :key="index" @click="goToDetail(item)" :style="{ 'animation-delay': (index * 0.1) + 's' }">
				<image class="ingredient-image" :src="item.image" mode="aspectFill"></image>
				<view class="ingredient-info">
					<text class="ingredient-name">{{ item.name }}</text>
					<text class="ingredient-alias">别名：{{ item.alias }}</text>
					<text class="ingredient-effect">功效：{{ item.mainEffect }}</text>
					<view class="ingredient-tags">
						<text class="tag" v-for="tag in item.tags" :key="tag">{{ tag }}</text>
					</view>
				</view>
				<view class="ingredient-meta">
					<text class="ingredient-type">{{ item.type }}</text>
					<text class="ingredient-nature">{{ item.nature }}</text>
				</view>
			</view>
			</template>
		</view>
		
		<!-- 底部导航 -->
		<view class="bottom-nav">
			<view class="nav-item" @click="goToPage('home')">
				<text class="nav-icon">🏠</text>
				<text class="nav-text">首页</text>
			</view>
			<view class="nav-item active" @click="goToPage('ingredients')">
				<text class="nav-icon">🌿</text>
				<text class="nav-text">药材食材</text>
			</view>
			<view class="nav-item add-btn">
				<text class="nav-icon add-icon">+</text>
			</view>
			<view class="nav-item" @click="goToPage('recipe')">
				<text class="nav-icon">📋</text>
				<text class="nav-text">食疗方案</text>
			</view>
			<view class="nav-item" @click="goToPage('profile')">
				<text class="nav-icon">👤</text>
				<text class="nav-text">我的</text>
			</view>
		</view>
	</view>
</template>

<script>
import api from '@/utils/api.js';

export default {
	data() {
		return {
			searchKeyword: '',
			activeCategory: 'all',
			categoryList: [
				{ id: 'all', name: '全部' }
			], // 动态从后端加载分类数据
			ingredientsList: [],
			loading: false,
			searchTimer: null, // 搜索防抖定时器
			isSearching: false, // 是否正在搜索状态
			originalIngredientsList: [ // 保留原始静态数据作为备用
				{
					id: 1,
					name: '黄芪',
					alias: '北芪、绵芪',
					mainEffect: '补气固表，利尿托毒',
					type: '中药材',
					nature: '温性',
					category: 'herb',
					subCategory: 'tonify',
					image: '/static/logo.png',
					tags: ['补气', '增强免疫', '抗疲劳'],
					description: '黄芪是常用的补气中药，具有补气固表、利尿托毒、排脓、敛疮生肌的功效。'
				},
				{
					id: 2,
					name: '枸杞',
					alias: '枸杞子、红耳坠',
					mainEffect: '滋补肝肾，益精明目',
					type: '药食同源',
					nature: '平性',
					category: 'food',
					subCategory: 'tonify',
					image: '/static/logo.png',
					tags: ['滋阴', '明目', '抗衰老'],
					description: '枸杞具有滋补肝肾、益精明目的功效，是药食同源的佳品。'
				},
				{
					id: 3,
					name: '绿豆',
					alias: '青小豆',
					mainEffect: '清热解毒，消暑利尿',
					type: '食材',
					nature: '凉性',
					category: 'food',
					subCategory: 'clear',
					image: '/static/logo.png',
					tags: ['清热', '解毒', '消暑'],
					description: '绿豆具有清热解毒、消暑利尿的功效，夏季常用。'
				},
				{
					id: 4,
					name: '党参',
					alias: '上党参、防风党参',
					mainEffect: '补中益气，健脾益肺',
					type: '中药材',
					nature: '平性',
					category: 'herb',
					subCategory: 'tonify',
					image: '/static/logo.png',
					tags: ['补气', '健脾', '益肺'],
					description: '党参具有补中益气、健脾益肺的功效，是常用的补气药。'
				},
				{
					id: 5,
					name: '莲子',
					alias: '莲实、莲米',
					mainEffect: '补脾止泻，益肾涩精',
					type: '药食同源',
					nature: '平性',
					category: 'food',
					subCategory: 'regulate',
					image: '/static/logo.png',
					tags: ['健脾', '安神', '止泻'],
					description: '莲子具有补脾止泻、益肾涩精、养心安神的功效。'
				},
				{
					id: 6,
					name: '百合',
					alias: '强瞿、番韭',
					mainEffect: '养阴润肺，清心安神',
					type: '药食同源',
					nature: '微寒',
					category: 'food',
					subCategory: 'clear',
					image: '/static/logo.png',
					tags: ['润肺', '安神', '美容'],
					description: '百合具有养阴润肺、清心安神的功效，常用于肺燥咳嗽。'
				}
			]
		}
	},
	computed: {
		filteredIngredients() {
			console.log('filteredIngredients计算 - isSearching:', this.isSearching, 'searchKeyword:', this.searchKeyword, 'activeCategory:', this.activeCategory, 'ingredientsList长度:', this.ingredientsList.length);

			// 如果正在搜索状态，直接返回搜索结果，不进行本地筛选
			// 因为搜索结果已经是从后端API获取的精确匹配数据
			if (this.isSearching && this.searchKeyword.trim()) {
				console.log('搜索状态，直接返回API结果:', this.ingredientsList);
				return this.ingredientsList;
			}

			// 非搜索状态，直接返回当前的药材列表
			// 因为：
			// 1. 如果是"全部"分类，loadMedicinalList() 已经加载了所有药材
			// 2. 如果是特定分类，loadMedicinalByType() 已经加载了该分类的药材
			// 不需要再进行本地筛选，避免ID不匹配的问题
			console.log('非搜索状态，直接返回当前药材列表:', this.ingredientsList);
			return this.ingredientsList;
		}
	},
	onLoad() {
		this.loadTypeList(); // 先加载分类数据
		this.loadMedicinalList(); // 再加载药材数据
	},
	methods: {
		// 加载分类列表
		async loadTypeList() {
			try {
				const response = await api.getTypeList();
				console.log('分类API响应:', response);

				if (response && response.data) {
					// 重置分类列表，保留"全部"选项
					this.categoryList = [{ id: 'all', name: '全部' }];

					// 添加从后端获取的分类数据
					response.data.forEach(item => {
						this.categoryList.push({
							id: item.id,
							name: item.name
						});
					});

					console.log('分类列表:', this.categoryList);
				}
			} catch (error) {
				console.error('加载分类列表失败:', error);
				uni.showToast({
					title: '加载分类失败',
					icon: 'none'
				});
			}
		},

		// 加载药材食材列表
		async loadMedicinalList() {
			this.loading = true;
			try {
				const response = await api.getMedicinalList();
				console.log('API响应:', response);

				// 将后端数据转换为前端需要的格式，保持与图片中一致的简洁显示
				// 注意：getMedicinalList返回的是数组，不是Result包装的格式
				this.ingredientsList = response.map(item => ({
					id: item.id,
					name: item.name || '未知',
					alias: item.rname || '无别名', // 使用rname字段作为别名
					mainEffect: item.efficacy || '功效待补充', // 使用efficacy字段作为功效
					type: item.name1 || '中药材', // 使用分类名称
					nature: this.formatMeridian(item.meridian), // 使用meridian字段作为归经显示
					category: this.getCategoryByType(item.name1),
					subCategory: this.getSubCategoryByEffect(item.efficacy), // 使用efficacy字段
					image: item.avatar || '/static/logo.png', // 使用avatar字段作为图片，如果没有则使用默认图片
					tags: this.getTagsByEffect(item.efficacy), // 使用efficacy字段生成标签
					description: item.description || item.efficacy || '详细信息待补充'
				}));

				console.log('转换后的数据:', this.ingredientsList);
				console.log('所有药材 ingredientsList 长度:', this.ingredientsList.length);

				// 强制触发视图更新
				this.$forceUpdate();
			} catch (error) {
				console.error('加载药材列表失败:', error);
				uni.showToast({
					title: '加载失败，使用本地数据',
					icon: 'none'
				});
				// 如果API调用失败，使用静态数据
				this.ingredientsList = this.originalIngredientsList;
			} finally {
				this.loading = false;
			}
		},

		// 格式化性味显示，保持简洁
		formatNature(nature) {
			if (!nature) return '平性';
			// 简化性味显示，只保留主要信息
			if (nature.includes('温') || nature.includes('热')) return '温性';
			if (nature.includes('凉') || nature.includes('寒')) return '凉性';
			if (nature.includes('平')) return '平性';
			return '平性';
		},

		// 格式化归经显示，提取主要功效关键词
		formatMeridian(meridian) {
			if (!meridian) return '补气';

			// 清理换行符和空格
			const cleanMeridian = meridian.replace(/\r\n/g, '').replace(/\n/g, '').trim();

			// 优先匹配具体功效关键词
			if (cleanMeridian.includes('补气')) return '补气';
			if (cleanMeridian.includes('补血')) return '补血';
			if (cleanMeridian.includes('增强免疫')) return '增强免疫';
			if (cleanMeridian.includes('抗疲劳')) return '抗疲劳';
			if (cleanMeridian.includes('滋阴')) return '滋阴';
			if (cleanMeridian.includes('温阳')) return '温阳';
			if (cleanMeridian.includes('清热')) return '清热';
			if (cleanMeridian.includes('解毒')) return '解毒';
			if (cleanMeridian.includes('润燥')) return '润燥';
			if (cleanMeridian.includes('安神')) return '安神';
			if (cleanMeridian.includes('利尿')) return '利尿';
			if (cleanMeridian.includes('明目')) return '明目';

			// 根据归经推断功效
			if (cleanMeridian.includes('脾') || cleanMeridian.includes('胃')) return '健脾';
			if (cleanMeridian.includes('肝')) return '养肝';
			if (cleanMeridian.includes('肺')) return '润肺';
			if (cleanMeridian.includes('肾')) return '益肾';
			if (cleanMeridian.includes('心')) return '安神';

			// 如果没有匹配到关键词，返回默认值
			return '补气';
		},

		// 根据功效获取子分类
		getSubCategoryByEffect(effect) {
			if (!effect) return 'tonify';
			if (effect.includes('补气') || effect.includes('补血') || effect.includes('滋补')) return 'tonify';
			if (effect.includes('清热') || effect.includes('解毒') || effect.includes('消暑')) return 'clear';
			if (effect.includes('调理') || effect.includes('健脾') || effect.includes('安神')) return 'regulate';
			return 'tonify';
		},

		// 根据分类名称获取category
		getCategoryByType(typeName) {
			if (!typeName) return 'herb';
			if (typeName.includes('食材') || typeName.includes('食物')) return 'food';
			return 'herb';
		},

		// 根据功效生成标签，最多3个，保持与图片显示一致
		getTagsByEffect(effect) {
			if (!effect) return ['功效'];
			const tags = [];

			// 根据功效关键词生成对应标签
			if (effect.includes('补气')) tags.push('补气');
			if (effect.includes('补血')) tags.push('补血');
			if (effect.includes('增强免疫') || effect.includes('免疫')) tags.push('增强免疫');
			if (effect.includes('抗疲劳') || effect.includes('疲劳')) tags.push('抗疲劳');
			if (effect.includes('滋阴')) tags.push('滋阴');
			if (effect.includes('明目')) tags.push('明目');
			if (effect.includes('抗衰老') || effect.includes('衰老')) tags.push('抗衰老');
			if (effect.includes('清热')) tags.push('清热');
			if (effect.includes('解毒')) tags.push('解毒');
			if (effect.includes('消暑')) tags.push('消暑');
			if (effect.includes('润肺')) tags.push('润肺');
			if (effect.includes('安神')) tags.push('安神');
			if (effect.includes('美容')) tags.push('美容');
			if (effect.includes('健脾')) tags.push('健脾');
			if (effect.includes('益肾') || effect.includes('补肾')) tags.push('益肾');
			if (effect.includes('健胃')) tags.push('健胃');

			// 如果没有匹配的标签，根据功效内容生成通用标签
			if (tags.length === 0) {
				if (effect.includes('补') || effect.includes('益')) tags.push('补益');
				if (effect.includes('清') || effect.includes('热')) tags.push('清热');
				if (effect.includes('调') || effect.includes('理')) tags.push('调理');
			}

			// 最多返回3个标签，与图片显示保持一致
			return tags.length > 0 ? tags.slice(0, 3) : ['功效'];
		},

		// 格式化功效显示（用于搜索结果）
		formatEffect(effect) {
			if (!effect) return '功效待补充';
			// 简化功效显示，保持简洁
			return effect.length > 20 ? effect.substring(0, 20) + '...' : effect;
		},

		// 格式化性味显示（用于搜索结果）
		formatProperty(property) {
			if (!property) return '平性';
			// 简化性味显示，只保留主要信息
			if (property.includes('温') || property.includes('热')) return '温性';
			if (property.includes('凉') || property.includes('寒')) return '凉性';
			if (property.includes('平')) return '平性';
			return '平性';
		},

		// 从功效中提取标签（用于搜索结果）
		extractTags(effect) {
			if (!effect) return ['功效'];
			const tags = [];

			// 根据功效关键词生成对应标签
			if (effect.includes('补气')) tags.push('补气');
			if (effect.includes('补血')) tags.push('补血');
			if (effect.includes('增强免疫') || effect.includes('免疫')) tags.push('增强免疫');
			if (effect.includes('抗疲劳') || effect.includes('疲劳')) tags.push('抗疲劳');
			if (effect.includes('滋阴')) tags.push('滋阴');
			if (effect.includes('明目')) tags.push('明目');
			if (effect.includes('抗衰老') || effect.includes('衰老')) tags.push('抗衰老');
			if (effect.includes('清热')) tags.push('清热');
			if (effect.includes('解毒')) tags.push('解毒');
			if (effect.includes('消暑')) tags.push('消暑');
			if (effect.includes('润肺')) tags.push('润肺');
			if (effect.includes('安神')) tags.push('安神');
			if (effect.includes('美容')) tags.push('美容');
			if (effect.includes('健脾')) tags.push('健脾');
			if (effect.includes('益肾') || effect.includes('补肾')) tags.push('益肾');
			if (effect.includes('健胃')) tags.push('健胃');

			// 如果没有匹配的标签，根据功效内容生成通用标签
			if (tags.length === 0) {
				if (effect.includes('补') || effect.includes('益')) tags.push('补益');
				if (effect.includes('清') || effect.includes('热')) tags.push('清热');
				if (effect.includes('调') || effect.includes('理')) tags.push('调理');
			}

			// 最多返回3个标签，与图片显示保持一致
			return tags.length > 0 ? tags.slice(0, 3) : ['功效'];
		},

		// 搜索功能
		onSearch() {
			console.log('用户输入搜索关键词:', this.searchKeyword);

			// 清除之前的定时器
			if (this.searchTimer) {
				clearTimeout(this.searchTimer);
			}

			// 设置防抖，500ms后执行搜索
			this.searchTimer = setTimeout(() => {
				this.performSearch();
			}, 500);
		},

		// 执行搜索
		async performSearch() {
			const keyword = this.searchKeyword.trim();
			console.log('执行搜索 - 原始输入:', this.searchKeyword);
			console.log('执行搜索 - 处理后关键词:', keyword);
			console.log('执行搜索 - 关键词长度:', keyword.length);
			console.log('执行搜索 - 关键词类型:', typeof keyword);
			console.log('执行搜索 - 当前isSearching状态:', this.isSearching);

			// 如果搜索关键词为空，重新加载所有数据
			if (!keyword) {
				console.log('搜索关键词为空，加载所有数据');
				this.isSearching = false;
				this.loadMedicinalList();
				return;
			}

			try {
				this.isSearching = true;
				this.loading = true;
				console.log('开始搜索药材，关键词:', keyword);
				console.log('调用API: getMedicinalByName，参数:', keyword);

				const response = await api.getMedicinalByName(keyword);
				console.log('后端API返回结果:', response);

				// 检查响应格式：后端返回的是Result包装的格式
				if (response && response.code === 200 && response.data && Array.isArray(response.data)) {
					console.log('搜索数据解析成功，数据条数:', response.data.length);
					// 处理搜索结果数据
					this.ingredientsList = response.data.map(item => ({
						id: item.id,
						name: item.name,
						alias: item.alias || '暂无别名',
						mainEffect: this.formatEffect(item.effect),
						type: item.type || '中药材',
						nature: this.formatProperty(item.property),
						category: this.getCategoryByType(item.type),
						subCategory: this.getSubCategoryByEffect(item.effect),
						image: item.avatar || '/static/logo.png',
						tags: this.extractTags(item.effect),
						originalData: item
					}));

					console.log('搜索结果处理完成:', this.ingredientsList);
					console.log('设置isSearching为true，当前状态:', this.isSearching);
					// 强制触发视图更新
					this.$forceUpdate();
				} else {
					console.log('搜索无结果或数据格式错误');
					console.log('响应详情:', response);
					this.ingredientsList = [];
				}
			} catch (error) {
				console.error('搜索失败:', error);
				uni.showToast({
					title: '搜索失败，请重试',
					icon: 'none'
				});
				// 搜索失败时保持当前数据不变
			} finally {
				this.loading = false;
			}
		},
		
		// 选择分类
		async selectCategory(categoryId) {
			console.log('选择分类，ID:', categoryId);
			this.activeCategory = categoryId;

			// 清除搜索状态和关键词
			this.searchKeyword = '';
			this.isSearching = false;
			if (this.searchTimer) {
				clearTimeout(this.searchTimer);
				this.searchTimer = null;
			}

			// 如果选择"全部"，加载所有药材
			if (categoryId === 'all') {
				console.log('加载所有药材');
				this.loadMedicinalList();
			} else {
				// 根据分类ID加载对应的药材
				console.log('根据分类ID加载药材:', categoryId);
				this.loadMedicinalByType(categoryId);
			}
		},

		// 根据分类ID加载药材
		async loadMedicinalByType(typeId) {
			console.log('开始加载分类药材，分类ID:', typeId);
			this.loading = true;
			try {
				const response = await api.getMedicinalByTypeId(typeId);
				console.log('分类药材API响应:', response);

				// 检查响应格式：后端返回的是Result包装的格式
				if (response && response.code === 200 && response.data) {
					console.log(`成功获取分类${typeId}的药材，共${response.data.length}个`);

					// 将后端数据转换为前端需要的格式
					this.ingredientsList = response.data.map(item => ({
						id: item.id,
						name: item.name || '未知',
						alias: item.rname || '无别名',
						mainEffect: item.efficacy || '功效待补充',
						type: item.name1 || '中药材',
						nature: this.formatMeridian(item.meridian),
						category: this.getCategoryByType(item.name1),
						subCategory: this.getSubCategoryByEffect(item.efficacy),
						image: item.avatar || '/static/logo.png',
						tags: this.getTagsByEffect(item.efficacy),
						description: item.description || item.efficacy || '详细信息待补充'
					}));

					console.log('转换后的药材列表:', this.ingredientsList);
					console.log('ingredientsList 长度:', this.ingredientsList.length);

					// 强制触发视图更新
					this.$forceUpdate();
				} else {
					console.error('API响应格式错误:', response);
					this.ingredientsList = [];
					uni.showToast({
						title: '数据格式错误',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('加载分类药材失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
				// 如果API调用失败，显示空列表
				this.ingredientsList = [];
			} finally {
				this.loading = false;
			}
		},
		
		// 跳转到详情页
		goToDetail(item) {
			uni.navigateTo({
				url: `/pages/ingredient-detail/ingredient-detail?id=${item.id}`
			});
		},
		
		// 导航跳转
		goToPage(page) {
			switch(page) {
				case 'home':
					uni.navigateTo({
						url: '/pages/index/index'
					});
					break;
				case 'ingredients':
					// 当前页面
					break;
				case 'recipe':
					uni.navigateTo({
						url: '/pages/recipe/recipe'
					});
					break;
				case 'profile':
					// 跳转到个人中心页面
					break;
			}
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 搜索栏 */
.search-section {
	background-color: #fff;
	padding: 30rpx 40rpx;
}

.search-box {
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
}

.search-icon {
	font-size: 32rpx;
	color: #999;
	margin-right: 20rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

/* 分类标签 */
.category-section {
	background-color: #fff;
	padding: 20rpx 0;
	margin-bottom: 20rpx;
}

.category-scroll {
	white-space: nowrap;
}

.category-list {
	display: flex;
	padding: 0 40rpx;
}

.category-item {
	flex-shrink: 0;
	padding: 15rpx 30rpx;
	margin-right: 20rpx;
	background-color: #f5f5f5;
	border-radius: 40rpx;
}

.category-item.active {
	background-color: #4CAF50;
}

.category-text {
	font-size: 26rpx;
	color: #666;
}

.category-item.active .category-text {
	color: #fff;
}

/* 药材食材列表 */
.ingredients-list {
	padding: 0 20rpx;
}

/* 加载状态 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 100rpx 0;
}

.loading-text {
	color: #999;
	font-size: 28rpx;
}

/* 搜索无结果提示 */
.no-result-container {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 100rpx 0;
}

.no-result-text {
	color: #666;
	font-size: 32rpx;
	margin-bottom: 20rpx;
}

.no-result-tip {
	color: #999;
	font-size: 26rpx;
}

.ingredient-card {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.ingredient-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 15rpx;
	margin-right: 30rpx;
}

.ingredient-info {
	flex: 1;
}

.ingredient-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.ingredient-alias {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 8rpx;
}

.ingredient-effect {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.ingredient-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
}

.tag {
	background-color: #E8F5E8;
	color: #4CAF50;
	font-size: 20rpx;
	padding: 5rpx 15rpx;
	border-radius: 20rpx;
}

.ingredient-meta {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
}

.ingredient-type,
.ingredient-nature {
	font-size: 22rpx;
	color: #666;
	background-color: #f5f5f5;
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
}

/* 底部导航 */
.bottom-nav {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-around;
	border-top: 1rpx solid #eee;
	z-index: 999;
}

.nav-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
}

.nav-item.active .nav-text {
	color: #4CAF50;
}

.nav-item.add-btn {
	position: relative;
}

.nav-icon {
	font-size: 40rpx;
	margin-bottom: 8rpx;
}

.add-icon {
	background-color: #4CAF50;
	color: #fff;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 50rpx;
	font-weight: bold;
}

.nav-text {
	font-size: 20rpx;
	color: #666;
}
</style>
