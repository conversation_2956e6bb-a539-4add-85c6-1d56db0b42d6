<template>
	<view class="login-container">
		<view class="login-header">
			<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
			<text class="app-name">中医食疗</text>
			<text class="welcome-text">欢迎使用中医食疗养生平台</text>
		</view>
		
		<view class="login-form">
			<view class="input-group">
				<view class="input-item">
					<text class="input-icon">📱</text>
					<input 
						class="input-field" 
						type="text" 
						placeholder="请输入手机号" 
						v-model="phone"
						maxlength="11"
					/>
				</view>
				
				<view class="input-item">
					<text class="input-icon">🔒</text>
					<input 
						class="input-field" 
						:type="showPassword ? 'text' : 'password'" 
						placeholder="请输入密码" 
						v-model="password"
					/>
					<text class="password-toggle" @click="togglePassword">
						{{ showPassword ? '👁️' : '👁️‍🗨️' }}
					</text>
				</view>
			</view>
			
			<button class="login-btn" @click="handleLogin" :disabled="!canLogin">
				{{ isLoading ? '登录中...' : '立即登录' }}
			</button>
			
			<view class="login-options">
				<text class="register-link" @click="goToRegister">还没有账号？立即注册</text>
				<text class="forgot-link" @click="forgotPassword">忘记密码？</text>
			</view>
		</view>
		
		<view class="login-footer">
			<text class="agreement-text">
				登录即表示同意
				<text class="link-text" @click="showAgreement">《用户协议》</text>
				和
				<text class="link-text" @click="showPrivacy">《隐私政策》</text>
			</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			phone: '',
			password: '',
			showPassword: false,
			isLoading: false
		}
	},
	
	computed: {
		canLogin() {
			return this.phone.length === 11 && this.password.length >= 6 && !this.isLoading;
		}
	},
	
	methods: {
		// 切换密码显示
		togglePassword() {
			this.showPassword = !this.showPassword;
		},
		
		// 处理登录
		async handleLogin() {
			if (!this.canLogin) return;
			
			// 简单的手机号验证
			const phoneReg = /^1[3-9]\d{9}$/;
			if (!phoneReg.test(this.phone)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return;
			}
			
			this.isLoading = true;
			
			try {
				// 模拟登录请求
				await this.simulateLogin();
				
				// 保存用户信息到本地存储
				const userInfo = {
					phone: this.phone,
					nickname: '养生达人',
					avatar: '/static/avatar.png',
					loginTime: Date.now()
				};
				
				uni.setStorageSync('userInfo', userInfo);
				uni.setStorageSync('token', 'mock_token_' + Date.now());
				
				uni.showToast({
					title: '登录成功',
					icon: 'success'
				});
				
				// 延迟跳转回个人中心
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
				
			} catch (error) {
				uni.showToast({
					title: '登录失败，请重试',
					icon: 'none'
				});
			} finally {
				this.isLoading = false;
			}
		},
		
		// 模拟登录请求
		simulateLogin() {
			return new Promise((resolve, reject) => {
				setTimeout(() => {
					// 模拟登录成功
					if (this.password.length >= 6) {
						resolve();
					} else {
						reject(new Error('密码错误'));
					}
				}, 2000);
			});
		},
		
		// 跳转到注册页面
		goToRegister() {
			uni.showToast({
				title: '注册功能开发中',
				icon: 'none'
			});
		},
		
		// 忘记密码
		forgotPassword() {
			uni.showToast({
				title: '找回密码功能开发中',
				icon: 'none'
			});
		},
		
		// 显示用户协议
		showAgreement() {
			uni.showToast({
				title: '用户协议',
				icon: 'none'
			});
		},
		
		// 显示隐私政策
		showPrivacy() {
			uni.showToast({
				title: '隐私政策',
				icon: 'none'
			});
		}
	}
}
</script>

<style scoped>
	.login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 60rpx 40rpx 40rpx;
		display: flex;
		flex-direction: column;
	}
	
	.login-header {
		text-align: center;
		margin-bottom: 80rpx;
	}
	
	.logo {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
	}
	
	.app-name {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: white;
		margin-bottom: 10rpx;
	}
	
	.welcome-text {
		display: block;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}
	
	.login-form {
		flex: 1;
	}
	
	.input-group {
		margin-bottom: 60rpx;
	}
	
	.input-item {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 50rpx;
		padding: 0 30rpx;
		margin-bottom: 30rpx;
		display: flex;
		align-items: center;
		height: 100rpx;
	}
	
	.input-icon {
		font-size: 32rpx;
		margin-right: 20rpx;
		width: 40rpx;
		text-align: center;
	}
	
	.input-field {
		flex: 1;
		font-size: 32rpx;
		color: #333;
	}
	
	.password-toggle {
		font-size: 32rpx;
		padding: 10rpx;
		color: #666;
	}
	
	.login-btn {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(45deg, #4CAF50, #45a049);
		color: white;
		border: none;
		border-radius: 50rpx;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 40rpx;
	}
	
	.login-btn:disabled {
		background: #ccc;
		color: #999;
	}
	
	.login-options {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.register-link,
	.forgot-link {
		color: rgba(255, 255, 255, 0.9);
		font-size: 28rpx;
	}
	
	.login-footer {
		text-align: center;
		margin-top: 60rpx;
	}
	
	.agreement-text {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
		line-height: 1.5;
	}
	
	.link-text {
		color: rgba(255, 255, 255, 0.9);
		text-decoration: underline;
	}
</style>
