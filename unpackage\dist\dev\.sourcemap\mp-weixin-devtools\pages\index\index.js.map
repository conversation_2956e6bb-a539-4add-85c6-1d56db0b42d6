{"version": 3, "names": ["_sfc_main", "data", "title", "featuredIngredients", "loading", "onLoad", "loadFeaturedIngredients", "methods", "_this", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "response", "ingredients", "wrap", "_callee$", "_context", "prev", "next", "common_vendor", "index", "__f__", "utils_api", "api", "getMedicinalList", "sent", "length", "slice", "map", "item", "id", "name", "nature", "formatProperty", "property", "meridian", "formatMeridian", "image", "avatar", "originalData", "t0", "getDefaultIngredients", "finish", "stop", "cleaned", "replace", "trim", "substring", "includes", "goToRecipe", "navigateTo", "url", "goToIngredients", "goToProfile", "goToIngredientDetail", "concat", "goToPage", "page", "goToTest", "showComingSoon", "showToast", "icon", "wx", "createPage", "MiniProgramPage"], "sources": ["index.vue", "E:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 顶部导航栏 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"header-left\">\r\n\t\t\t\t<text class=\"leaf-icon\">🍃</text>\r\n\t\t\t\t<text class=\"app-title\">药食同源</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"header-right\">\r\n\t\t\t\t<text class=\"search-icon\">🔍</text>\r\n\t\t\t\t<text class=\"notification-icon\">🔔</text>\r\n\t\t\t\t<image class=\"avatar\" src=\"/static/logo.png\" mode=\"aspectFill\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 主横幅 -->\r\n\t\t<view class=\"hero-banner fade-in\">\r\n\t\t\t<view class=\"banner-bg-placeholder\"></view>\r\n\t\t\t<view class=\"banner-overlay\">\r\n\t\t\t\t<text class=\"banner-title\">传统智慧 · 现代健康</text>\r\n\t\t\t\t<text class=\"banner-subtitle\">探索药食同源的养生之道</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 功能导航 -->\r\n\t\t<view class=\"nav-section\">\r\n\t\t\t<view class=\"nav-grid\">\r\n\t\t\t\t<view class=\"nav-item fade-in card-hover\" @click=\"goToIngredients\" style=\"animation-delay: 0.1s\">\r\n\t\t\t\t\t<text class=\"nav-icon\">�</text>\r\n\t\t\t\t\t<text class=\"nav-text\">药材食材</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"nav-item fade-in card-hover\" @click=\"goToRecipe\" style=\"animation-delay: 0.2s\">\r\n\t\t\t\t\t<text class=\"nav-icon\">📋</text>\r\n\t\t\t\t\t<text class=\"nav-text\">食疗方案</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"nav-item fade-in card-hover\" @click=\"showComingSoon\" style=\"animation-delay: 0.3s\">\r\n\t\t\t\t\t<text class=\"nav-icon\">📊</text>\r\n\t\t\t\t\t<text class=\"nav-text\">营养分析</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"nav-item fade-in card-hover\" @click=\"goToTest\" style=\"animation-delay: 0.4s\">\r\n\t\t\t\t\t<text class=\"nav-icon\">💬</text>\r\n\t\t\t\t\t<text class=\"nav-text\">互动问答</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 体质测试卡片 -->\r\n\t\t<view class=\"test-section\">\r\n\t\t\t<view class=\"test-card fade-in card-hover\" @click=\"goToTest\" style=\"animation-delay: 0.5s\">\r\n\t\t\t\t<view class=\"test-content\">\r\n\t\t\t\t\t<text class=\"test-title\">不知道自己的体质？</text>\r\n\t\t\t\t\t<text class=\"test-subtitle\">3分钟体质测试，获取专属食疗推荐</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"test-button\">\r\n\t\t\t\t\t<text class=\"test-btn-text\">开始测试</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 药材与食材推荐 -->\r\n\t\t<view class=\"ingredients-section\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">药材与食材</text>\r\n\t\t\t\t<text class=\"section-more\" @click=\"goToIngredients\">查看全部 ></text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 加载状态 -->\r\n\t\t\t<view v-if=\"loading\" class=\"loading-container\">\r\n\t\t\t\t<text class=\"loading-text\">正在加载推荐药材...</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 药材网格 -->\r\n\t\t\t<view v-else class=\"ingredients-grid\">\r\n\t\t\t\t<view class=\"ingredient-card fade-in card-hover\" v-for=\"(item, index) in featuredIngredients\" :key=\"item.id\" @click=\"goToIngredientDetail(item)\" :style=\"{ 'animation-delay': (0.1 * index) + 's' }\">\r\n\t\t\t\t\t<!-- 显示真实图片，失败时显示图标 -->\r\n\t\t\t\t\t<view class=\"ingredient-image-container\">\r\n\t\t\t\t\t\t<image v-if=\"item.image && item.image !== '/static/logo.png'\"\r\n\t\t\t\t\t\t\t   class=\"ingredient-image\"\r\n\t\t\t\t\t\t\t   :src=\"item.image\"\r\n\t\t\t\t\t\t\t   mode=\"aspectFill\"\r\n\t\t\t\t\t\t\t   @error=\"onImageError(item, index)\"></image>\r\n\t\t\t\t\t\t<view v-else class=\"ingredient-icon\">\r\n\t\t\t\t\t\t\t<text class=\"icon-text\">{{ getIconText(item.name) }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"ingredient-info\">\r\n\t\t\t\t\t\t<text class=\"ingredient-name\">{{ item.name }}</text>\r\n\t\t\t\t\t\t<text class=\"ingredient-nature\">性味：{{ item.nature }}</text>\r\n\t\t\t\t\t\t<text class=\"ingredient-effect\">归经：{{ item.meridian }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 营养与功效分析 -->\r\n\t\t<view class=\"analysis-section\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">营养与功效分析</text>\r\n\t\t\t\t<text class=\"section-more\" @click=\"showComingSoon\">分析工具 ></text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 食材营养成分对比 -->\r\n\t\t\t<view class=\"nutrition-card\">\r\n\t\t\t\t<text class=\"card-title\">食材营养成分对比</text>\r\n\t\t\t\t<text class=\"card-subtitle\">蛋白质含量</text>\r\n\t\t\t\t<view class=\"chart-container\">\r\n\t\t\t\t\t<view class=\"bar-chart\">\r\n\t\t\t\t\t\t<view class=\"bar-item\">\r\n\t\t\t\t\t\t\t<view class=\"bar green\" style=\"height: 160rpx;\"></view>\r\n\t\t\t\t\t\t\t<text class=\"bar-label\">山药</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bar-item\">\r\n\t\t\t\t\t\t\t<view class=\"bar orange\" style=\"height: 120rpx;\"></view>\r\n\t\t\t\t\t\t\t<text class=\"bar-label\">莲子</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bar-item\">\r\n\t\t\t\t\t\t\t<view class=\"bar green\" style=\"height: 140rpx;\"></view>\r\n\t\t\t\t\t\t\t<text class=\"bar-label\">百合</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 食疗方案功效分析 -->\r\n\t\t\t<view class=\"efficacy-card\">\r\n\t\t\t\t<text class=\"card-title\">食疗方案功效分析</text>\r\n\t\t\t\t<view class=\"efficacy-list\">\r\n\t\t\t\t\t<view class=\"efficacy-item\">\r\n\t\t\t\t\t\t<text class=\"efficacy-name\">补气功效</text>\r\n\t\t\t\t\t\t<view class=\"progress-container\">\r\n\t\t\t\t\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t\t\t\t\t<view class=\"progress-fill\" style=\"width: 85%;\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"progress-text\">85%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"efficacy-item\">\r\n\t\t\t\t\t\t<text class=\"efficacy-name\">养血功效</text>\r\n\t\t\t\t\t\t<view class=\"progress-container\">\r\n\t\t\t\t\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t\t\t\t\t<view class=\"progress-fill\" style=\"width: 70%;\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"progress-text\">70%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"efficacy-item\">\r\n\t\t\t\t\t\t<text class=\"efficacy-name\">滋阴功效</text>\r\n\t\t\t\t\t\t<view class=\"progress-container\">\r\n\t\t\t\t\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t\t\t\t\t<view class=\"progress-fill\" style=\"width: 40%;\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"progress-text\">40%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"efficacy-item\">\r\n\t\t\t\t\t\t<text class=\"efficacy-name\">润燥功效</text>\r\n\t\t\t\t\t\t<view class=\"progress-container\">\r\n\t\t\t\t\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t\t\t\t\t<view class=\"progress-fill\" style=\"width: 60%;\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"progress-text\">60%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 底部导航 -->\r\n\t\t<view class=\"bottom-nav\">\r\n\t\t\t<view class=\"nav-item active\" @click=\"goToPage('home')\">\r\n\t\t\t\t<text class=\"nav-icon\">🏠</text>\r\n\t\t\t\t<text class=\"nav-text\">首页</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('ingredients')\">\r\n\t\t\t\t<text class=\"nav-icon\">🌿</text>\r\n\t\t\t\t<text class=\"nav-text\">药材食材</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item add-btn\">\r\n\t\t\t\t<text class=\"nav-icon add-icon\">+</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('recipe')\">\r\n\t\t\t\t<text class=\"nav-icon\">📋</text>\r\n\t\t\t\t<text class=\"nav-text\">食疗方案</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('profile')\">\r\n\t\t\t\t<text class=\"nav-icon\">👤</text>\r\n\t\t\t\t<text class=\"nav-text\">我的</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport api from '@/utils/api.js';\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '药食同源',\r\n\t\t\t\tfeaturedIngredients: [],\r\n\t\t\t\tloading: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.loadFeaturedIngredients();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 加载推荐药材食材（前4条）\r\n\t\t\tasync loadFeaturedIngredients() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.loading = true;\r\n\t\t\t\t\tconsole.log('开始加载首页推荐药材...');\r\n\r\n\t\t\t\t\tconst response = await api.getMedicinalList();\r\n\t\t\t\t\tconsole.log('API响应:', response);\r\n\r\n\t\t\t\t\tif (response && response.length > 0) {\r\n\t\t\t\t\t\t// 只取前4条数据\r\n\t\t\t\t\t\tconst ingredients = response.slice(0, 4).map(item => ({\r\n\t\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\t\tname: item.name,\r\n\t\t\t\t\t\t\tnature: this.formatProperty(item.property),\r\n\t\t\t\t\t\t\tmeridian: this.formatMeridian(item.meridian),\r\n\t\t\t\t\t\t\timage: item.avatar || '/static/logo.png',\r\n\t\t\t\t\t\t\t// 保存原始数据用于跳转详情页\r\n\t\t\t\t\t\t\toriginalData: item\r\n\t\t\t\t\t\t}));\r\n\r\n\t\t\t\t\t\tthis.featuredIngredients = ingredients;\r\n\t\t\t\t\t\tconsole.log('首页推荐药材加载成功:', this.featuredIngredients);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('没有获取到药材数据');\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加载推荐药材失败:', error);\r\n\t\t\t\t\t// 如果API失败，使用默认数据\r\n\t\t\t\t\tthis.featuredIngredients = this.getDefaultIngredients();\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 格式化性味信息\r\n\t\t\tformatProperty(property) {\r\n\t\t\t\tif (!property) return '甘、平';\r\n\t\t\t\t// 清理换行符并截取前面部分\r\n\t\t\t\tconst cleaned = property.replace(/\\r\\n/g, '').replace(/\\n/g, '').trim();\r\n\t\t\t\treturn cleaned.length > 10 ? cleaned.substring(0, 10) + '...' : cleaned;\r\n\t\t\t},\r\n\r\n\t\t\t// 格式化归经信息\r\n\t\t\tformatMeridian(meridian) {\r\n\t\t\t\tif (!meridian) return '归脾、肺经';\r\n\t\t\t\t// 清理换行符并提取归经信息\r\n\t\t\t\tconst cleaned = meridian.replace(/\\r\\n/g, '').replace(/\\n/g, '').trim();\r\n\r\n\t\t\t\t// 如果包含归经信息，直接返回\r\n\t\t\t\tif (cleaned.includes('归') && cleaned.includes('经')) {\r\n\t\t\t\t\treturn cleaned.length > 15 ? cleaned.substring(0, 15) + '...' : cleaned;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 否则根据功效推断归经\r\n\t\t\t\tif (cleaned.includes('补气') || cleaned.includes('脾')) return '归脾、肺经';\r\n\t\t\t\tif (cleaned.includes('补血') || cleaned.includes('肝')) return '归肝、心经';\r\n\t\t\t\tif (cleaned.includes('补肾') || cleaned.includes('肾')) return '归肾经';\r\n\r\n\t\t\t\treturn '归脾、肺经';\r\n\t\t\t},\r\n\r\n\t\t\t// 获取默认数据（API失败时使用）\r\n\t\t\tgetDefaultIngredients() {\r\n\t\t\t\treturn [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\tname: '人参',\r\n\t\t\t\t\t\tnature: '甘、微苦、平',\r\n\t\t\t\t\t\tmeridian: '归脾、肺、心经',\r\n\t\t\t\t\t\timage: '/static/logo.png'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\tname: '枸杞',\r\n\t\t\t\t\t\tnature: '甘、平',\r\n\t\t\t\t\t\tmeridian: '归肝、肾经',\r\n\t\t\t\t\t\timage: '/static/logo.png'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 3,\r\n\t\t\t\t\t\tname: '山药',\r\n\t\t\t\t\t\tnature: '甘、平',\r\n\t\t\t\t\t\tmeridian: '归脾、肺、肾经',\r\n\t\t\t\t\t\timage: '/static/logo.png'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 4,\r\n\t\t\t\t\t\tname: '莲子',\r\n\t\t\t\t\t\tnature: '甘、平',\r\n\t\t\t\t\t\tmeridian: '归脾、肾、心经',\r\n\t\t\t\t\t\timage: '/static/logo.png'\r\n\t\t\t\t\t}\r\n\t\t\t\t];\r\n\t\t\t},\r\n\r\n\t\t\tgoToRecipe() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/recipe/recipe'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tgoToIngredients() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/ingredients/ingredients'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tgoToProfile() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/profile/profile'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgoToIngredientDetail(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/ingredient-detail/ingredient-detail?id=${item.id}&name=${item.name}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgoToPage(page) {\r\n\t\t\t\tswitch(page) {\r\n\t\t\t\t\tcase 'home':\r\n\t\t\t\t\t\t// 当前页面，不需要跳转\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'ingredients':\r\n\t\t\t\t\t\tthis.goToIngredients();\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'recipe':\r\n\t\t\t\t\t\tthis.goToRecipe();\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'profile':\r\n\t\t\t\t\t\tthis.goToProfile();\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoToTest() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/test/test'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tshowComingSoon() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '功能开发中...',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t.container {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tmin-height: 100vh;\r\n\t\tpadding-bottom: 120rpx;\r\n\t}\r\n\r\n\t/* 顶部导航栏 */\r\n\t.header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.header-left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.leaf-icon {\r\n\t\tfont-size: 40rpx;\r\n\t\tmargin-right: 15rpx;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n\r\n\t.app-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.header-right {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 25rpx;\r\n\t}\r\n\r\n\t.search-icon, .notification-icon {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.avatar {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t/* 主横幅 */\r\n\t.hero-banner {\r\n\t\tposition: relative;\r\n\t\theight: 300rpx;\r\n\t\tmargin: 20rpx 30rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.banner-bg-placeholder {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: linear-gradient(135deg, #52c41a 0%, #73d13d 50%, #95de64 100%);\r\n\t\tbackground-image:\r\n\t\t\tradial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\r\n\t\t\tradial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\r\n\t}\r\n\r\n\t.banner-overlay {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: linear-gradient(45deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.3));\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 40rpx;\r\n\t}\r\n\r\n\t.banner-title {\r\n\t\tfont-size: 42rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #ffffff;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.banner-subtitle {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t}\r\n\r\n\t/* 功能导航 */\r\n\t.nav-section {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.nav-grid {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(4, 1fr);\r\n\t\tgap: 30rpx;\r\n\t}\r\n\r\n\t.nav-item {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 15rpx;\r\n\t\tpadding: 30rpx 20rpx;\r\n\t\ttext-align: center;\r\n\t\tbox-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.nav-icon {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 48rpx;\r\n\t\tmargin-bottom: 15rpx;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n\r\n\t.nav-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t/* 体质测试卡片样式 */\r\n\t.test-section {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.test-card {\r\n\t\tbackground: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 6rpx 24rpx rgba(76, 175, 80, 0.3);\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.test-content {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.test-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: white;\r\n\t\tfont-weight: 600;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.test-subtitle {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\tline-height: 1.4;\r\n\t}\r\n\r\n\t.test-button {\r\n\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\tborder: 2rpx solid rgba(255, 255, 255, 0.3);\r\n\t\tborder-radius: 50rpx;\r\n\t\tpadding: 16rpx 32rpx;\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t}\r\n\r\n\t.test-btn-text {\r\n\t\tcolor: white;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t/* 药材与食材推荐 */\r\n\t.ingredients-section {\r\n\t\tpadding: 0 30rpx;\r\n\t}\r\n\r\n\t.section-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.section-more {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #52c41a;\r\n\t}\r\n\r\n\t.ingredients-grid {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.ingredient-card {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 15rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.ingredient-image {\r\n\t\twidth: 100%;\r\n\t\theight: 200rpx;\r\n\t\tobject-fit: cover;\r\n\t}\r\n\r\n\t.ingredient-info {\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t.ingredient-name {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.ingredient-nature, .ingredient-effect {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 5rpx;\r\n\t}\r\n\r\n\t/* 营养与功效分析 */\r\n\t.analysis-section {\r\n\t\tpadding: 0 30rpx;\r\n\t\tmargin-top: 40rpx;\r\n\t}\r\n\r\n\t.nutrition-card, .efficacy-card {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n\t}\r\n\r\n\t.card-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.card-subtitle {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t/* 柱状图样式 */\r\n\t.chart-container {\r\n\t\tpadding: 20rpx 0;\r\n\t}\r\n\r\n\t.bar-chart {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: flex-end;\r\n\t\theight: 200rpx;\r\n\t}\r\n\r\n\t.bar-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.bar {\r\n\t\twidth: 60rpx;\r\n\t\tborder-radius: 8rpx 8rpx 0 0;\r\n\t\tmargin-bottom: 15rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.bar.green {\r\n\t\tbackground: linear-gradient(to top, #4CAF50, #66BB6A);\r\n\t}\r\n\r\n\t.bar.orange {\r\n\t\tbackground: linear-gradient(to top, #FF9800, #FFB74D);\r\n\t}\r\n\r\n\t.bar-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t/* 功效分析样式 */\r\n\t.efficacy-list {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.efficacy-item {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.efficacy-name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\twidth: 160rpx;\r\n\t}\r\n\r\n\t.progress-container {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.progress-bar {\r\n\t\tflex: 1;\r\n\t\theight: 12rpx;\r\n\t\tbackground: #f0f0f0;\r\n\t\tborder-radius: 6rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.progress-fill {\r\n\t\theight: 100%;\r\n\t\tbackground: linear-gradient(to right, #4CAF50, #66BB6A);\r\n\t\tborder-radius: 6rpx;\r\n\t\ttransition: width 0.8s ease;\r\n\t}\r\n\r\n\t.progress-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #4CAF50;\r\n\t\tfont-weight: 600;\r\n\t\twidth: 60rpx;\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t/* 底部导航 */\r\n\t.bottom-nav {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\theight: 120rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-around;\r\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.bottom-nav .nav-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx;\r\n\t\tbackground: none;\r\n\t\tbox-shadow: none;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.bottom-nav .nav-item.active .nav-icon {\r\n\t\tcolor: #52c41a;\r\n\t}\r\n\r\n\t.bottom-nav .nav-item.active .nav-text {\r\n\t\tcolor: #52c41a;\r\n\t}\r\n\r\n\t.bottom-nav .nav-icon {\r\n\t\tfont-size: 40rpx;\r\n\t\tmargin-bottom: 5rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.bottom-nav .nav-text {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.add-btn {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.add-icon {\r\n\t\tbackground: linear-gradient(45deg, #52c41a, #73d13d);\r\n\t\tcolor: #ffffff;\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: bold;\r\n\t\tbox-shadow: 0 4rpx 15rpx rgba(82, 196, 26, 0.3);\r\n\t}\r\n\r\n\t/* 动画效果 */\r\n\t.fade-in {\r\n\t\topacity: 0;\r\n\t\ttransform: translateY(30rpx);\r\n\t\tanimation: fadeInUp 0.8s ease forwards;\r\n\t}\r\n\r\n\t@keyframes fadeInUp {\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t.card-hover:active {\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n</style>\r\n", "import MiniProgramPage from 'D:/idewenjiain/BreezeBikes/Medicine-uniapp/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "mappings": ";;;;;;;;;AAmMC,IAAKA,SAAA,GAAU;EACdC,IAAA,WAAAA,KAAA,EAAO;IACN,OAAO;MACNC,KAAA,EAAO;MACPC,mBAAA,EAAqB,EAAE;MACvBC,OAAA,EAAS;IACV;EACA;EACDC,MAAA,WAAAA,OAAA,EAAS;IACR,KAAKC,uBAAA,EAAuB;EAC5B;EACDC,OAAA,EAAS;IAAA;IAEFD,uBAAA,WAAAA,wBAAA,EAA0B;MAAA,IAAAE,KAAA;MAAA,OAAAC,kBAAA,eAAAC,oBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,WAAA;QAAA,OAAAJ,oBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAE9BV,KAAA,CAAKJ,OAAA,GAAU;cACfgB,aAAA,CAAAC,KAAA,CAAAC,KAAA,wCAAY,eAAe;cAAAL,QAAA,CAAAE,IAAA;cAAA,OAEJI,SAAA,CAAAC,GAAA,CAAIC,gBAAA;YAAA;cAArBZ,QAAA,GAAAI,QAAA,CAAAS,IAAA;cACNN,aAAA,CAAAC,KAAA,CAAAC,KAAA,wCAAY,UAAUT,QAAQ;cAE9B,IAAIA,QAAA,IAAYA,QAAA,CAASc,MAAA,GAAS,GAAG;gBAE9Bb,WAAA,GAAcD,QAAA,CAASe,KAAA,CAAM,GAAG,CAAC,EAAEC,GAAA,CAAI,UAAAC,IAAA;kBAAA,OAAS;oBACrDC,EAAA,EAAID,IAAA,CAAKC,EAAA;oBACTC,IAAA,EAAMF,IAAA,CAAKE,IAAA;oBACXC,MAAA,EAAQzB,KAAA,CAAK0B,cAAA,CAAeJ,IAAA,CAAKK,QAAQ;oBACzCC,QAAA,EAAU5B,KAAA,CAAK6B,cAAA,CAAeP,IAAA,CAAKM,QAAQ;oBAC3CE,KAAA,EAAOR,IAAA,CAAKS,MAAA,IAAU;oBAAA;oBAEtBC,YAAA,EAAcV;kBACd;gBAAA,CAAC;gBAEFtB,KAAA,CAAKL,mBAAA,GAAsBW,WAAA;gBAC3BM,aAAA,CAAYC,KAAA,CAAAC,KAAA,uDAAed,KAAA,CAAKL,mBAAmB;cAAA,OAC7C;gBACNiB,aAAA,CAAAC,KAAA,CAAAC,KAAA,wCAAY,WAAW;cACxB;cAAAL,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAwB,EAAA,GAAAxB,QAAA;cAEAG,aAAA,CAAAC,KAAA,CAAAC,KAAA,0CAAc,aAAAL,QAAA,CAAAwB,EAAA,CAAkB;cAEhCjC,KAAA,CAAKL,mBAAA,GAAsBK,KAAA,CAAKkC,qBAAA;YAAA;cAAAzB,QAAA,CAAAC,IAAA;cAEhCV,KAAA,CAAKJ,OAAA,GAAU;cAAA,OAAAa,QAAA,CAAA0B,MAAA;YAAA;YAAA;cAAA,OAAA1B,QAAA,CAAA2B,IAAA;UAAA;QAAA,GAAAhC,OAAA;MAAA;IAEhB;IAAA;IAGDsB,cAAA,WAAAA,eAAeC,QAAA,EAAU;MACxB,IAAI,CAACA,QAAA,EAAU,OAAO;MAEtB,IAAMU,OAAA,GAAUV,QAAA,CAASW,OAAA,CAAQ,SAAS,EAAE,EAAEA,OAAA,CAAQ,OAAO,EAAE,EAAEC,IAAA,EAAI;MACrE,OAAOF,OAAA,CAAQlB,MAAA,GAAS,KAAKkB,OAAA,CAAQG,SAAA,CAAU,GAAG,EAAE,IAAI,QAAQH,OAAA;IAChE;IAAA;IAGDR,cAAA,WAAAA,eAAeD,QAAA,EAAU;MACxB,IAAI,CAACA,QAAA,EAAU,OAAO;MAEtB,IAAMS,OAAA,GAAUT,QAAA,CAASU,OAAA,CAAQ,SAAS,EAAE,EAAEA,OAAA,CAAQ,OAAO,EAAE,EAAEC,IAAA,EAAI;MAGrE,IAAIF,OAAA,CAAQI,QAAA,CAAS,GAAG,KAAKJ,OAAA,CAAQI,QAAA,CAAS,GAAG,GAAG;QACnD,OAAOJ,OAAA,CAAQlB,MAAA,GAAS,KAAKkB,OAAA,CAAQG,SAAA,CAAU,GAAG,EAAE,IAAI,QAAQH,OAAA;MACjE;MAGA,IAAIA,OAAA,CAAQI,QAAA,CAAS,IAAI,KAAKJ,OAAA,CAAQI,QAAA,CAAS,GAAG,GAAG,OAAO;MAC5D,IAAIJ,OAAA,CAAQI,QAAA,CAAS,IAAI,KAAKJ,OAAA,CAAQI,QAAA,CAAS,GAAG,GAAG,OAAO;MAC5D,IAAIJ,OAAA,CAAQI,QAAA,CAAS,IAAI,KAAKJ,OAAA,CAAQI,QAAA,CAAS,GAAG,GAAG,OAAO;MAE5D,OAAO;IACP;IAAA;IAGDP,qBAAA,WAAAA,sBAAA,EAAwB;MACvB,OAAO,CACN;QACCX,EAAA,EAAI;QACJC,IAAA,EAAM;QACNC,MAAA,EAAQ;QACRG,QAAA,EAAU;QACVE,KAAA,EAAO;MACP,GACD;QACCP,EAAA,EAAI;QACJC,IAAA,EAAM;QACNC,MAAA,EAAQ;QACRG,QAAA,EAAU;QACVE,KAAA,EAAO;MACP,GACD;QACCP,EAAA,EAAI;QACJC,IAAA,EAAM;QACNC,MAAA,EAAQ;QACRG,QAAA,EAAU;QACVE,KAAA,EAAO;MACP,GACD;QACCP,EAAA,EAAI;QACJC,IAAA,EAAM;QACNC,MAAA,EAAQ;QACRG,QAAA,EAAU;QACVE,KAAA,EAAO;MACR;IAED;IAEDY,UAAA,WAAAA,WAAA,EAAa;MACZ9B,aAAA,CAAAC,KAAA,CAAI8B,UAAA,CAAW;QACdC,GAAA,EAAK;MACN,CAAC;IACD;IAEDC,eAAA,WAAAA,gBAAA,EAAkB;MACjBjC,aAAA,CAAAC,KAAA,CAAI8B,UAAA,CAAW;QACdC,GAAA,EAAK;MACN,CAAC;IACD;IAEDE,WAAA,WAAAA,YAAA,EAAc;MACblC,aAAA,CAAAC,KAAA,CAAI8B,UAAA,CAAW;QACdC,GAAA,EAAK;MACN,CAAC;IACD;IACDG,oBAAA,WAAAA,qBAAqBzB,IAAA,EAAM;MAC1BV,aAAA,CAAAC,KAAA,CAAI8B,UAAA,CAAW;QACdC,GAAA,mDAAAI,MAAA,CAAsD1B,IAAA,CAAKC,EAAE,YAAAyB,MAAA,CAAS1B,IAAA,CAAKE,IAAI;MAChF,CAAC;IACD;IACDyB,QAAA,WAAAA,SAASC,IAAA,EAAM;MACd,QAAOA,IAAA;QACN,KAAK;UAEJ;QACD,KAAK;UACJ,KAAKL,eAAA,EAAe;UACpB;QACD,KAAK;UACJ,KAAKH,UAAA,EAAU;UACf;QACD,KAAK;UACJ,KAAKI,WAAA,EAAW;UAChB;MAAA;IAEF;IACDK,QAAA,WAAAA,SAAA,EAAW;MACVvC,aAAA,CAAAC,KAAA,CAAI8B,UAAA,CAAW;QACdC,GAAA,EAAK;MACN,CAAC;IACD;IAEDQ,cAAA,WAAAA,eAAA,EAAiB;MAChBxC,aAAA,CAAAC,KAAA,CAAIwC,SAAA,CAAU;QACb3D,KAAA,EAAO;QACP4D,IAAA,EAAM;MACP,CAAC;IACF;EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjWDC,EAAA,CAAGC,UAAA,CAAWC,eAAe", "ignoreList": []}