<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>轮播图API测试工具</h1>
        
        <!-- 直接API测试 -->
        <div class="test-section">
            <div class="test-title">1. 直接API测试</div>
            <p>直接调用后端API，不通过网关</p>
            <button class="test-button" onclick="testDirectAPI()">测试 http://localhost:1000/carousel/list</button>
            <div id="direct-result" class="result info">点击按钮开始测试...</div>
        </div>
        
        <!-- 网关API测试 -->
        <div class="test-section">
            <div class="test-title">2. 网关API测试</div>
            <p>通过网关调用API</p>
            <button class="test-button" onclick="testGatewayAPI()">测试 http://localhost:8083/carousel/list</button>
            <div id="gateway-result" class="result info">点击按钮开始测试...</div>
        </div>
        
        <!-- 数据格式分析 -->
        <div class="test-section">
            <div class="test-title">3. 数据格式分析</div>
            <p>分析返回的数据格式和结构</p>
            <button class="test-button" onclick="analyzeDataFormat()">分析数据格式</button>
            <div id="format-result" class="result info">点击按钮开始分析...</div>
        </div>
        
        <!-- 前端模拟测试 -->
        <div class="test-section">
            <div class="test-title">4. 前端模拟测试</div>
            <p>模拟前端uni.request调用</p>
            <button class="test-button" onclick="testFrontendCall()">模拟前端调用</button>
            <div id="frontend-result" class="result info">点击按钮开始测试...</div>
        </div>
    </div>

    <script>
        // 1. 直接API测试
        async function testDirectAPI() {
            const resultDiv = document.getElementById('direct-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试直接API调用...';
            
            try {
                const response = await fetch('http://localhost:1000/carousel/list', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 直接API调用成功！
状态码: ${response.status}
响应头: ${JSON.stringify(Object.fromEntries(response.headers), null, 2)}
响应数据: ${JSON.stringify(data, null, 2)}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 直接API调用失败！
错误信息: ${error.message}
错误详情: ${JSON.stringify(error, null, 2)}`;
            }
        }
        
        // 2. 网关API测试
        async function testGatewayAPI() {
            const resultDiv = document.getElementById('gateway-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试网关API调用...';
            
            try {
                const response = await fetch('http://localhost:8083/carousel/list', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 网关API调用成功！
状态码: ${response.status}
响应头: ${JSON.stringify(Object.fromEntries(response.headers), null, 2)}
响应数据: ${JSON.stringify(data, null, 2)}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网关API调用失败！
错误信息: ${error.message}
错误详情: ${JSON.stringify(error, null, 2)}`;
            }
        }
        
        // 3. 数据格式分析
        async function analyzeDataFormat() {
            const resultDiv = document.getElementById('format-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在分析数据格式...';
            
            try {
                // 先尝试网关
                let response, data;
                try {
                    response = await fetch('http://localhost:8083/carousel/list');
                    data = await response.json();
                } catch (gatewayError) {
                    // 如果网关失败，尝试直接调用
                    response = await fetch('http://localhost:1000/carousel/list');
                    data = await response.json();
                }
                
                let analysis = `📊 数据格式分析结果：

数据类型: ${typeof data}
是否为数组: ${Array.isArray(data)}
数据长度: ${Array.isArray(data) ? data.length : '不是数组'}

`;
                
                if (data && typeof data === 'object') {
                    analysis += `对象属性: ${Object.keys(data).join(', ')}

`;
                    
                    // 检查是否是Result格式
                    if (data.hasOwnProperty('code') && data.hasOwnProperty('data')) {
                        analysis += `✅ 检测到Result格式:
- code: ${data.code}
- message: ${data.message || '无'}
- data类型: ${typeof data.data}
- data是否为数组: ${Array.isArray(data.data)}
- data长度: ${Array.isArray(data.data) ? data.data.length : '不是数组'}

`;
                        
                        if (Array.isArray(data.data) && data.data.length > 0) {
                            analysis += `第一条数据结构:
${JSON.stringify(data.data[0], null, 2)}`;
                        }
                    } else if (Array.isArray(data)) {
                        analysis += `✅ 检测到直接数组格式:
数组长度: ${data.length}

`;
                        if (data.length > 0) {
                            analysis += `第一条数据结构:
${JSON.stringify(data[0], null, 2)}`;
                        }
                    } else {
                        analysis += `❓ 未知数据格式:
${JSON.stringify(data, null, 2)}`;
                    }
                }
                
                resultDiv.className = 'result success';
                resultDiv.textContent = analysis;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 数据格式分析失败！
错误信息: ${error.message}`;
            }
        }
        
        // 4. 前端模拟测试
        async function testFrontendCall() {
            const resultDiv = document.getElementById('frontend-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在模拟前端调用...';
            
            try {
                // 模拟uni.request的调用方式
                const response = await fetch('http://localhost:8083/carousel/list', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                if (response.status === 200) {
                    const data = await response.json();
                    
                    // 模拟前端的数据处理逻辑
                    let carouselList = [];
                    let processLog = '';
                    
                    if (data && data.code === 200 && data.data && Array.isArray(data.data)) {
                        processLog += '✅ 检测到标准Result格式\n';
                        carouselList = data.data.filter(item => item.status === 1);
                        processLog += `过滤后的轮播图数量: ${carouselList.length}\n`;
                    } else if (Array.isArray(data)) {
                        processLog += '✅ 检测到直接数组格式\n';
                        carouselList = data.filter(item => item.status === 1);
                        processLog += `过滤后的轮播图数量: ${carouselList.length}\n`;
                    } else {
                        processLog += '❌ 未知数据格式\n';
                    }
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 前端模拟调用成功！

${processLog}
最终轮播图数据:
${JSON.stringify(carouselList, null, 2)}`;
                    
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 前端模拟调用失败！
错误信息: ${error.message}`;
            }
        }
    </script>
</body>
</html>
