<template>
	<view class="container">
		<view class="header">
			<text class="title">API测试页面</text>
		</view>
		
		<view class="info-section">
			<text class="info-title">当前配置信息</text>
			<text class="info-item">BASE_URL: {{ baseUrl }}</text>
			<text class="info-item">当前页面: {{ currentUrl }}</text>
		</view>
		
		<view class="test-section">
			<button class="test-btn" @click="testAPI" :disabled="loading">
				{{ loading ? '测试中...' : '测试API连接' }}
			</button>
		</view>
		
		<view class="result-section" v-if="result">
			<text class="result-title">测试结果</text>
			<text class="result-content">{{ result }}</text>
		</view>
		
		<view class="log-section" v-if="logs.length > 0">
			<text class="log-title">日志信息</text>
			<text class="log-item" v-for="(log, index) in logs" :key="index">{{ log }}</text>
		</view>
	</view>
</template>

<script>
import api from '@/utils/api.js';
import config from '@/utils/config.js';

export default {
	data() {
		return {
			loading: false,
			result: '',
			logs: [],
			baseUrl: config.baseURL,
			currentUrl: ''
		}
	},
	onLoad() {
		this.currentUrl = typeof window !== 'undefined' && window.location ? window.location.href : '未知';
		this.addLog('页面加载完成');
		this.addLog(`BASE_URL: ${this.baseUrl}`);
		this.addLog(`当前页面: ${this.currentUrl}`);
	},
	methods: {
		addLog(message) {
			const timestamp = new Date().toLocaleTimeString();
			this.logs.push(`[${timestamp}] ${message}`);
		},
		
		async testAPI() {
			this.loading = true;
			this.result = '';
			this.addLog('开始测试API...');
			
			try {
				this.addLog('调用 api.getMedicinalList()');
				const response = await api.getMedicinalList();
				this.addLog('API调用成功');
				this.result = `成功！获取到 ${response.length} 条数据`;
				this.addLog(`响应数据: ${JSON.stringify(response.slice(0, 2))}`);
			} catch (error) {
				this.addLog('API调用失败');
				this.result = `失败：${error.message || error}`;
				this.addLog(`错误详情: ${JSON.stringify(error)}`);
			} finally {
				this.loading = false;
			}
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.info-section, .test-section, .result-section, .log-section {
	background: white;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.info-title, .result-title, .log-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.info-item, .result-content, .log-item {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
	display: block;
	word-break: break-all;
}

.test-btn {
	width: 100%;
	background: #52c41a;
	color: white;
	border: none;
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 32rpx;
}

.test-btn:disabled {
	background: #ccc;
}

.log-item {
	font-family: monospace;
	font-size: 24rpx;
	background: #f8f8f8;
	padding: 10rpx;
	margin-bottom: 5rpx;
	border-radius: 5rpx;
}
</style>
