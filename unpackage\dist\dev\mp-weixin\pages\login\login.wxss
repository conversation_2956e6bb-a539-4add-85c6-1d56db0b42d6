
.login-container.data-v-e4e4508d {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 60rpx 40rpx 40rpx;
		display: flex;
		flex-direction: column;
}
.login-header.data-v-e4e4508d {
		text-align: center;
		margin-bottom: 80rpx;
}
.logo.data-v-e4e4508d {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
}
.app-name.data-v-e4e4508d {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: white;
		margin-bottom: 10rpx;
}
.welcome-text.data-v-e4e4508d {
		display: block;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
}
.login-form.data-v-e4e4508d {
		flex: 1;
}
.input-group.data-v-e4e4508d {
		margin-bottom: 60rpx;
}
.input-item.data-v-e4e4508d {
		background: rgba(255, 255, 255, 0.9);
		border-radius: 50rpx;
		padding: 0 30rpx;
		margin-bottom: 30rpx;
		display: flex;
		align-items: center;
		height: 100rpx;
}
.input-icon.data-v-e4e4508d {
		font-size: 32rpx;
		margin-right: 20rpx;
		width: 40rpx;
		text-align: center;
}
.input-field.data-v-e4e4508d {
		flex: 1;
		font-size: 32rpx;
		color: #333;
}
.password-toggle.data-v-e4e4508d {
		font-size: 32rpx;
		padding: 10rpx;
		color: #666;
}
.login-btn.data-v-e4e4508d {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(45deg, #4CAF50, #45a049);
		color: white;
		border: none;
		border-radius: 50rpx;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 40rpx;
}
.login-btn.data-v-e4e4508d:disabled {
		background: #ccc;
		color: #999;
}
.login-options.data-v-e4e4508d {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.register-link.data-v-e4e4508d,
	.forgot-link.data-v-e4e4508d {
		color: rgba(255, 255, 255, 0.9);
		font-size: 28rpx;
}
.login-footer.data-v-e4e4508d {
		text-align: center;
		margin-top: 60rpx;
}
.agreement-text.data-v-e4e4508d {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
		line-height: 1.5;
}
.link-text.data-v-e4e4508d {
		color: rgba(255, 255, 255, 0.9);
		text-decoration: underline;
}
