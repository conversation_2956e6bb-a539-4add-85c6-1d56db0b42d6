// API配置文件
import config from './config.js';

const BASE_URL = config.baseURL;
console.log('API BASE_URL:', BASE_URL);

// 通用请求方法
const request = (options) => {
	return new Promise((resolve, reject) => {
		const fullUrl = BASE_URL + options.url;
		console.log('发起请求:', fullUrl);
		console.log('请求参数:', options);

		uni.request({
			url: fullUrl,
			method: options.method || 'GET',
			data: options.data || {},
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				...options.header
			},
			timeout: 15000, // 15秒超时
			success: (res) => {
				console.log('请求成功:', res);
				if (res.statusCode === 200) {
					resolve(res.data);
				} else {
					console.error('请求失败:', res);
					reject(new Error(`请求失败: ${res.statusCode} - ${res.errMsg || '未知错误'}`));
				}
			},
			fail: (err) => {
				console.error('请求错误:', err);
				reject(new Error(`网络错误: ${err.errMsg || '请求失败'}`));
			}
		});
	});
};

// API接口定义
const api = {
	// 获取药材食材列表
	getMedicinalList() {
		return request({
			url: '/medicinal/medicinalList',
			method: 'GET'
		});
	},

	// 根据ID获取药材详情
	getMedicinalById(id) {
		return request({
			url: `/medicinal/medicinalById?id=${id}`,
			method: 'GET'
		});
	},

	// 获取药材食材分类列表
	getTypeList() {
		return request({
			url: '/medicinal/TypeList',
			method: 'GET'
		});
	},

	// 根据分类ID获取药材列表
	getMedicinalByTypeId(typeId) {
		return request({
			url: `/medicinal/medicinalTypeId?id=${typeId}`,
			method: 'GET'
		});
	},

	// 根据名称搜索药材
	getMedicinalByName(name) {
		console.log('API调用 - 搜索药材，原始关键词:', name);
		const encodedName = encodeURIComponent(name);
		console.log('API调用 - 编码后的关键词:', encodedName);
		const url = `/medicinal/medicinalByName?name=${encodedName}`;
		console.log('API调用 - 完整URL:', url);

		return request({
			url: url,
			method: 'GET'
		});
	},

	// 获取轮播图列表
	getCarouselList() {
		return request({
			url: '/carousel/list',
			method: 'GET'
		});
	}
};

export default api;
