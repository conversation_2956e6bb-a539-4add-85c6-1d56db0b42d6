
	/*每个页面公共css */

	/* 全局动画 */
.fade-in {
		animation: fadeIn 0.3s ease-in-out;
}
@keyframes fadeIn {
from {
			opacity: 0;
			transform: translateY(20rpx);
}
to {
			opacity: 1;
			transform: translateY(0);
}
}
.scale-in {
		animation: scaleIn 0.2s ease-out;
}
@keyframes scaleIn {
from {
			transform: scale(0.9);
			opacity: 0;
}
to {
			transform: scale(1);
			opacity: 1;
}
}

	/* 全局按钮样式 */
.btn-hover {
		transition: all 0.2s ease;
}
.btn-hover:active {
		transform: scale(0.95);
		opacity: 0.8;
}

	/* 全局卡片样式 */
.card-hover {
		transition: all 0.3s ease;
}
.card-hover:active {
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

	/* 全局文字样式 */
.text-primary {
		color: #4CAF50;
}
.text-secondary {
		color: #666;
}
.text-muted {
		color: #999;
}

	/* 全局间距 */
.mt-10 { margin-top: 10rpx;
}
.mt-20 { margin-top: 20rpx;
}
.mt-30 { margin-top: 30rpx;
}
.mb-10 { margin-bottom: 10rpx;
}
.mb-20 { margin-bottom: 20rpx;
}
.mb-30 { margin-bottom: 30rpx;
}
.p-20 { padding: 20rpx;
}
.p-30 { padding: 30rpx;
}
.p-40 { padding: 40rpx;
}
page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}