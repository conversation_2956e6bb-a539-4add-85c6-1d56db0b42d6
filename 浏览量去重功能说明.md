# 浏览量去重功能实现说明

## 功能概述

实现了食疗方案浏览量的用户去重功能，确保每个用户对每个食疗方案只能贡献1次浏览量，避免重复计算。

## 核心改进

### 1. 数据库层面

#### 新增用户浏览记录表
```sql
CREATE TABLE `userviewlog` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `userId` int NOT NULL COMMENT '用户ID',
  `planId` int NOT NULL COMMENT '食疗方案ID',
  `viewTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '浏览时间',
  `createTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_user_plan`(`userId` ASC, `planId` ASC) USING BTREE COMMENT '用户和方案的唯一索引，确保每个用户对每个方案只记录一次浏览',
  INDEX `idx_userId`(`userId` ASC) USING BTREE,
  INDEX `idx_planId`(`planId` ASC) USING BTREE,
  INDEX `idx_viewTime`(`viewTime` ASC) USING BTREE,
  CONSTRAINT `userviewlog_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `userviewlog_ibfk_2` FOREIGN KEY (`planId`) REFERENCES `diettherapyplan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户浏览记录表' ROW_FORMAT = Dynamic;
```

**关键特性**：
- 使用 `UNIQUE INDEX unique_user_plan` 确保同一用户对同一方案只能有一条记录
- 外键约束保证数据完整性
- 索引优化查询性能

### 2. 后端实现

#### 新增实体类和Mapper
- ✅ `common/src/main/java/com/zhentao/pojo/Userviewlog.java` - 浏览记录实体
- ✅ `common/src/main/java/com/zhentao/mapper/UserviewlogMapper.java` - 浏览记录Mapper

#### 修改Service接口和实现
**接口变更**：
```java
// 原来的方法
void incrementViewCount(Integer planId);

// 新的方法
Boolean incrementViewCount(Integer planId, Integer userId);
```

**实现逻辑**：
1. 检查用户是否已浏览过该方案
2. 如果未浏览过：
   - 插入浏览记录
   - 增加方案浏览量
   - 返回 `true`（首次浏览）
3. 如果已浏览过：
   - 返回 `false`（不重复计算）
4. 处理并发冲突：使用唯一索引约束防止重复插入

#### 修改Controller接口
**接口变更**：
```java
// 新的接口需要用户ID
@PostMapping("/view/{planId}")
public Result incrementViewCount(@PathVariable Integer planId, @RequestBody Map<String, Object> request)
```

**响应格式**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "isFirstView": true,
    "message": "首次浏览，浏览量+1"
  }
}
```

### 3. 前端适配

#### API调用修改
```javascript
// 原来的调用
await incrementViewCount(item.id);

// 新的调用
const response = await incrementViewCount(item.id, this.userId);
```

#### 用户反馈
- 在控制台输出浏览量统计结果
- 区分首次浏览和重复浏览

## 技术亮点

### 1. 数据一致性保证
- **数据库层面**：使用唯一索引约束
- **应用层面**：事务控制和异常处理
- **并发安全**：处理并发插入冲突

### 2. 性能优化
- 使用索引加速查询
- 单次数据库交互完成检查和插入
- 避免不必要的重复操作

### 3. 用户体验
- 浏览量统计失败不影响页面功能
- 提供清晰的日志输出
- 前端无感知的功能增强

## 测试场景

### 1. 正常流程测试
1. 用户首次浏览方案
   - ✅ 应记录浏览记录
   - ✅ 应增加方案浏览量
   - ✅ 返回 `isFirstView: true`

2. 用户重复浏览方案
   - ✅ 不应增加浏览量
   - ✅ 返回 `isFirstView: false`

### 2. 边界情况测试
1. 并发浏览测试
   - 同一用户同时多次浏览同一方案
   - 应只记录一次浏览量

2. 不同用户浏览同一方案
   - 每个用户都应贡献浏览量
   - 总浏览量 = 浏览过的用户数

3. 数据库异常处理
   - 唯一索引冲突
   - 外键约束异常

### 3. 性能测试
1. 大量用户并发浏览
2. 浏览记录表数据量增长情况
3. 查询性能测试

## 部署说明

### 1. 数据库更新
执行以下SQL创建新表：
```sql
-- 在 breezy 数据库中执行
USE breezy;

-- 创建用户浏览记录表
DROP TABLE IF EXISTS `userviewlog`;
CREATE TABLE `userviewlog` ( ... );
```

### 2. 应用重启
- 重启 Order_demo 服务
- 重启 Gateway 服务
- 清除前端缓存并重新加载

### 3. 验证部署
```bash
# 1. 检查服务状态
curl -X GET "http://localhost:8083/test/health"

# 2. 测试浏览量接口
curl -X POST "http://localhost:8083/recipe/view/1" \
  -H "Content-Type: application/json" \
  -d '{"userId": 1}'

# 3. 检查数据库记录
SELECT * FROM userviewlog WHERE userId = 1 AND planId = 1;
SELECT view_count FROM diettherapyplan WHERE id = 1;
```

## 注意事项

1. **用户ID获取**：目前使用固定用户ID(1)，生产环境需从登录状态获取
2. **数据清理**：考虑定期清理过期的浏览记录
3. **监控报警**：监控浏览记录表的增长速度
4. **缓存策略**：可考虑缓存用户浏览状态以提升性能

## 后续优化建议

1. **用户认证集成**：与用户登录系统集成获取真实用户ID
2. **缓存优化**：使用Redis缓存用户浏览状态
3. **数据分析**：基于浏览记录进行用户行为分析
4. **推荐系统**：基于浏览历史推荐相关方案 