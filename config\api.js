/**
 * API配置文件
 * 管理不同环境的API地址
 */

// 环境配置
const ENV = {
  // 开发环境
  development: {
    baseUrl: 'http://localhost:8083',
    timeout: 10000
  },
  // 生产环境
  production: {
    baseUrl: 'http://localhost:8083',
    timeout: 15000
  }
};

// 当前环境（可以根据实际部署情况调整）
const currentEnv = 'development';

// 导出配置
export const API_CONFIG = ENV[currentEnv];

// 接口路径配置
export const API_PATHS = {
  // 食疗方案相关
  RECIPE: {
    LIST: '/recipe/list',
    DETAIL: '/recipe/detail',
    FAVORITE: '/recipe/favorite',
    VIEW_COUNT: '/recipe/view',
    SEARCH_LOG: '/recipe/search-log',
    LIKE: '/recipe/like',
    BATCH_LIKE_STATUS: '/recipe/like/batch-status'
  },
  // 测试接口
  TEST: {
    HEALTH: '/test/health'
  }
}; 