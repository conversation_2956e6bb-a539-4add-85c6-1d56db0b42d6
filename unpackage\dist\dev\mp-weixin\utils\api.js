"use strict";
const common_vendor = require("../common/vendor.js");
const utils_config = require("./config.js");
const BASE_URL = utils_config.config.baseURL;
common_vendor.index.__f__("log", "at utils/api.js:5", "API BASE_URL:", BASE_URL);
const request = (options) => {
  return new Promise((resolve, reject) => {
    const fullUrl = BASE_URL + options.url;
    common_vendor.index.__f__("log", "at utils/api.js:11", "发起请求:", fullUrl);
    common_vendor.index.__f__("log", "at utils/api.js:12", "请求参数:", options);
    common_vendor.index.request({
      url: fullUrl,
      method: options.method || "GET",
      data: options.data || {},
      header: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        ...options.header
      },
      timeout: 15e3,
      // 15秒超时
      success: (res) => {
        common_vendor.index.__f__("log", "at utils/api.js:25", "请求成功:", res);
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          common_vendor.index.__f__("error", "at utils/api.js:29", "请求失败:", res);
          reject(new Error(`请求失败: ${res.statusCode} - ${res.errMsg || "未知错误"}`));
        }
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at utils/api.js:34", "请求错误:", err);
        reject(new Error(`网络错误: ${err.errMsg || "请求失败"}`));
      }
    });
  });
};
const api = {
  // 获取药材食材列表
  getMedicinalList() {
    return request({
      url: "/medicinal/medicinalList",
      method: "GET"
    });
  },
  // 根据ID获取药材详情
  getMedicinalById(id) {
    return request({
      url: `/medicinal/medicinalById?id=${id}`,
      method: "GET"
    });
  },
  // 获取药材食材分类列表
  getTypeList() {
    return request({
      url: "/medicinal/TypeList",
      method: "GET"
    });
  },
  // 根据分类ID获取药材列表
  getMedicinalByTypeId(typeId) {
    return request({
      url: `/medicinal/medicinalTypeId?id=${typeId}`,
      method: "GET"
    });
  },
  // 根据名称搜索药材
  getMedicinalByName(name) {
    common_vendor.index.__f__("log", "at utils/api.js:77", "API调用 - 搜索药材，原始关键词:", name);
    const encodedName = encodeURIComponent(name);
    common_vendor.index.__f__("log", "at utils/api.js:79", "API调用 - 编码后的关键词:", encodedName);
    const url = `/medicinal/medicinalByName?name=${encodedName}`;
    common_vendor.index.__f__("log", "at utils/api.js:81", "API调用 - 完整URL:", url);
    return request({
      url,
      method: "GET"
    });
  },
  // 获取轮播图列表
  getCarouselList() {
    return request({
      url: "/carousel/list",
      method: "GET"
    });
  }
};
exports.api = api;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/api.js.map
