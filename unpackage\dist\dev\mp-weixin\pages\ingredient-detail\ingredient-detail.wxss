
.container.data-v-4c08f3f1 {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container.data-v-4c08f3f1 {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}
.loading-text.data-v-4c08f3f1 {
	color: #666;
	font-size: 28rpx;
}

/* 药材头部信息 */
.ingredient-header.data-v-4c08f3f1 {
	background-color: #fff;
	padding: 40rpx;
	display: flex;
	align-items: flex-start;
	gap: 30rpx;
}
.ingredient-image-container.data-v-4c08f3f1 {
	width: 160rpx;
	height: 160rpx;
	border-radius: 20rpx;
	overflow: hidden;
}
.ingredient-image.data-v-4c08f3f1 {
	width: 100%;
	height: 100%;
	border-radius: 20rpx;
}
.ingredient-icon.data-v-4c08f3f1 {
	width: 160rpx;
	height: 160rpx;
	border-radius: 20rpx;
	background-color: #4CAF50;
	display: flex;
	align-items: center;
	justify-content: center;
}
.icon-text.data-v-4c08f3f1 {
	color: white;
	font-size: 60rpx;
	font-weight: bold;
}
.header-info.data-v-4c08f3f1 {
	flex: 1;
}
.ingredient-name.data-v-4c08f3f1 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}
.ingredient-alias.data-v-4c08f3f1 {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 20rpx;
}
.ingredient-tags.data-v-4c08f3f1 {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
}
.tag.data-v-4c08f3f1 {
	background-color: #E8F5E8;
	color: #4CAF50;
	font-size: 22rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}
.ingredient-meta.data-v-4c08f3f1 {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	margin-top: -20rpx; /* 向上移动类型区域 */
}
.meta-item.data-v-4c08f3f1 {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.meta-label.data-v-4c08f3f1 {
	font-size: 22rpx;
	color: #999;
	margin-bottom: 5rpx;
}
.meta-value.data-v-4c08f3f1 {
	font-size: 24rpx;
	color: #333;
	background-color: #f5f5f5;
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
}

/* 详细内容 */
.detail-content.data-v-4c08f3f1 {
	margin-top: 20rpx;
}
.section.data-v-4c08f3f1 {
	background-color: #fff;
	padding: 40rpx;
	margin-bottom: 20rpx;
}
.section-title.data-v-4c08f3f1 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}
.section-content.data-v-4c08f3f1 {
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
	white-space: pre-line;
}

/* 相关食疗方案 */
.related-recipes.data-v-4c08f3f1 {
	margin-top: 20rpx;
}
.recipe-item.data-v-4c08f3f1 {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}
.recipe-item.data-v-4c08f3f1:last-child {
	border-bottom: none;
}
.recipe-thumb.data-v-4c08f3f1 {
	width: 80rpx;
	height: 80rpx;
	border-radius: 10rpx;
	margin-right: 20rpx;
}
.recipe-info.data-v-4c08f3f1 {
	flex: 1;
}
.recipe-name.data-v-4c08f3f1 {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}
.recipe-effect.data-v-4c08f3f1 {
	font-size: 24rpx;
	color: #666;
}
.arrow.data-v-4c08f3f1 {
	font-size: 28rpx;
	color: #ccc;
}

/* 底部操作按钮 */
.bottom-actions.data-v-4c08f3f1 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	border-top: 1rpx solid #eee;
}
.action-btn.data-v-4c08f3f1 {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
}
.action-btn.primary.data-v-4c08f3f1 {
	background-color: #4CAF50;
	color: #fff;
}
.action-btn.secondary.data-v-4c08f3f1 {
	background-color: #f5f5f5;
	color: #666;
}
