"use strict";
const common_vendor = require("../common/vendor.js");
const config_api = require("../config/api.js");
function request(options) {
  return new Promise((resolve, reject) => {
    common_vendor.index.request({
      url: config_api.API_CONFIG.baseUrl + options.url,
      method: options.method || "GET",
      data: options.data || {},
      header: {
        "Content-Type": "application/json",
        ...options.header
      },
      timeout: config_api.API_CONFIG.timeout,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}
function getRecipeList(params = {}) {
  common_vendor.index.__f__("log", "at api/recipe.js:49", "getRecipeList - 原始参数:", params);
  const queryString = Object.keys(params).filter((key) => params[key] !== void 0 && params[key] !== null && params[key] !== "").map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join("&");
  const url = queryString ? `${config_api.API_PATHS.RECIPE.LIST}?${queryString}` : config_api.API_PATHS.RECIPE.LIST;
  common_vendor.index.__f__("log", "at api/recipe.js:59", "getRecipeList - 最终URL:", config_api.API_CONFIG.baseUrl + url);
  return request({
    url,
    method: "GET"
  });
}
function toggleFavorite(data) {
  return request({
    url: config_api.API_PATHS.RECIPE.FAVORITE,
    method: "POST",
    data
  });
}
function incrementViewCount(planId, userId) {
  return request({
    url: `${config_api.API_PATHS.RECIPE.VIEW_COUNT}/${planId}`,
    method: "POST",
    data: {
      userId
    }
  });
}
function recordSearchLog(data) {
  return request({
    url: config_api.API_PATHS.RECIPE.SEARCH_LOG,
    method: "POST",
    data
  });
}
function getRecipeDetail(planId) {
  return request({
    url: `${config_api.API_PATHS.RECIPE.DETAIL}/${planId}`,
    method: "GET"
  });
}
function toggleLike(planId, userId) {
  return request({
    url: `${config_api.API_PATHS.RECIPE.LIKE}/${planId}`,
    method: "POST",
    data: {
      userId
    }
  });
}
function getBatchLikeStatus(userId, planIds) {
  return request({
    url: config_api.API_PATHS.RECIPE.BATCH_LIKE_STATUS,
    method: "POST",
    data: {
      userId,
      planIds
    }
  });
}
exports.getBatchLikeStatus = getBatchLikeStatus;
exports.getRecipeDetail = getRecipeDetail;
exports.getRecipeList = getRecipeList;
exports.incrementViewCount = incrementViewCount;
exports.recordSearchLog = recordSearchLog;
exports.toggleFavorite = toggleFavorite;
exports.toggleLike = toggleLike;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/recipe.js.map
