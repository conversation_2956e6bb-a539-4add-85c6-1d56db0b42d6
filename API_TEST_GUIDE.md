# 药材食材API连接测试指南 - 最终版本

## 最新修改内容总结

### 前端字段映射修改
- **重要变更**: 前端现在使用`meridian`字段替代`property`字段来显示药材的主要功效
- **显示效果**: 红框中的字段现在显示为"补气"、"增强免疫"等功效关键词
- **数据处理**: 新增`formatMeridian()`方法，智能提取功效关键词

## 修改内容总结

### 1. 网关配置修改
- 文件：`gateway/src/main/resources/application.yml`
- 启用服务发现：`spring.cloud.gateway.discovery.locator.enabled: true`
- 文件：`gateway/src/main/java/com/zhentao/filter/GatewayRoutes.java`
- 注释掉静态路由，使用服务发现自动路由

### 2. 后端服务配置
- 文件：`service/Nearby/src/main/resources/application.yml`
- 服务名：`Nearby`，端口：`1003`
- 注册到Nacos服务发现

### 3. 前端API配置
- 新增文件：`Medicine-uniapp/utils/api.js`
- 配置网关地址：`http://localhost:8083`
- 使用直接路由路径：`/medicinal/**`（直接映射到后端）

### 4. 前端页面修改
- 文件：`Medicine-uniapp/pages/ingredients/ingredients.vue`
- 从静态数据改为API调用
- 添加加载状态显示
- 数据格式转换，保持与UI设计一致
- **新增**: 使用`meridian`字段替代`property`字段
- **新增**: `formatMeridian()`方法智能提取功效关键词

### 5. CORS配置优化
- 网关层统一处理CORS，避免重复头问题
- 移除后端服务的CORS配置
- 移除路由层的@CrossOrigin注解

## API接口说明

### 获取药材列表
- **接口路径**：`GET /medicinal/medicinalList`
- **完整URL**：`http://localhost:8083/medicinal/medicinalList`
- **后端实际路径**：`http://localhost:1003/medicinal/medicinalList`
- **返回格式**：直接返回数组

### 获取分类列表
- **接口路径**：`GET /medicinal/TypeList`
- **完整URL**：`http://localhost:8083/medicinal/TypeList`
- **后端实际路径**：`http://localhost:1003/medicinal/TypeList`
- **返回格式**：Result包装格式

### 根据分类获取药材
- **接口路径**：`GET /medicinal/medicinalTypeId?id={typeId}`
- **完整URL**：`http://localhost:8083/medicinal/medicinalTypeId?id=1`
- **后端实际路径**：`http://localhost:1003/medicinal/medicinalTypeId?id=1`
- **返回格式**：Result包装格式

### 获取药材详情
- **接口路径**：`GET /medicinal/medicinalById?id={id}`
- **完整URL**：`http://localhost:8083/medicinal/medicinalById?id=1`
- **后端实际路径**：`http://localhost:1003/medicinal/medicinalById?id=1`

## 字段映射变化

### 前端显示字段映射
- **name**: 药材名称 (使用`item.name`)
- **alias**: 别名 (使用`item.rname`)
- **mainEffect**: 主要功效 (使用`item.efficacy`)
- **type**: 类型 (使用`item.name1`)
- **nature**: 红框显示字段 (使用`item.meridian` - **重要变更**)
- **image**: 图片 (使用`item.avatar`)

### formatMeridian()方法处理逻辑
```javascript
// 优先匹配具体功效关键词
if (meridian.includes('补气')) return '补气';
if (meridian.includes('增强免疫')) return '增强免疫';
if (meridian.includes('抗疲劳')) return '抗疲劳';
// 根据归经推断功效
if (meridian.includes('脾')) return '健脾';
if (meridian.includes('肝')) return '养肝';
// 默认返回'补气'
```

## 数据格式转换

### 后端返回格式（实际）
```json
[
  {
    "id": 1,
    "name": "黄芪",
    "rname": "北芪、绵芪、黄耆",
    "efficacy": "补气固表，利尿托毒，敛疮生肌",
    "property": "甘，微温，甘",
    "meridian": "补气\r\n增强免疫\r\n抗疲劳\r\n利尿",
    "avatar": "http://*************:9000/test/黄芪.jpg",
    "name1": "中药材",
    "categoryid": 1
  }
]
```

### 前端显示格式（转换后）
```json
{
  "id": 1,
  "name": "黄芪",
  "alias": "北芪、绵芪、黄耆",
  "mainEffect": "补气固表，利尿托毒，敛疮生肌",
  "type": "中药材",
  "nature": "补气",  // 使用meridian字段，经formatMeridian()处理
  "category": "herb",
  "subCategory": "tonify",
  "image": "http://*************:9000/test/黄芪.jpg",
  "tags": ["补气", "增强免疫", "抗疲劳"],
  "description": "补气固表，利尿托毒，敛疮生肌"
}
```

## 测试步骤

1. **启动后端服务**
   - 确保Nearby服务运行在1003端口
   - 确保网关服务运行在8083端口
   - 确保Nacos服务发现正常工作

2. **测试API连接**
   - 浏览器访问：`http://localhost:8083/medicinal/medicinalList`
   - 应该返回药材数据，包含meridian字段

3. **测试字段处理**
   - 打开：`Medicine-uniapp/test-meridian.html`
   - 点击"测试Meridian格式化"按钮
   - 验证黄芪显示为"补气"，人参显示为"安神"等

4. **路由说明**
   - 使用直接路由配置：`/medicinal/**` → `http://localhost:1003/medicinal/**`
   - CORS在网关层统一处理，避免重复头问题

5. **测试前端页面**
   - 运行uniapp项目
   - 进入药材食材页面
   - 观察红框中是否显示"补气"等功效关键词
   - 检查数据是否正确显示

## 故障排除

### 如果API调用失败
- 页面会显示"加载失败，使用本地数据"提示
- 自动回退到静态数据显示
- 检查控制台错误信息

### 常见问题
1. **跨域问题**：网关已配置CORS
2. **端口冲突**：确保8003和8083端口未被占用
3. **网络连接**：确保前端能访问localhost:8083

## 显示效果
- 保持与原UI设计完全一致
- 药材卡片显示：名称、别名、功效、类型、性味
- 标签最多显示3个
- 加载状态友好提示
