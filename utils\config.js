// 环境配置文件
const config = {
	// 开发环境
	development: {
		baseURL: 'http://localhost:8083',
		timeout: 15000
	},
	// 生产环境
	production: {
		baseURL: 'http://localhost:8083',
		timeout: 10000
	}
};

// 获取当前环境
const getEnvironment = () => {
	// 在uni-app中，可以通过process.env.NODE_ENV判断环境
	// 但在某些情况下可能不可用，所以我们用其他方式判断
	if (typeof window !== 'undefined' && window.location) {
		const hostname = window.location.hostname;
		if (hostname === 'localhost' || hostname === '127.0.0.1') {
			return 'development';
		}
	}
	return 'production';
};

// 获取当前配置
const getCurrentConfig = () => {
	const env = getEnvironment();
	console.log('当前环境:', env);
	return config[env];
};

export default getCurrentConfig();
