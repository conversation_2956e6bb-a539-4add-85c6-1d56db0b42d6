
.container.data-v-dd383ca2 {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 用户信息头部 */
.user-header.data-v-dd383ca2 {
	background: linear-gradient(135deg, #4CAF50, #66BB6A);
	padding: 60rpx 40rpx 40rpx;
	display: flex;
	align-items: center;
	gap: 30rpx;
	cursor: pointer;
}
.user-header.data-v-dd383ca2:active {
	opacity: 0.9;
}
.user-avatar.data-v-dd383ca2 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	overflow: hidden;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
}
.user-avatar image.data-v-dd383ca2 {
	width: 100%;
	height: 100%;
}
.avatar-placeholder.data-v-dd383ca2 {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 48rpx;
	color: white;
	font-weight: bold;
}
.user-info.data-v-dd383ca2 {
	flex: 1;
}
.user-name.data-v-dd383ca2 {
	font-size: 36rpx;
	font-weight: bold;
	color: #fff;
	display: block;
	margin-bottom: 10rpx;
}
.user-desc.data-v-dd383ca2 {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
}
.user-stats.data-v-dd383ca2 {
	display: flex;
	gap: 30rpx;
}
.stat-item.data-v-dd383ca2 {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.stat-number.data-v-dd383ca2 {
	font-size: 32rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 5rpx;
}
.stat-label.data-v-dd383ca2 {
	font-size: 22rpx;
	color: rgba(255, 255, 255, 0.8);
}
.login-prompt.data-v-dd383ca2 {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 40rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 30rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}
.login-text.data-v-dd383ca2 {
	color: white;
	font-size: 28rpx;
	font-weight: 500;
}

/* 功能菜单 */
.menu-section.data-v-dd383ca2 {
	margin-top: 20rpx;
}
.menu-group.data-v-dd383ca2 {
	background-color: #fff;
	margin-bottom: 20rpx;
}
.menu-item.data-v-dd383ca2 {
	display: flex;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.menu-item.data-v-dd383ca2:last-child {
	border-bottom: none;
}
.menu-icon.data-v-dd383ca2 {
	font-size: 36rpx;
	margin-right: 30rpx;
}
.menu-text.data-v-dd383ca2 {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}
.menu-arrow.data-v-dd383ca2 {
	font-size: 28rpx;
	color: #ccc;
}

/* 退出登录按钮样式 */
.logout-item.data-v-dd383ca2 {
	margin-top: 20rpx;
	border-top: 1rpx solid #f0f0f0;
	padding-top: 30rpx;
}
.logout-icon.data-v-dd383ca2 {
	color: #ff4757;
}
.logout-text.data-v-dd383ca2 {
	color: #ff4757;
	font-weight: 500;
}
.logout-item.data-v-dd383ca2:active {
	background-color: #fff5f5;
}

/* 最近收藏 */
.recent-section.data-v-dd383ca2 {
	background-color: #fff;
	margin-top: 20rpx;
	padding: 40rpx;
}
.section-header.data-v-dd383ca2 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}
.section-title.data-v-dd383ca2 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.section-more.data-v-dd383ca2 {
	font-size: 26rpx;
	color: #4CAF50;
}
.recent-list.data-v-dd383ca2 {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.recent-item.data-v-dd383ca2 {
	display: flex;
	align-items: center;
	gap: 20rpx;
}
.recent-image.data-v-dd383ca2 {
	width: 80rpx;
	height: 80rpx;
	border-radius: 10rpx;
}
.recent-info.data-v-dd383ca2 {
	flex: 1;
}
.recent-title.data-v-dd383ca2 {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}
.recent-type.data-v-dd383ca2 {
	font-size: 24rpx;
	color: #666;
}

/* 底部导航 */
.bottom-nav.data-v-dd383ca2 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-around;
	border-top: 1rpx solid #eee;
	z-index: 999;
}
.nav-item.data-v-dd383ca2 {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
}
.nav-item.active .nav-text.data-v-dd383ca2 {
	color: #4CAF50;
}
.nav-item.add-btn.data-v-dd383ca2 {
	position: relative;
}
.nav-icon.data-v-dd383ca2 {
	font-size: 40rpx;
	margin-bottom: 8rpx;
}
.add-icon.data-v-dd383ca2 {
	background-color: #4CAF50;
	color: #fff;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 50rpx;
	font-weight: bold;
}
.nav-text.data-v-dd383ca2 {
	font-size: 20rpx;
	color: #666;
}
