<template>
	<view class="container">
		<view class="header">
			<text class="title">轮播图功能测试</text>
		</view>
		
		<!-- 测试按钮 -->
		<view class="test-buttons">
			<button class="test-btn" @click="testCarouselAPI">测试轮播图API</button>
			<button class="test-btn" @click="clearLogs">清空日志</button>
		</view>
		
		<!-- 轮播图展示 -->
		<view class="carousel-section" v-if="carouselList.length > 0">
			<text class="section-title">轮播图展示</text>
			<swiper class="carousel-swiper" 
					:indicator-dots="true" 
					:autoplay="true" 
					:interval="3000" 
					:duration="500"
					indicator-color="rgba(255, 255, 255, 0.5)"
					indicator-active-color="#ffffff"
					circular>
				<swiper-item v-for="(item, index) in carouselList" :key="item.id" @click="onCarouselClick(item)">
					<view class="carousel-item">
						<image class="carousel-image" :src="item.imageUrl" mode="aspectFill" @error="onImageError(item, index)"></image>
						<view class="carousel-overlay">
							<text class="carousel-title">{{ item.title }}</text>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- 数据展示 -->
		<view class="data-section" v-if="carouselList.length > 0">
			<text class="section-title">轮播图数据</text>
			<view class="data-item" v-for="(item, index) in carouselList" :key="item.id">
				<text class="data-text">{{ index + 1 }}. {{ item.title }}</text>
				<text class="data-text">状态: {{ item.status === 1 ? '启用' : '禁用' }}</text>
				<text class="data-text">图片: {{ item.imageUrl }}</text>
			</view>
		</view>
		
		<!-- 日志展示 -->
		<view class="log-section">
			<text class="section-title">调试日志</text>
			<scroll-view class="log-container" scroll-y="true">
				<text class="log-item" v-for="(log, index) in logs" :key="index">{{ log }}</text>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import api from '@/utils/api.js';
	
	export default {
		data() {
			return {
				carouselList: [],
				logs: []
			}
		},
		onLoad() {
			this.addLog('页面加载完成');
			this.testCarouselAPI();
		},
		methods: {
			addLog(message) {
				const timestamp = new Date().toLocaleTimeString();
				this.logs.push(`[${timestamp}] ${message}`);
				console.log(message);
			},
			
			clearLogs() {
				this.logs = [];
				this.addLog('日志已清空');
			},
			
			async testCarouselAPI() {
				this.addLog('开始测试轮播图API...');
				
				try {
					this.addLog('调用 api.getCarouselList()');
					const response = await api.getCarouselList();
					
					this.addLog(`API响应类型: ${typeof response}`);
					this.addLog(`API响应: ${JSON.stringify(response)}`);
					
					// 检查响应格式
					if (response) {
						// 情况1: 标准Result格式 {code: 200, data: [...]}
						if (response.code === 200 && response.data && Array.isArray(response.data)) {
							this.addLog(`检测到标准Result格式，数据条数: ${response.data.length}`);
							// 过滤启用状态的轮播图
							this.carouselList = response.data.filter(item => item.status === 1);
							this.addLog(`过滤后的轮播图数量: ${this.carouselList.length}`);
						}
						// 情况2: 直接返回数组格式 [...]
						else if (Array.isArray(response)) {
							this.addLog(`检测到直接数组格式，数据条数: ${response.length}`);
							// 过滤启用状态的轮播图
							this.carouselList = response.filter(item => item.status === 1);
							this.addLog(`过滤后的轮播图数量: ${this.carouselList.length}`);
						}
						// 情况3: 其他格式
						else {
							this.addLog('未知的响应格式');
							this.addLog(`response.code: ${response.code}`);
							this.addLog(`response.data: ${JSON.stringify(response.data)}`);
							this.addLog(`response.message: ${response.message}`);
							this.carouselList = [];
						}
					} else {
						this.addLog('响应为空');
						this.carouselList = [];
					}
					
					this.addLog(`最终轮播图列表: ${JSON.stringify(this.carouselList)}`);
					
					if (this.carouselList.length > 0) {
						this.addLog('✅ 轮播图加载成功！');
					} else {
						this.addLog('⚠️ 没有可用的轮播图数据');
					}
					
				} catch (error) {
					this.addLog(`❌ API调用失败: ${error.message}`);
					this.addLog(`错误详情: ${JSON.stringify(error)}`);
					this.carouselList = [];
				}
			},
			
			onCarouselClick(item) {
				this.addLog(`点击轮播图: ${item.title}`);
				uni.showToast({
					title: `点击了: ${item.title}`,
					icon: 'none'
				});
			},
			
			onImageError(item, index) {
				this.addLog(`图片加载失败: ${item.imageUrl}`);
				this.carouselList[index].imageUrl = '/static/logo.png';
			}
		}
	}
</script>

<style scoped>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.header {
		text-align: center;
		margin-bottom: 30rpx;
	}
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.test-buttons {
		display: flex;
		justify-content: center;
		gap: 20rpx;
		margin-bottom: 30rpx;
	}
	
	.test-btn {
		background: #007bff;
		color: white;
		border: none;
		padding: 20rpx 40rpx;
		border-radius: 10rpx;
		font-size: 28rpx;
	}
	
	.carousel-section {
		margin-bottom: 30rpx;
	}
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.carousel-swiper {
		height: 300rpx;
		border-radius: 20rpx;
	}
	
	.carousel-item {
		position: relative;
		width: 100%;
		height: 100%;
		border-radius: 20rpx;
		overflow: hidden;
	}
	
	.carousel-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.carousel-overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
		padding: 30rpx 40rpx 40rpx;
	}
	
	.carousel-title {
		font-size: 42rpx;
		font-weight: bold;
		color: #ffffff;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
		line-height: 1.2;
	}
	
	.data-section {
		margin-bottom: 30rpx;
	}
	
	.data-item {
		background: white;
		padding: 20rpx;
		margin-bottom: 10rpx;
		border-radius: 10rpx;
	}
	
	.data-text {
		display: block;
		font-size: 24rpx;
		color: #666;
		margin-bottom: 5rpx;
	}
	
	.log-section {
		background: white;
		border-radius: 10rpx;
		padding: 20rpx;
	}
	
	.log-container {
		height: 400rpx;
		background: #f8f9fa;
		border-radius: 5rpx;
		padding: 10rpx;
	}
	
	.log-item {
		display: block;
		font-size: 22rpx;
		color: #333;
		margin-bottom: 5rpx;
		font-family: monospace;
		word-break: break-all;
	}
</style>
