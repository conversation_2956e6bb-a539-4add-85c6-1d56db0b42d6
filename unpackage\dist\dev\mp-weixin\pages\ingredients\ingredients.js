"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      activeCategory: "all",
      categoryList: [
        { id: "all", name: "全部" }
      ],
      // 动态从后端加载分类数据
      ingredientsList: [],
      loading: false,
      searchTimer: null,
      // 搜索防抖定时器
      isSearching: false,
      // 是否正在搜索状态
      originalIngredientsList: [
        // 保留原始静态数据作为备用
        {
          id: 1,
          name: "黄芪",
          alias: "北芪、绵芪",
          mainEffect: "补气固表，利尿托毒",
          type: "中药材",
          nature: "温性",
          category: "herb",
          subCategory: "tonify",
          image: "/static/logo.png",
          tags: ["补气", "增强免疫", "抗疲劳"],
          description: "黄芪是常用的补气中药，具有补气固表、利尿托毒、排脓、敛疮生肌的功效。"
        },
        {
          id: 2,
          name: "枸杞",
          alias: "枸杞子、红耳坠",
          mainEffect: "滋补肝肾，益精明目",
          type: "药食同源",
          nature: "平性",
          category: "food",
          subCategory: "tonify",
          image: "/static/logo.png",
          tags: ["滋阴", "明目", "抗衰老"],
          description: "枸杞具有滋补肝肾、益精明目的功效，是药食同源的佳品。"
        },
        {
          id: 3,
          name: "绿豆",
          alias: "青小豆",
          mainEffect: "清热解毒，消暑利尿",
          type: "食材",
          nature: "凉性",
          category: "food",
          subCategory: "clear",
          image: "/static/logo.png",
          tags: ["清热", "解毒", "消暑"],
          description: "绿豆具有清热解毒、消暑利尿的功效，夏季常用。"
        },
        {
          id: 4,
          name: "党参",
          alias: "上党参、防风党参",
          mainEffect: "补中益气，健脾益肺",
          type: "中药材",
          nature: "平性",
          category: "herb",
          subCategory: "tonify",
          image: "/static/logo.png",
          tags: ["补气", "健脾", "益肺"],
          description: "党参具有补中益气、健脾益肺的功效，是常用的补气药。"
        },
        {
          id: 5,
          name: "莲子",
          alias: "莲实、莲米",
          mainEffect: "补脾止泻，益肾涩精",
          type: "药食同源",
          nature: "平性",
          category: "food",
          subCategory: "regulate",
          image: "/static/logo.png",
          tags: ["健脾", "安神", "止泻"],
          description: "莲子具有补脾止泻、益肾涩精、养心安神的功效。"
        },
        {
          id: 6,
          name: "百合",
          alias: "强瞿、番韭",
          mainEffect: "养阴润肺，清心安神",
          type: "药食同源",
          nature: "微寒",
          category: "food",
          subCategory: "clear",
          image: "/static/logo.png",
          tags: ["润肺", "安神", "美容"],
          description: "百合具有养阴润肺、清心安神的功效，常用于肺燥咳嗽。"
        }
      ]
    };
  },
  computed: {
    filteredIngredients() {
      common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:186", "filteredIngredients计算 - isSearching:", this.isSearching, "searchKeyword:", this.searchKeyword, "activeCategory:", this.activeCategory, "ingredientsList长度:", this.ingredientsList.length);
      if (this.isSearching && this.searchKeyword.trim()) {
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:191", "搜索状态，直接返回API结果:", this.ingredientsList);
        return this.ingredientsList;
      }
      common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:200", "非搜索状态，直接返回当前药材列表:", this.ingredientsList);
      return this.ingredientsList;
    }
  },
  onLoad() {
    this.loadTypeList();
    this.loadMedicinalList();
  },
  methods: {
    // 加载分类列表
    async loadTypeList() {
      try {
        const response = await utils_api.api.getTypeList();
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:213", "分类API响应:", response);
        if (response && response.data) {
          this.categoryList = [{ id: "all", name: "全部" }];
          response.data.forEach((item) => {
            this.categoryList.push({
              id: item.id,
              name: item.name
            });
          });
          common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:227", "分类列表:", this.categoryList);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/ingredients/ingredients.vue:230", "加载分类列表失败:", error);
        common_vendor.index.showToast({
          title: "加载分类失败",
          icon: "none"
        });
      }
    },
    // 加载药材食材列表
    async loadMedicinalList() {
      this.loading = true;
      try {
        const response = await utils_api.api.getMedicinalList();
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:243", "API响应:", response);
        this.ingredientsList = response.map((item) => ({
          id: item.id,
          name: item.name || "未知",
          alias: item.rname || "无别名",
          // 使用rname字段作为别名
          mainEffect: item.efficacy || "功效待补充",
          // 使用efficacy字段作为功效
          type: item.name1 || "中药材",
          // 使用分类名称
          nature: this.formatMeridian(item.meridian),
          // 使用meridian字段作为归经显示
          category: this.getCategoryByType(item.name1),
          subCategory: this.getSubCategoryByEffect(item.efficacy),
          // 使用efficacy字段
          image: item.avatar || "/static/logo.png",
          // 使用avatar字段作为图片，如果没有则使用默认图片
          tags: this.getTagsByEffect(item.efficacy),
          // 使用efficacy字段生成标签
          description: item.description || item.efficacy || "详细信息待补充"
        }));
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:261", "转换后的数据:", this.ingredientsList);
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:262", "所有药材 ingredientsList 长度:", this.ingredientsList.length);
        this.$forceUpdate();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/ingredients/ingredients.vue:267", "加载药材列表失败:", error);
        common_vendor.index.showToast({
          title: "加载失败，使用本地数据",
          icon: "none"
        });
        this.ingredientsList = this.originalIngredientsList;
      } finally {
        this.loading = false;
      }
    },
    // 格式化性味显示，保持简洁
    formatNature(nature) {
      if (!nature)
        return "平性";
      if (nature.includes("温") || nature.includes("热"))
        return "温性";
      if (nature.includes("凉") || nature.includes("寒"))
        return "凉性";
      if (nature.includes("平"))
        return "平性";
      return "平性";
    },
    // 格式化归经显示，提取主要功效关键词
    formatMeridian(meridian) {
      if (!meridian)
        return "补气";
      const cleanMeridian = meridian.replace(/\r\n/g, "").replace(/\n/g, "").trim();
      if (cleanMeridian.includes("补气"))
        return "补气";
      if (cleanMeridian.includes("补血"))
        return "补血";
      if (cleanMeridian.includes("增强免疫"))
        return "增强免疫";
      if (cleanMeridian.includes("抗疲劳"))
        return "抗疲劳";
      if (cleanMeridian.includes("滋阴"))
        return "滋阴";
      if (cleanMeridian.includes("温阳"))
        return "温阳";
      if (cleanMeridian.includes("清热"))
        return "清热";
      if (cleanMeridian.includes("解毒"))
        return "解毒";
      if (cleanMeridian.includes("润燥"))
        return "润燥";
      if (cleanMeridian.includes("安神"))
        return "安神";
      if (cleanMeridian.includes("利尿"))
        return "利尿";
      if (cleanMeridian.includes("明目"))
        return "明目";
      if (cleanMeridian.includes("脾") || cleanMeridian.includes("胃"))
        return "健脾";
      if (cleanMeridian.includes("肝"))
        return "养肝";
      if (cleanMeridian.includes("肺"))
        return "润肺";
      if (cleanMeridian.includes("肾"))
        return "益肾";
      if (cleanMeridian.includes("心"))
        return "安神";
      return "补气";
    },
    // 根据功效获取子分类
    getSubCategoryByEffect(effect) {
      if (!effect)
        return "tonify";
      if (effect.includes("补气") || effect.includes("补血") || effect.includes("滋补"))
        return "tonify";
      if (effect.includes("清热") || effect.includes("解毒") || effect.includes("消暑"))
        return "clear";
      if (effect.includes("调理") || effect.includes("健脾") || effect.includes("安神"))
        return "regulate";
      return "tonify";
    },
    // 根据分类名称获取category
    getCategoryByType(typeName) {
      if (!typeName)
        return "herb";
      if (typeName.includes("食材") || typeName.includes("食物"))
        return "food";
      return "herb";
    },
    // 根据功效生成标签，最多3个，保持与图片显示一致
    getTagsByEffect(effect) {
      if (!effect)
        return ["功效"];
      const tags = [];
      if (effect.includes("补气"))
        tags.push("补气");
      if (effect.includes("补血"))
        tags.push("补血");
      if (effect.includes("增强免疫") || effect.includes("免疫"))
        tags.push("增强免疫");
      if (effect.includes("抗疲劳") || effect.includes("疲劳"))
        tags.push("抗疲劳");
      if (effect.includes("滋阴"))
        tags.push("滋阴");
      if (effect.includes("明目"))
        tags.push("明目");
      if (effect.includes("抗衰老") || effect.includes("衰老"))
        tags.push("抗衰老");
      if (effect.includes("清热"))
        tags.push("清热");
      if (effect.includes("解毒"))
        tags.push("解毒");
      if (effect.includes("消暑"))
        tags.push("消暑");
      if (effect.includes("润肺"))
        tags.push("润肺");
      if (effect.includes("安神"))
        tags.push("安神");
      if (effect.includes("美容"))
        tags.push("美容");
      if (effect.includes("健脾"))
        tags.push("健脾");
      if (effect.includes("益肾") || effect.includes("补肾"))
        tags.push("益肾");
      if (effect.includes("健胃"))
        tags.push("健胃");
      if (tags.length === 0) {
        if (effect.includes("补") || effect.includes("益"))
          tags.push("补益");
        if (effect.includes("清") || effect.includes("热"))
          tags.push("清热");
        if (effect.includes("调") || effect.includes("理"))
          tags.push("调理");
      }
      return tags.length > 0 ? tags.slice(0, 3) : ["功效"];
    },
    // 格式化功效显示（用于搜索结果）
    formatEffect(effect) {
      if (!effect)
        return "功效待补充";
      return effect.length > 20 ? effect.substring(0, 20) + "..." : effect;
    },
    // 格式化性味显示（用于搜索结果）
    formatProperty(property) {
      if (!property)
        return "平性";
      if (property.includes("温") || property.includes("热"))
        return "温性";
      if (property.includes("凉") || property.includes("寒"))
        return "凉性";
      if (property.includes("平"))
        return "平性";
      return "平性";
    },
    // 从功效中提取标签（用于搜索结果）
    extractTags(effect) {
      if (!effect)
        return ["功效"];
      const tags = [];
      if (effect.includes("补气"))
        tags.push("补气");
      if (effect.includes("补血"))
        tags.push("补血");
      if (effect.includes("增强免疫") || effect.includes("免疫"))
        tags.push("增强免疫");
      if (effect.includes("抗疲劳") || effect.includes("疲劳"))
        tags.push("抗疲劳");
      if (effect.includes("滋阴"))
        tags.push("滋阴");
      if (effect.includes("明目"))
        tags.push("明目");
      if (effect.includes("抗衰老") || effect.includes("衰老"))
        tags.push("抗衰老");
      if (effect.includes("清热"))
        tags.push("清热");
      if (effect.includes("解毒"))
        tags.push("解毒");
      if (effect.includes("消暑"))
        tags.push("消暑");
      if (effect.includes("润肺"))
        tags.push("润肺");
      if (effect.includes("安神"))
        tags.push("安神");
      if (effect.includes("美容"))
        tags.push("美容");
      if (effect.includes("健脾"))
        tags.push("健脾");
      if (effect.includes("益肾") || effect.includes("补肾"))
        tags.push("益肾");
      if (effect.includes("健胃"))
        tags.push("健胃");
      if (tags.length === 0) {
        if (effect.includes("补") || effect.includes("益"))
          tags.push("补益");
        if (effect.includes("清") || effect.includes("热"))
          tags.push("清热");
        if (effect.includes("调") || effect.includes("理"))
          tags.push("调理");
      }
      return tags.length > 0 ? tags.slice(0, 3) : ["功效"];
    },
    // 搜索功能
    onSearch() {
      common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:424", "用户输入搜索关键词:", this.searchKeyword);
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      this.searchTimer = setTimeout(() => {
        this.performSearch();
      }, 500);
    },
    // 执行搜索
    async performSearch() {
      const keyword = this.searchKeyword.trim();
      common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:440", "执行搜索 - 原始输入:", this.searchKeyword);
      common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:441", "执行搜索 - 处理后关键词:", keyword);
      common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:442", "执行搜索 - 关键词长度:", keyword.length);
      common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:443", "执行搜索 - 关键词类型:", typeof keyword);
      common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:444", "执行搜索 - 当前isSearching状态:", this.isSearching);
      if (!keyword) {
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:448", "搜索关键词为空，加载所有数据");
        this.isSearching = false;
        this.loadMedicinalList();
        return;
      }
      try {
        this.isSearching = true;
        this.loading = true;
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:457", "开始搜索药材，关键词:", keyword);
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:458", "调用API: getMedicinalByName，参数:", keyword);
        const response = await utils_api.api.getMedicinalByName(keyword);
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:461", "后端API返回结果:", response);
        if (response && response.code === 200 && response.data && Array.isArray(response.data)) {
          common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:465", "搜索数据解析成功，数据条数:", response.data.length);
          this.ingredientsList = response.data.map((item) => ({
            id: item.id,
            name: item.name,
            alias: item.alias || "暂无别名",
            mainEffect: this.formatEffect(item.effect),
            type: item.type || "中药材",
            nature: this.formatProperty(item.property),
            category: this.getCategoryByType(item.type),
            subCategory: this.getSubCategoryByEffect(item.effect),
            image: item.avatar || "/static/logo.png",
            tags: this.extractTags(item.effect),
            originalData: item
          }));
          common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:481", "搜索结果处理完成:", this.ingredientsList);
          common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:482", "设置isSearching为true，当前状态:", this.isSearching);
          this.$forceUpdate();
        } else {
          common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:486", "搜索无结果或数据格式错误");
          common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:487", "响应详情:", response);
          this.ingredientsList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/ingredients/ingredients.vue:491", "搜索失败:", error);
        common_vendor.index.showToast({
          title: "搜索失败，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 选择分类
    async selectCategory(categoryId) {
      common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:504", "选择分类，ID:", categoryId);
      this.activeCategory = categoryId;
      this.searchKeyword = "";
      this.isSearching = false;
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
        this.searchTimer = null;
      }
      if (categoryId === "all") {
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:517", "加载所有药材");
        this.loadMedicinalList();
      } else {
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:521", "根据分类ID加载药材:", categoryId);
        this.loadMedicinalByType(categoryId);
      }
    },
    // 根据分类ID加载药材
    async loadMedicinalByType(typeId) {
      common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:528", "开始加载分类药材，分类ID:", typeId);
      this.loading = true;
      try {
        const response = await utils_api.api.getMedicinalByTypeId(typeId);
        common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:532", "分类药材API响应:", response);
        if (response && response.code === 200 && response.data) {
          common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:536", `成功获取分类${typeId}的药材，共${response.data.length}个`);
          this.ingredientsList = response.data.map((item) => ({
            id: item.id,
            name: item.name || "未知",
            alias: item.rname || "无别名",
            mainEffect: item.efficacy || "功效待补充",
            type: item.name1 || "中药材",
            nature: this.formatMeridian(item.meridian),
            category: this.getCategoryByType(item.name1),
            subCategory: this.getSubCategoryByEffect(item.efficacy),
            image: item.avatar || "/static/logo.png",
            tags: this.getTagsByEffect(item.efficacy),
            description: item.description || item.efficacy || "详细信息待补充"
          }));
          common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:553", "转换后的药材列表:", this.ingredientsList);
          common_vendor.index.__f__("log", "at pages/ingredients/ingredients.vue:554", "ingredientsList 长度:", this.ingredientsList.length);
          this.$forceUpdate();
        } else {
          common_vendor.index.__f__("error", "at pages/ingredients/ingredients.vue:559", "API响应格式错误:", response);
          this.ingredientsList = [];
          common_vendor.index.showToast({
            title: "数据格式错误",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/ingredients/ingredients.vue:567", "加载分类药材失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
        this.ingredientsList = [];
      } finally {
        this.loading = false;
      }
    },
    // 跳转到详情页
    goToDetail(item) {
      common_vendor.index.navigateTo({
        url: `/pages/ingredient-detail/ingredient-detail?id=${item.id}`
      });
    },
    // 导航跳转
    goToPage(page) {
      switch (page) {
        case "home":
          common_vendor.index.navigateTo({
            url: "/pages/index/index"
          });
          break;
        case "ingredients":
          break;
        case "recipe":
          common_vendor.index.navigateTo({
            url: "/pages/recipe/recipe"
          });
          break;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearch && $options.onSearch(...args)]),
    b: $data.searchKeyword,
    c: common_vendor.f($data.categoryList, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.n($data.activeCategory === item.id ? "active" : ""),
        c: item.id,
        d: common_vendor.o(($event) => $options.selectCategory(item.id), item.id)
      };
    }),
    d: $data.loading
  }, $data.loading ? {
    e: common_vendor.t($data.isSearching ? "正在搜索药材..." : "正在加载药材数据...")
  } : common_vendor.e({
    f: $data.isSearching && $options.filteredIngredients.length === 0
  }, $data.isSearching && $options.filteredIngredients.length === 0 ? {} : {}, {
    g: common_vendor.f($options.filteredIngredients, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.alias),
        d: common_vendor.t(item.mainEffect),
        e: common_vendor.f(item.tags, (tag, k1, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tag
          };
        }),
        f: common_vendor.t(item.type),
        g: common_vendor.t(item.nature),
        h: index,
        i: common_vendor.o(($event) => $options.goToDetail(item), index),
        j: index * 0.1 + "s"
      };
    })
  }), {
    h: common_vendor.o(($event) => $options.goToPage("home")),
    i: common_vendor.o(($event) => $options.goToPage("ingredients")),
    j: common_vendor.o(($event) => $options.goToPage("recipe")),
    k: common_vendor.o(($event) => $options.goToPage("profile"))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-9d78cbe1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ingredients/ingredients.js.map
