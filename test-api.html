<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>药材API测试页面</h1>
    
    <div class="test-section">
        <h3>测试1: 获取药材列表</h3>
        <button onclick="testMedicinalList()">测试药材列表API</button>
        <div id="list-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试2: 获取药材详情</h3>
        <button onclick="testMedicinalDetail()">测试药材详情API (ID=1)</button>
        <div id="detail-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试3: 直接访问后端服务</h3>
        <button onclick="testDirectAccess()">直接访问Nearby服务</button>
        <div id="direct-result" class="result"></div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8083';
        
        async function testMedicinalList() {
            const resultDiv = document.getElementById('list-result');
            resultDiv.textContent = '正在测试...';

            try {
                console.log('发起请求到:', `${BASE_URL}/medicinal/medicinalList`);
                const response = await fetch(`${BASE_URL}/medicinal/medicinalList`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `成功! 状态码: ${response.status}\n\n数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `失败! 状态码: ${response.status}\n响应: ${await response.text()}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `网络错误: ${error.message}`;
            }
        }
        
        async function testMedicinalDetail() {
            const resultDiv = document.getElementById('detail-result');
            resultDiv.textContent = '正在测试...';
            
            try {
                const response = await fetch(`${BASE_URL}/medicinal/medicinalById?id=1`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `成功! 状态码: ${response.status}\n\n数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `失败! 状态码: ${response.status}\n响应: ${await response.text()}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `网络错误: ${error.message}`;
            }
        }
        
        async function testDirectAccess() {
            const resultDiv = document.getElementById('direct-result');
            resultDiv.textContent = '正在测试...';
            
            try {
                const response = await fetch('http://localhost:1003/medicinal/medicinalList', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `成功! 状态码: ${response.status}\n\n数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `失败! 状态码: ${response.status}\n响应: ${await response.text()}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `网络错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
