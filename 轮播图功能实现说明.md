# 轮播图功能实现说明

## 概述
已成功将前端首页的静态横幅替换为动态轮播图，通过网关调用后端 `/carousel/list` API 获取轮播图数据，实现前后端连接。轮播图中的 title 字段文字样式与图片上的文字样式保持一致。

## 修改内容

### 1. API 接口添加
**文件**: `Medicine-uniapp/utils/api.js`
- 添加了 `getCarouselList()` 方法，调用 `/carousel/list` 接口获取轮播图数据

```javascript
// 获取轮播图列表
getCarouselList() {
    return request({
        url: '/carousel/list',
        method: 'GET'
    });
}
```

### 2. 首页模板修改
**文件**: `Medicine-uniapp/pages/index/index.vue`

#### 模板部分
- 将静态的 `hero-banner` 替换为动态的 `swiper` 轮播图组件
- 添加了轮播图指示器、自动播放、循环播放功能
- 保留了默认横幅作为备用（当没有轮播图数据时显示）

#### 主要特性
- **自动播放**: 3秒间隔自动切换
- **指示器**: 白色圆点指示当前位置
- **循环播放**: 无限循环轮播
- **点击事件**: 支持点击轮播图触发跳转（预留功能）
- **错误处理**: 图片加载失败时显示默认图片

### 3. 脚本逻辑修改

#### 数据结构
```javascript
data() {
    return {
        title: '药食同源',
        featuredIngredients: [],
        carouselList: [],        // 新增：轮播图数据
        loading: false
    }
}
```

#### 新增方法
1. **loadCarouselList()**: 加载轮播图数据
   - 调用 API 获取轮播图列表
   - 过滤启用状态的轮播图 (status === 1)
   - 错误处理和日志记录

2. **onCarouselClick(item)**: 轮播图点击事件处理
   - 预留跳转功能接口
   - 当前显示"跳转功能开发中"提示

3. **onCarouselImageError(item, index)**: 轮播图图片错误处理
   - 图片加载失败时设置默认图片

### 4. 样式设计

#### 轮播图样式特点
- **尺寸**: 高度 300rpx，与原横幅保持一致
- **圆角**: 20rpx 圆角设计，保持界面一致性
- **文字覆盖**: 底部渐变遮罩，确保文字清晰可读
- **标题样式**: 42rpx 字体，粗体，白色，带阴影效果

#### 文字样式一致性
轮播图标题样式与原横幅标题完全一致：
```css
.carousel-title {
    font-size: 42rpx;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
    line-height: 1.2;
}
```

### 5. 后端数据结构
轮播图数据来自 `CarouselBanner` 实体：
- `id`: 主键
- `title`: 轮播图标题（显示在图片上）
- `imageUrl`: 图片URL
- `redirectUrl`: 点击跳转链接（预留）
- `status`: 状态（0=禁用，1=启用）
- `createTime`: 创建时间
- `updateTime`: 更新时间

### 6. 网关路由配置
已配置轮播图路由：
```java
.route("carousel_route", r -> r.path("/carousel/**")
    .filters(f -> f.rewritePath("/carousel/(?<segment>.*)", "/carousel/${segment}"))
    .uri("http://localhost:1005"))
```

## 测试文件
创建了 `test-carousel.html` 测试页面，可以独立测试轮播图功能：
- 模拟真实的 API 调用
- 展示轮播图的各种状态（加载中、成功、失败）
- 验证样式和交互效果

## 使用说明

### 前端调用
```javascript
// 在页面加载时自动调用
onLoad() {
    this.loadCarouselList();
    this.loadFeaturedIngredients();
}
```

### 后端管理
1. 通过管理端添加轮播图数据
2. 设置 `status = 1` 启用轮播图
3. 前端会自动获取并显示启用的轮播图

### 错误处理
- API 调用失败：显示默认横幅
- 图片加载失败：显示默认图片
- 无轮播图数据：显示默认横幅

## 兼容性
- 保持了原有的界面布局和样式
- 向后兼容：即使轮播图功能失败，也会显示原来的默认横幅
- 不影响其他页面功能

## 注意事项
1. 轮播图图片建议尺寸比例为 16:9 或类似宽屏比例
2. 标题文字建议控制在 20 字以内，确保显示效果
3. 图片 URL 需要确保可访问性
4. 后端需要确保 `/carousel/list` 接口正常工作
