{"version": 3, "file": "recipe.js", "sources": ["api/recipe.js"], "sourcesContent": ["/**\r\n * 食疗方案相关API接口\r\n * 通过网关访问后端服务\r\n */\r\n\r\nimport { API_CONFIG, API_PATHS } from '../config/api.js';\r\n\r\n/**\r\n * 通用请求方法\r\n * @param {Object} options 请求配置\r\n * @returns {Promise} 请求结果\r\n */\r\nfunction request(options) {\r\n  return new Promise((resolve, reject) => {\r\n    uni.request({\r\n      url: API_CONFIG.baseUrl + options.url,\r\n      method: options.method || 'GET',\r\n      data: options.data || {},\r\n      header: {\r\n        'Content-Type': 'application/json',\r\n        ...options.header\r\n      },\r\n      timeout: API_CONFIG.timeout,\r\n      success: (res) => {\r\n        if (res.statusCode === 200) {\r\n          resolve(res.data);\r\n        } else {\r\n          reject(new Error(`请求失败: ${res.statusCode}`));\r\n        }\r\n      },\r\n      fail: (err) => {\r\n        reject(err);\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\n/**\r\n * 获取食疗方案列表\r\n * @param {Object} params 查询参数\r\n * @param {String} params.keyword 搜索关键词\r\n * @param {String} params.category 分类筛选\r\n * @param {Number} params.page 页码\r\n * @param {Number} params.size 每页大小\r\n * @param {Number} params.userId 用户ID\r\n * @returns {Promise} 方案列表\r\n */\r\nexport function getRecipeList(params = {}) {\r\n  console.log('getRecipeList - 原始参数:', params);\r\n  \r\n  // 构建查询字符串\r\n  const queryString = Object.keys(params)\r\n    .filter(key => params[key] !== undefined && params[key] !== null && params[key] !== '')\r\n    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)\r\n    .join('&');\r\n  \r\n  const url = queryString ? `${API_PATHS.RECIPE.LIST}?${queryString}` : API_PATHS.RECIPE.LIST;\r\n  \r\n  console.log('getRecipeList - 最终URL:', API_CONFIG.baseUrl + url);\r\n  \r\n  return request({\r\n    url: url,\r\n    method: 'GET'\r\n  });\r\n}\r\n\r\n/**\r\n * 收藏/取消收藏食疗方案\r\n * @param {Object} data 请求数据\r\n * @param {Number} data.planId 方案ID\r\n * @param {Number} data.userId 用户ID\r\n * @returns {Promise} 操作结果\r\n */\r\nexport function toggleFavorite(data) {\r\n  return request({\r\n    url: API_PATHS.RECIPE.FAVORITE,\r\n    method: 'POST',\r\n    data: data\r\n  });\r\n}\r\n\r\n/**\r\n * 增加方案浏览量\r\n * 每个用户对每个方案只能贡献1次浏览量\r\n * @param {Number} planId 方案ID\r\n * @param {Number} userId 用户ID\r\n * @returns {Promise} 操作结果\r\n */\r\nexport function incrementViewCount(planId, userId) {\r\n  return request({\r\n    url: `${API_PATHS.RECIPE.VIEW_COUNT}/${planId}`,\r\n    method: 'POST',\r\n    data: {\r\n      userId: userId\r\n    }\r\n  });\r\n}\r\n\r\n/**\r\n * 记录搜索日志\r\n * @param {Object} data 搜索数据\r\n * @param {Number} data.userId 用户ID\r\n * @param {String} data.queryText 搜索关键词\r\n * @param {Number} data.resultCount 结果数量\r\n * @returns {Promise} 操作结果\r\n */\r\nexport function recordSearchLog(data) {\r\n  return request({\r\n    url: API_PATHS.RECIPE.SEARCH_LOG,\r\n    method: 'POST',\r\n    data: data\r\n  });\r\n}\r\n\r\n/**\r\n * 获取食疗方案详情\r\n * @param {Number} planId 方案ID\r\n * @returns {Promise} 方案详情\r\n */\r\nexport function getRecipeDetail(planId) {\r\n  return request({\r\n    url: `${API_PATHS.RECIPE.DETAIL}/${planId}`,\r\n    method: 'GET'\r\n  });\r\n}\r\n\r\n/**\r\n * 健康检查\r\n * @returns {Promise} 服务状态\r\n */\r\nexport function healthCheck() {\r\n  return request({\r\n    url: API_PATHS.TEST.HEALTH,\r\n    method: 'GET'\r\n  });\r\n}\r\n\r\n/**\r\n * 点赞/取消点赞食疗方案\r\n * @param {Number} planId 方案ID\r\n * @param {Number} userId 用户ID\r\n * @returns {Promise} 操作结果\r\n */\r\nexport function toggleLike(planId, userId) {\r\n  return request({\r\n    url: `${API_PATHS.RECIPE.LIKE}/${planId}`,\r\n    method: 'POST',\r\n    data: {\r\n      userId: userId\r\n    }\r\n  });\r\n}\r\n\r\n/**\r\n * 批量获取点赞状态\r\n * @param {Number} userId 用户ID\r\n * @param {Array} planIds 方案ID列表\r\n * @returns {Promise} 点赞状态映射\r\n */\r\nexport function getBatchLikeStatus(userId, planIds) {\r\n  return request({\r\n    url: API_PATHS.RECIPE.BATCH_LIKE_STATUS,\r\n    method: 'POST',\r\n    data: {\r\n      userId: userId,\r\n      planIds: planIds\r\n    }\r\n  });\r\n} "], "names": ["uni", "API_CONFIG", "API_PATHS"], "mappings": ";;;AAYA,SAAS,QAAQ,SAAS;AACxB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,kBAAAA,MAAI,QAAQ;AAAA,MACV,KAAKC,WAAU,WAAC,UAAU,QAAQ;AAAA,MAClC,QAAQ,QAAQ,UAAU;AAAA,MAC1B,MAAM,QAAQ,QAAQ,CAAE;AAAA,MACxB,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,GAAG,QAAQ;AAAA,MACZ;AAAA,MACD,SAASA,WAAU,WAAC;AAAA,MACpB,SAAS,CAAC,QAAQ;AAChB,YAAI,IAAI,eAAe,KAAK;AAC1B,kBAAQ,IAAI,IAAI;AAAA,QAC1B,OAAe;AACL,iBAAO,IAAI,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;AAAA,QAC5C;AAAA,MACF;AAAA,MACD,MAAM,CAAC,QAAQ;AACb,eAAO,GAAG;AAAA,MACX;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAYO,SAAS,cAAc,SAAS,IAAI;AACzCD,gBAAA,MAAA,MAAA,OAAA,uBAAY,yBAAyB,MAAM;AAG3C,QAAM,cAAc,OAAO,KAAK,MAAM,EACnC,OAAO,SAAO,OAAO,GAAG,MAAM,UAAa,OAAO,GAAG,MAAM,QAAQ,OAAO,GAAG,MAAM,EAAE,EACrF,IAAI,SAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EAC1E,KAAK,GAAG;AAEX,QAAM,MAAM,cAAc,GAAGE,WAAAA,UAAU,OAAO,IAAI,IAAI,WAAW,KAAKA,qBAAU,OAAO;AAEvFF,sBAAY,MAAA,OAAA,uBAAA,0BAA0BC,sBAAW,UAAU,GAAG;AAE9D,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,EACZ,CAAG;AACH;AASO,SAAS,eAAe,MAAM;AACnC,SAAO,QAAQ;AAAA,IACb,KAAKC,WAAAA,UAAU,OAAO;AAAA,IACtB,QAAQ;AAAA,IACR;AAAA,EACJ,CAAG;AACH;AASO,SAAS,mBAAmB,QAAQ,QAAQ;AACjD,SAAO,QAAQ;AAAA,IACb,KAAK,GAAGA,qBAAU,OAAO,UAAU,IAAI,MAAM;AAAA,IAC7C,QAAQ;AAAA,IACR,MAAM;AAAA,MACJ;AAAA,IACD;AAAA,EACL,CAAG;AACH;AAUO,SAAS,gBAAgB,MAAM;AACpC,SAAO,QAAQ;AAAA,IACb,KAAKA,WAAAA,UAAU,OAAO;AAAA,IACtB,QAAQ;AAAA,IACR;AAAA,EACJ,CAAG;AACH;AAOO,SAAS,gBAAgB,QAAQ;AACtC,SAAO,QAAQ;AAAA,IACb,KAAK,GAAGA,qBAAU,OAAO,MAAM,IAAI,MAAM;AAAA,IACzC,QAAQ;AAAA,EACZ,CAAG;AACH;AAmBO,SAAS,WAAW,QAAQ,QAAQ;AACzC,SAAO,QAAQ;AAAA,IACb,KAAK,GAAGA,qBAAU,OAAO,IAAI,IAAI,MAAM;AAAA,IACvC,QAAQ;AAAA,IACR,MAAM;AAAA,MACJ;AAAA,IACD;AAAA,EACL,CAAG;AACH;AAQO,SAAS,mBAAmB,QAAQ,SAAS;AAClD,SAAO,QAAQ;AAAA,IACb,KAAKA,WAAAA,UAAU,OAAO;AAAA,IACtB,QAAQ;AAAA,IACR,MAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACD;AAAA,EACL,CAAG;AACH;;;;;;;;"}