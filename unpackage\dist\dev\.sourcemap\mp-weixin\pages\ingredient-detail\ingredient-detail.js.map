{"version": 3, "file": "ingredient-detail.js", "sources": ["pages/ingredient-detail/ingredient-detail.vue", "../产出物/安装包/HBuilderX/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5ncmVkaWVudC1kZXRhaWwvaW5ncmVkaWVudC1kZXRhaWwudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 加载状态 -->\r\n\t\t<view v-if=\"loading\" class=\"loading-container\">\r\n\t\t\t<text class=\"loading-text\">正在加载药材详情...</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 药材头部信息 -->\r\n\t\t<view v-else class=\"ingredient-header\">\r\n\t\t\t<!-- 显示图片，如果没有图片或加载失败则显示图标 -->\r\n\t\t\t<view v-if=\"showImage && ingredientDetail.avatar\" class=\"ingredient-image-container\">\r\n\t\t\t\t<image class=\"ingredient-image\" :src=\"ingredientDetail.avatar\" mode=\"aspectFill\" @error=\"onImageError\" @load=\"onImageLoad\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view v-else class=\"ingredient-icon\">\r\n\t\t\t\t<text class=\"icon-text\">{{ getIconText(ingredientDetail.name) }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"header-info\">\r\n\t\t\t\t<text class=\"ingredient-name\">{{ ingredientDetail.name || '未知药材' }}</text>\r\n\t\t\t\t<text class=\"ingredient-alias\">别名：{{ ingredientDetail.rname || '无别名' }}</text>\r\n\t\t\t\t<view class=\"ingredient-tags\">\r\n\t\t\t\t\t<text class=\"tag\" v-for=\"tag in parsedTags\" :key=\"tag\">{{ tag }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ingredient-meta\">\r\n\t\t\t\t<view class=\"meta-item\">\r\n\t\t\t\t\t<text class=\"meta-label\">类型</text>\r\n\t\t\t\t\t<text class=\"meta-value\">{{ ingredientDetail.name1 || '中药材' }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"meta-item\">\r\n\t\t\t\t\t<text class=\"meta-label\">性味</text>\r\n\t\t\t\t\t<text class=\"meta-value\">{{ formatNature(ingredientDetail.meridian) }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 详细信息 -->\r\n\t\t<view v-if=\"!loading\" class=\"detail-content\">\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<text class=\"section-title\">主要功效</text>\r\n\t\t\t\t<text class=\"section-content\">{{ ingredientDetail.efficacy || '功效信息待补充' }}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<text class=\"section-title\">详细介绍</text>\r\n\t\t\t\t<text class=\"section-content\">{{ ingredientDetail.description || getDefaultDescription() }}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<text class=\"section-title\">适用人群</text>\r\n\t\t\t\t<text class=\"section-content\">{{ ingredientDetail.applicablepopulation || getSuitableFor() }}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<text class=\"section-title\">使用方法</text>\r\n\t\t\t\t<text class=\"section-content\">{{ ingredientDetail.dosage || getDefaultUsageMethod() }}</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<text class=\"section-title\">注意事项</text>\r\n\t\t\t\t<text class=\"section-content\">{{ ingredientDetail.taboo || getDefaultPrecautions() }}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 相关食疗方案 -->\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<text class=\"section-title\">相关食疗方案</text>\r\n\t\t\t\t<view class=\"related-recipes\">\r\n\t\t\t\t\t<view class=\"recipe-item\" v-for=\"recipe in ingredientDetail.relatedRecipes\" :key=\"recipe.id\" @click=\"goToRecipe(recipe.id)\">\r\n\t\t\t\t\t\t<image class=\"recipe-thumb\" :src=\"recipe.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<view class=\"recipe-info\">\r\n\t\t\t\t\t\t\t<text class=\"recipe-name\">{{ recipe.name }}</text>\r\n\t\t\t\t\t\t\t<text class=\"recipe-effect\">{{ recipe.effect }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"arrow\">></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部操作按钮 -->\r\n\t\t<view class=\"bottom-actions\">\r\n\t\t\t<button class=\"action-btn secondary\" @click=\"addToCollection\">加入收藏</button>\r\n\t\t\t<button class=\"action-btn primary\" @click=\"viewRecipes\">查看食疗方案</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport api from '@/utils/api.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tingredientId: '',\r\n\t\t\tloading: true,\r\n\t\t\tshowImage: true, // 控制是否显示图片\r\n\t\t\tingredientDetail: {}\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tparsedTags() {\r\n\t\t\treturn this.getTagsByEffect(this.ingredientDetail.efficacy || '');\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\tif (options.id) {\r\n\t\t\tthis.ingredientId = options.id;\r\n\t\t\tthis.loadIngredientDetail();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 加载药材详情\r\n\t\tasync loadIngredientDetail() {\r\n\t\t\ttry {\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tthis.showImage = true; // 重置图片显示状态\r\n\t\t\t\tconsole.log('加载药材详情，ID:', this.ingredientId);\r\n\r\n\t\t\t\tconst response = await api.getMedicinalById(this.ingredientId);\r\n\t\t\t\tconsole.log('药材详情API响应:', response);\r\n\r\n\t\t\t\tif (response && response.code === 200 && response.data) {\r\n\t\t\t\t\tthis.ingredientDetail = response.data;\r\n\t\t\t\t\tconsole.log('药材详情加载成功:', this.ingredientDetail);\r\n\t\t\t\t\tconsole.log('dosage字段:', this.ingredientDetail.dosage);\r\n\t\t\t\t\tconsole.log('taboo字段:', this.ingredientDetail.taboo);\r\n\t\t\t\t\tconsole.log('applicablepopulation字段:', this.ingredientDetail.applicablepopulation);\r\n\t\t\t\t\tconsole.log('avatar图片字段:', this.ingredientDetail.avatar);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow new Error('获取药材详情失败');\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('加载药材详情失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '加载失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t} finally {\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 图片加载成功\r\n\t\tonImageLoad() {\r\n\t\t\tconsole.log('图片加载成功:', this.ingredientDetail.avatar);\r\n\t\t},\r\n\r\n\t\t// 图片加载错误处理\r\n\t\tonImageError() {\r\n\t\t\tconsole.log('图片加载失败，将显示图标:', this.ingredientDetail.avatar);\r\n\t\t\t// 图片加载失败时，设置showImage为false，这样会显示图标\r\n\t\t\tthis.showImage = false;\r\n\t\t},\r\n\r\n\t\t// 获取图标文字\r\n\t\tgetIconText(name) {\r\n\t\t\tif (!name) return '药';\r\n\t\t\treturn name.charAt(0);\r\n\t\t},\r\n\r\n\t\t// 格式化性味\r\n\t\tformatNature(meridian) {\r\n\t\t\tif (!meridian) return '甘，微温';\r\n\r\n\t\t\tconst cleanMeridian = meridian.replace(/\\r\\n/g, '').replace(/\\n/g, '').trim();\r\n\r\n\t\t\t// 提取性味信息\r\n\t\t\tif (cleanMeridian.includes('甘') && cleanMeridian.includes('温')) return '甘，微温';\r\n\t\t\tif (cleanMeridian.includes('苦') && cleanMeridian.includes('寒')) return '苦，寒';\r\n\t\t\tif (cleanMeridian.includes('辛') && cleanMeridian.includes('温')) return '辛，温';\r\n\t\t\tif (cleanMeridian.includes('酸') && cleanMeridian.includes('平')) return '酸，平';\r\n\t\t\tif (cleanMeridian.includes('咸') && cleanMeridian.includes('寒')) return '咸，寒';\r\n\r\n\t\t\treturn '甘，微温';\r\n\t\t},\r\n\r\n\t\t// 根据功效生成标签\r\n\t\tgetTagsByEffect(effect) {\r\n\t\t\tif (!effect) return ['功效'];\r\n\t\t\tconst tags = [];\r\n\r\n\t\t\tif (effect.includes('补气')) tags.push('补气');\r\n\t\t\tif (effect.includes('补血')) tags.push('补血');\r\n\t\t\tif (effect.includes('增强免疫') || effect.includes('免疫')) tags.push('增强免疫');\r\n\t\t\tif (effect.includes('抗疲劳') || effect.includes('疲劳')) tags.push('抗疲劳');\r\n\t\t\tif (effect.includes('利尿')) tags.push('利尿');\r\n\r\n\t\t\tif (tags.length === 0) {\r\n\t\t\t\tif (effect.includes('补') || effect.includes('益')) tags.push('补益');\r\n\t\t\t\tif (effect.includes('清') || effect.includes('热')) tags.push('清热');\r\n\t\t\t\tif (effect.includes('调') || effect.includes('理')) tags.push('调理');\r\n\t\t\t}\r\n\r\n\t\t\treturn tags.slice(0, 4);\r\n\t\t},\r\n\r\n\t\t// 获取默认描述\r\n\t\tgetDefaultDescription() {\r\n\t\t\tconst name = this.ingredientDetail.name || '该药材';\r\n\t\t\tconst efficacy = this.ingredientDetail.efficacy || '具有多种功效';\r\n\r\n\t\t\treturn `${name}为常用中药材，${efficacy}。${name}味甘，性微温，归肺、脾经。常用于气虚乏力、食少便溏、中气下陷、久泻脱肛、便血崩漏、表虚自汗、气虚水肿、痈疽难溃、久溃不敛、血虚萎黄、内热消渴等症。`;\r\n\t\t},\r\n\r\n\t\t// 获取适用人群\r\n\t\tgetSuitableFor() {\r\n\t\t\tconst efficacy = this.ingredientDetail.efficacy || '';\r\n\r\n\t\t\tif (efficacy.includes('补气')) {\r\n\t\t\t\treturn '适用于气虚体质、免疫力低下、容易感冒、疲劳乏力、食欲不振、消化不良、水肿、自汗盗汗等人群。特别适合体质虚弱、病后康复期的人群使用。';\r\n\t\t\t} else if (efficacy.includes('补血')) {\r\n\t\t\t\treturn '适用于血虚体质、面色苍白、头晕心悸、失眠多梦、月经不调等人群。特别适合女性朋友和贫血患者使用。';\r\n\t\t\t} else if (efficacy.includes('清热')) {\r\n\t\t\t\treturn '适用于热性体质、口干舌燥、便秘、上火、咽喉肿痛等人群。特别适合夏季使用或体内有热的人群。';\r\n\t\t\t}\r\n\r\n\t\t\treturn '适用于相应体质和症状的人群，建议在专业医师指导下使用。';\r\n\t\t},\r\n\r\n\t\t// 获取默认使用方法（当dosage字段为空时使用）\r\n\t\tgetDefaultUsageMethod() {\r\n\t\t\treturn `1. 煎汤：常用量9-30g，大剂量可用30-120g\r\n2. 泡茶：3-6g，开水冲泡代茶饮\r\n3. 炖汤：与鸡肉、排骨等一起炖煮\r\n4. 研粉：研成细粉，每次3-6g，温水送服\r\n5. 制膏：与其他药材制成膏方服用`;\r\n\t\t},\r\n\r\n\t\t// 获取默认注意事项（当taboo字段为空时使用）\r\n\t\tgetDefaultPrecautions() {\r\n\t\t\treturn `1. 表实邪盛、气滞湿阻、食积停滞、痈疽初起或溃后热毒尚盛等实证，以及阴虚阳亢者，均不宜服用\r\n2. 感冒发热期间暂停使用\r\n3. 高血压患者应在医生指导下使用\r\n4. 孕妇慎用\r\n5. 服用期间忌食萝卜、绿豆等`;\r\n\t\t},\r\n\r\n\t\t// 加入收藏\r\n\t\taddToCollection() {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '已加入收藏',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 查看相关食疗方案\r\n\t\tviewRecipes() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/recipe/recipe'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 跳转到具体食疗方案\r\n\t\tgoToRecipe(recipeId) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/recipe-detail/recipe-detail?id=${recipeId}`\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 120rpx;\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\theight: 400rpx;\r\n}\r\n\r\n.loading-text {\r\n\tcolor: #666;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n/* 药材头部信息 */\r\n.ingredient-header {\r\n\tbackground-color: #fff;\r\n\tpadding: 40rpx;\r\n\tdisplay: flex;\r\n\talign-items: flex-start;\r\n\tgap: 30rpx;\r\n}\r\n\r\n.ingredient-image-container {\r\n\twidth: 160rpx;\r\n\theight: 160rpx;\r\n\tborder-radius: 20rpx;\r\n\toverflow: hidden;\r\n}\r\n\r\n.ingredient-image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.ingredient-icon {\r\n\twidth: 160rpx;\r\n\theight: 160rpx;\r\n\tborder-radius: 20rpx;\r\n\tbackground-color: #4CAF50;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.icon-text {\r\n\tcolor: white;\r\n\tfont-size: 60rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.header-info {\r\n\tflex: 1;\r\n}\r\n\r\n.ingredient-name {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.ingredient-alias {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.ingredient-tags {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 10rpx;\r\n}\r\n\r\n.tag {\r\n\tbackground-color: #E8F5E8;\r\n\tcolor: #4CAF50;\r\n\tfont-size: 22rpx;\r\n\tpadding: 8rpx 16rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.ingredient-meta {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 15rpx;\r\n\tmargin-top: -20rpx; /* 向上移动类型区域 */\r\n}\r\n\r\n.meta-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n\r\n.meta-label {\r\n\tfont-size: 22rpx;\r\n\tcolor: #999;\r\n\tmargin-bottom: 5rpx;\r\n}\r\n\r\n.meta-value {\r\n\tfont-size: 24rpx;\r\n\tcolor: #333;\r\n\tbackground-color: #f5f5f5;\r\n\tpadding: 8rpx 15rpx;\r\n\tborder-radius: 15rpx;\r\n}\r\n\r\n/* 详细内容 */\r\n.detail-content {\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.section {\r\n\tbackground-color: #fff;\r\n\tpadding: 40rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.section-content {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.8;\r\n\twhite-space: pre-line;\r\n}\r\n\r\n/* 相关食疗方案 */\r\n.related-recipes {\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.recipe-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 20rpx 0;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.recipe-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.recipe-thumb {\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tborder-radius: 10rpx;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.recipe-info {\r\n\tflex: 1;\r\n}\r\n\r\n.recipe-name {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.recipe-effect {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.arrow {\r\n\tfont-size: 28rpx;\r\n\tcolor: #ccc;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.bottom-actions {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground-color: #fff;\r\n\tpadding: 30rpx 40rpx;\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n\tborder-top: 1rpx solid #eee;\r\n}\r\n\r\n.action-btn {\r\n\tflex: 1;\r\n\theight: 80rpx;\r\n\tborder-radius: 40rpx;\r\n\tfont-size: 28rpx;\r\n\tborder: none;\r\n}\r\n\r\n.action-btn.primary {\r\n\tbackground-color: #4CAF50;\r\n\tcolor: #fff;\r\n}\r\n\r\n.action-btn.secondary {\r\n\tbackground-color: #f5f5f5;\r\n\tcolor: #666;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/桌面/Medicine-uniapp/pages/ingredient-detail/ingredient-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "api"], "mappings": ";;;AAyFA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,cAAc;AAAA,MACd,SAAS;AAAA,MACT,WAAW;AAAA;AAAA,MACX,kBAAkB,CAAC;AAAA,IACpB;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,aAAa;AACZ,aAAO,KAAK,gBAAgB,KAAK,iBAAiB,YAAY,EAAE;AAAA,IACjE;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,QAAI,QAAQ,IAAI;AACf,WAAK,eAAe,QAAQ;AAC5B,WAAK,qBAAoB;AAAA,IAC1B;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,uBAAuB;AAC5B,UAAI;AACH,aAAK,UAAU;AACf,aAAK,YAAY;AACjBA,sBAAY,MAAA,MAAA,OAAA,wDAAA,cAAc,KAAK,YAAY;AAE3C,cAAM,WAAW,MAAMC,UAAG,IAAC,iBAAiB,KAAK,YAAY;AAC7DD,sBAAA,MAAA,MAAA,OAAA,wDAAY,cAAc,QAAQ;AAElC,YAAI,YAAY,SAAS,SAAS,OAAO,SAAS,MAAM;AACvD,eAAK,mBAAmB,SAAS;AACjCA,wBAAY,MAAA,MAAA,OAAA,wDAAA,aAAa,KAAK,gBAAgB;AAC9CA,mGAAY,aAAa,KAAK,iBAAiB,MAAM;AACrDA,mGAAY,YAAY,KAAK,iBAAiB,KAAK;AACnDA,8BAAA,MAAA,OAAA,wDAAY,2BAA2B,KAAK,iBAAiB,oBAAoB;AACjFA,mGAAY,eAAe,KAAK,iBAAiB,MAAM;AAAA,eACjD;AACN,gBAAM,IAAI,MAAM,UAAU;AAAA,QAC3B;AAAA,MACC,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,wDAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACT,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,cAAc;AACbA,0BAAY,MAAA,OAAA,wDAAA,WAAW,KAAK,iBAAiB,MAAM;AAAA,IACnD;AAAA;AAAA,IAGD,eAAe;AACdA,0BAAY,MAAA,OAAA,wDAAA,iBAAiB,KAAK,iBAAiB,MAAM;AAEzD,WAAK,YAAY;AAAA,IACjB;AAAA;AAAA,IAGD,YAAY,MAAM;AACjB,UAAI,CAAC;AAAM,eAAO;AAClB,aAAO,KAAK,OAAO,CAAC;AAAA,IACpB;AAAA;AAAA,IAGD,aAAa,UAAU;AACtB,UAAI,CAAC;AAAU,eAAO;AAEtB,YAAM,gBAAgB,SAAS,QAAQ,SAAS,EAAE,EAAE,QAAQ,OAAO,EAAE,EAAE,KAAI;AAG3E,UAAI,cAAc,SAAS,GAAG,KAAK,cAAc,SAAS,GAAG;AAAG,eAAO;AACvE,UAAI,cAAc,SAAS,GAAG,KAAK,cAAc,SAAS,GAAG;AAAG,eAAO;AACvE,UAAI,cAAc,SAAS,GAAG,KAAK,cAAc,SAAS,GAAG;AAAG,eAAO;AACvE,UAAI,cAAc,SAAS,GAAG,KAAK,cAAc,SAAS,GAAG;AAAG,eAAO;AACvE,UAAI,cAAc,SAAS,GAAG,KAAK,cAAc,SAAS,GAAG;AAAG,eAAO;AAEvE,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,gBAAgB,QAAQ;AACvB,UAAI,CAAC;AAAQ,eAAO,CAAC,IAAI;AACzB,YAAM,OAAO,CAAA;AAEb,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AACzC,UAAI,OAAO,SAAS,MAAM,KAAK,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,MAAM;AACtE,UAAI,OAAO,SAAS,KAAK,KAAK,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,KAAK;AACpE,UAAI,OAAO,SAAS,IAAI;AAAG,aAAK,KAAK,IAAI;AAEzC,UAAI,KAAK,WAAW,GAAG;AACtB,YAAI,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG;AAAG,eAAK,KAAK,IAAI;AAChE,YAAI,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG;AAAG,eAAK,KAAK,IAAI;AAChE,YAAI,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG;AAAG,eAAK,KAAK,IAAI;AAAA,MACjE;AAEA,aAAO,KAAK,MAAM,GAAG,CAAC;AAAA,IACtB;AAAA;AAAA,IAGD,wBAAwB;AACvB,YAAM,OAAO,KAAK,iBAAiB,QAAQ;AAC3C,YAAM,WAAW,KAAK,iBAAiB,YAAY;AAEnD,aAAO,GAAG,IAAI,UAAU,QAAQ,IAAI,IAAI;AAAA,IACxC;AAAA;AAAA,IAGD,iBAAiB;AAChB,YAAM,WAAW,KAAK,iBAAiB,YAAY;AAEnD,UAAI,SAAS,SAAS,IAAI,GAAG;AAC5B,eAAO;AAAA,MACR,WAAW,SAAS,SAAS,IAAI,GAAG;AACnC,eAAO;AAAA,MACR,WAAW,SAAS,SAAS,IAAI,GAAG;AACnC,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,wBAAwB;AACvB,aAAO;AAAA;AAAA;AAAA;AAAA;AAAA,IAKP;AAAA;AAAA,IAGD,wBAAwB;AACvB,aAAO;AAAA;AAAA;AAAA;AAAA;AAAA,IAKP;AAAA;AAAA,IAGD,kBAAkB;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,UAAU;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,yCAAyC,QAAQ;AAAA,MACvD,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjQA,GAAG,WAAW,eAAe;"}