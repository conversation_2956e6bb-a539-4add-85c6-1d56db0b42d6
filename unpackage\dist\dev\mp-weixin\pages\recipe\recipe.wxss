
.container.data-v-fb437fc6 {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 页面头部 */
.header.data-v-fb437fc6 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx 20rpx;
	background-color: #fff;
}
.header-title.data-v-fb437fc6 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}
.header-subtitle.data-v-fb437fc6 {
	font-size: 28rpx;
	color: #4CAF50;
}

/* 搜索栏 */
.search-section.data-v-fb437fc6 {
	background-color: #fff;
	padding: 30rpx 40rpx;
}
.search-box.data-v-fb437fc6 {
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
}
.search-icon.data-v-fb437fc6 {
	font-size: 32rpx;
	color: #999;
	margin-right: 20rpx;
}
.search-input.data-v-fb437fc6 {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

/* 筛选标签 */
.filter-section.data-v-fb437fc6 {
	background-color: #fff;
	padding: 20rpx 0;
	margin-bottom: 20rpx;
}
.filter-scroll.data-v-fb437fc6 {
	white-space: nowrap;
}
.filter-list.data-v-fb437fc6 {
	display: flex;
	padding: 0 40rpx;
}
.filter-item.data-v-fb437fc6 {
	flex-shrink: 0;
	padding: 15rpx 30rpx;
	margin-right: 20rpx;
	background-color: #f5f5f5;
	border-radius: 40rpx;
}
.filter-item.active.data-v-fb437fc6 {
	background-color: #4CAF50;
}
.filter-text.data-v-fb437fc6 {
	font-size: 26rpx;
	color: #666;
}
.filter-item.active .filter-text.data-v-fb437fc6 {
	color: #fff;
}

/* 食疗方案列表 */
.recipe-list.data-v-fb437fc6 {
	padding: 20rpx;
}
.recipe-card.data-v-fb437fc6 {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	position: relative;
}
.recipe-image.data-v-fb437fc6 {
	width: 100%;
	height: 400rpx;
}
.favorite-btn.data-v-fb437fc6 {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	width: 60rpx;
	height: 60rpx;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.favorite-icon.data-v-fb437fc6 {
	font-size: 32rpx;
	color: #ccc;
}
.favorite-icon.favorited.data-v-fb437fc6 {
	color: #4CAF50;
}
.recipe-info.data-v-fb437fc6 {
	padding: 30rpx;
}
.recipe-title.data-v-fb437fc6 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}
.recipe-suitable.data-v-fb437fc6 {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}
.recipe-description.data-v-fb437fc6 {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	display: block;
	margin-bottom: 25rpx;
}

/* 医生信息区域 */
.doctor-section.data-v-fb437fc6 {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 15rpx;
}
.doctor-avatar.data-v-fb437fc6 {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	overflow: hidden;
}
.doctor-avatar image.data-v-fb437fc6 {
	width: 100%;
	height: 100%;
}
.doctor-name.data-v-fb437fc6 {
	font-size: 24rpx;
	color: #666;
}

/* 统计信息区域 */
.stats-info.data-v-fb437fc6 {
	display: flex;
	align-items: center;
	gap: 20rpx;
}
.rating.data-v-fb437fc6 {
	display: flex;
	align-items: center;
}
.star.data-v-fb437fc6 {
	color: #FFD700;
	font-size: 24rpx;
	margin-right: 5rpx;
}
.rating-score.data-v-fb437fc6 {
	font-size: 24rpx;
	color: #666;
}
.view-count.data-v-fb437fc6 {
	display: flex;
	align-items: center;
}
.view-icon.data-v-fb437fc6 {
	font-size: 24rpx;
	margin-right: 5rpx;
}
.view-number.data-v-fb437fc6 {
	font-size: 24rpx;
	color: #666;
}
.like-count.data-v-fb437fc6 {
	display: flex;
	align-items: center;
	cursor: pointer;
}
.like-icon.data-v-fb437fc6 {
	font-size: 24rpx;
	margin-right: 5rpx;
	color: #ccc;
	transition: all 0.3s ease;
}
.like-icon.liked.data-v-fb437fc6 {
	color: #ff4757;
	transform: scale(1.2);
}
.like-number.data-v-fb437fc6 {
	font-size: 24rpx;
	color: #666;
}

/* 底部导航 */
.bottom-nav.data-v-fb437fc6 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-around;
	border-top: 1rpx solid #eee;
	z-index: 999;
}
.nav-item.data-v-fb437fc6 {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
}
.nav-item.active .nav-text.data-v-fb437fc6 {
	color: #4CAF50;
}
.nav-item.add-btn.data-v-fb437fc6 {
	position: relative;
}
.nav-icon.data-v-fb437fc6 {
	font-size: 40rpx;
	margin-bottom: 8rpx;
}
.add-icon.data-v-fb437fc6 {
	background-color: #4CAF50;
	color: #fff;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 50rpx;
	font-weight: bold;
}
.nav-text.data-v-fb437fc6 {
	font-size: 20rpx;
	color: #666;
}

/* 加载状态样式 */
.loading-container.data-v-fb437fc6 {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}
.loading-text.data-v-fb437fc6 {
	font-size: 28rpx;
	color: #999;
}

/* 空状态样式 */
.empty-container.data-v-fb437fc6 {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}
.empty-text.data-v-fb437fc6 {
	font-size: 28rpx;
	color: #999;
}

/* 加载更多样式 */
.loading-more.data-v-fb437fc6 {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 30rpx 0;
}
.no-more.data-v-fb437fc6 {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 30rpx 0;
}
.no-more-text.data-v-fb437fc6 {
	font-size: 24rpx;
	color: #ccc;
}
