<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: #ffffff;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .leaf-icon {
            font-size: 20px;
            margin-right: 8px;
            color: #52c41a;
        }
        
        .app-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .search-icon, .notification-icon {
            font-size: 18px;
            color: #666;
        }
        
        .avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #ddd;
        }
        
        /* 轮播图样式 */
        .carousel-section {
            margin: 10px 15px;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }
        
        .carousel-container {
            position: relative;
            height: 150px;
            overflow: hidden;
            border-radius: 10px;
        }
        
        .carousel-item {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }
        
        .carousel-item.active {
            opacity: 1;
        }
        
        .carousel-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .carousel-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            padding: 15px 20px 20px;
        }
        
        .carousel-title {
            font-size: 21px;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            line-height: 1.2;
        }
        
        /* 指示器 */
        .carousel-indicators {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }
        
        .indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .indicator.active {
            background: #ffffff;
        }
        
        /* 默认横幅样式 */
        .default-banner {
            background: linear-gradient(135deg, #52c41a 0%, #73d13d 50%, #95de64 100%);
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 20px;
        }
        
        .banner-title {
            font-size: 21px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 8px;
        }
        
        .banner-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .status {
            padding: 15px;
            text-align: center;
            color: #666;
        }
        
        .error {
            color: #f56565;
        }
        
        .success {
            color: #48bb78;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="header-left">
                <span class="leaf-icon">🍃</span>
                <span class="app-title">药食同源</span>
            </div>
            <div class="header-right">
                <span class="search-icon">🔍</span>
                <span class="notification-icon">🔔</span>
                <div class="avatar"></div>
            </div>
        </div>
        
        <!-- 轮播图 -->
        <div class="carousel-section">
            <div id="carousel-container" class="carousel-container">
                <!-- 轮播图内容将通过JavaScript动态生成 -->
            </div>
            <div id="carousel-indicators" class="carousel-indicators">
                <!-- 指示器将通过JavaScript动态生成 -->
            </div>
        </div>
        
        <div id="status" class="status">正在加载轮播图...</div>
    </div>

    <script>
        let carouselData = [];
        let currentIndex = 0;
        let carouselInterval;

        // 模拟API调用
        async function loadCarouselData() {
            try {
                const response = await fetch('http://localhost:8080/carousel/list');
                const result = await response.json();
                
                if (result.code === 200 && result.data) {
                    carouselData = result.data.filter(item => item.status === 1);
                    if (carouselData.length > 0) {
                        renderCarousel();
                        startAutoPlay();
                        document.getElementById('status').innerHTML = '<span class="success">轮播图加载成功！</span>';
                    } else {
                        showDefaultBanner();
                        document.getElementById('status').innerHTML = '<span class="error">没有可用的轮播图数据</span>';
                    }
                } else {
                    showDefaultBanner();
                    document.getElementById('status').innerHTML = '<span class="error">轮播图数据格式错误</span>';
                }
            } catch (error) {
                console.error('加载轮播图失败:', error);
                showDefaultBanner();
                document.getElementById('status').innerHTML = '<span class="error">网络错误，显示默认横幅</span>';
            }
        }

        function renderCarousel() {
            const container = document.getElementById('carousel-container');
            const indicators = document.getElementById('carousel-indicators');
            
            // 清空容器
            container.innerHTML = '';
            indicators.innerHTML = '';
            
            // 生成轮播图项
            carouselData.forEach((item, index) => {
                const carouselItem = document.createElement('div');
                carouselItem.className = `carousel-item ${index === 0 ? 'active' : ''}`;
                carouselItem.innerHTML = `
                    <img class="carousel-image" src="${item.imageUrl}" alt="${item.title}" onerror="this.src='/static/logo.png'">
                    <div class="carousel-overlay">
                        <div class="carousel-title">${item.title}</div>
                    </div>
                `;
                carouselItem.addEventListener('click', () => onCarouselClick(item));
                container.appendChild(carouselItem);
                
                // 生成指示器
                const indicator = document.createElement('div');
                indicator.className = `indicator ${index === 0 ? 'active' : ''}`;
                indicator.addEventListener('click', () => goToSlide(index));
                indicators.appendChild(indicator);
            });
        }

        function showDefaultBanner() {
            const container = document.getElementById('carousel-container');
            container.innerHTML = `
                <div class="carousel-item active default-banner">
                    <div class="banner-title">传统智慧 · 现代健康</div>
                    <div class="banner-subtitle">探索药食同源的养生之道</div>
                </div>
            `;
            document.getElementById('carousel-indicators').innerHTML = '';
        }

        function goToSlide(index) {
            const items = document.querySelectorAll('.carousel-item');
            const indicators = document.querySelectorAll('.indicator');
            
            items[currentIndex].classList.remove('active');
            indicators[currentIndex]?.classList.remove('active');
            
            currentIndex = index;
            
            items[currentIndex].classList.add('active');
            indicators[currentIndex]?.classList.add('active');
        }

        function nextSlide() {
            if (carouselData.length === 0) return;
            const nextIndex = (currentIndex + 1) % carouselData.length;
            goToSlide(nextIndex);
        }

        function startAutoPlay() {
            if (carouselData.length <= 1) return;
            carouselInterval = setInterval(nextSlide, 3000);
        }

        function onCarouselClick(item) {
            console.log('点击轮播图:', item);
            alert(`点击了轮播图: ${item.title}`);
        }

        // 页面加载时启动
        document.addEventListener('DOMContentLoaded', loadCarouselData);
    </script>
</body>
</html>
