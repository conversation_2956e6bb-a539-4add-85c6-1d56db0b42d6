{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "../产出物/安装包/HBuilderX/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\n\t<view class=\"login-container\">\n\t\t<view class=\"login-header\">\n\t\t\t<image class=\"logo\" src=\"/static/logo.png\" mode=\"aspectFit\"></image>\n\t\t\t<text class=\"app-name\">中医食疗</text>\n\t\t\t<text class=\"welcome-text\">欢迎使用中医食疗养生平台</text>\n\t\t</view>\n\t\t\n\t\t<view class=\"login-form\">\n\t\t\t<view class=\"input-group\">\n\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t<text class=\"input-icon\">📱</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"input-field\" \n\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\tplaceholder=\"请输入手机号\" \n\t\t\t\t\t\tv-model=\"phone\"\n\t\t\t\t\t\tmaxlength=\"11\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t<text class=\"input-icon\">🔒</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"input-field\" \n\t\t\t\t\t\t:type=\"showPassword ? 'text' : 'password'\" \n\t\t\t\t\t\tplaceholder=\"请输入密码\" \n\t\t\t\t\t\tv-model=\"password\"\n\t\t\t\t\t/>\n\t\t\t\t\t<text class=\"password-toggle\" @click=\"togglePassword\">\n\t\t\t\t\t\t{{ showPassword ? '👁️' : '👁️‍🗨️' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<button class=\"login-btn\" @click=\"handleLogin\" :disabled=\"!canLogin\">\n\t\t\t\t{{ isLoading ? '登录中...' : '立即登录' }}\n\t\t\t</button>\n\t\t\t\n\t\t\t<view class=\"login-options\">\n\t\t\t\t<text class=\"register-link\" @click=\"goToRegister\">还没有账号？立即注册</text>\n\t\t\t\t<text class=\"forgot-link\" @click=\"forgotPassword\">忘记密码？</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"login-footer\">\n\t\t\t<text class=\"agreement-text\">\n\t\t\t\t登录即表示同意\n\t\t\t\t<text class=\"link-text\" @click=\"showAgreement\">《用户协议》</text>\n\t\t\t\t和\n\t\t\t\t<text class=\"link-text\" @click=\"showPrivacy\">《隐私政策》</text>\n\t\t\t</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tphone: '',\n\t\t\tpassword: '',\n\t\t\tshowPassword: false,\n\t\t\tisLoading: false\n\t\t}\n\t},\n\t\n\tcomputed: {\n\t\tcanLogin() {\n\t\t\treturn this.phone.length === 11 && this.password.length >= 6 && !this.isLoading;\n\t\t}\n\t},\n\t\n\tmethods: {\n\t\t// 切换密码显示\n\t\ttogglePassword() {\n\t\t\tthis.showPassword = !this.showPassword;\n\t\t},\n\t\t\n\t\t// 处理登录\n\t\tasync handleLogin() {\n\t\t\tif (!this.canLogin) return;\n\t\t\t\n\t\t\t// 简单的手机号验证\n\t\t\tconst phoneReg = /^1[3-9]\\d{9}$/;\n\t\t\tif (!phoneReg.test(this.phone)) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入正确的手机号',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.isLoading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 模拟登录请求\n\t\t\t\tawait this.simulateLogin();\n\t\t\t\t\n\t\t\t\t// 保存用户信息到本地存储\n\t\t\t\tconst userInfo = {\n\t\t\t\t\tphone: this.phone,\n\t\t\t\t\tnickname: '养生达人',\n\t\t\t\t\tavatar: '/static/avatar.png',\n\t\t\t\t\tloginTime: Date.now()\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tuni.setStorageSync('userInfo', userInfo);\n\t\t\t\tuni.setStorageSync('token', 'mock_token_' + Date.now());\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '登录成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 延迟跳转回个人中心\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '登录失败，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.isLoading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 模拟登录请求\n\t\tsimulateLogin() {\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t// 模拟登录成功\n\t\t\t\t\tif (this.password.length >= 6) {\n\t\t\t\t\t\tresolve();\n\t\t\t\t\t} else {\n\t\t\t\t\t\treject(new Error('密码错误'));\n\t\t\t\t\t}\n\t\t\t\t}, 2000);\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到注册页面\n\t\tgoToRegister() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '注册功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 忘记密码\n\t\tforgotPassword() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '找回密码功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 显示用户协议\n\t\tshowAgreement() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '用户协议',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 显示隐私政策\n\t\tshowPrivacy() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '隐私政策',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n\t.login-container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tpadding: 60rpx 40rpx 40rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t.login-header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 80rpx;\n\t}\n\t\n\t.logo {\n\t\twidth: 120rpx;\n\t\theight: 120rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.app-name {\n\t\tdisplay: block;\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: white;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.welcome-text {\n\t\tdisplay: block;\n\t\tfont-size: 28rpx;\n\t\tcolor: rgba(255, 255, 255, 0.8);\n\t}\n\t\n\t.login-form {\n\t\tflex: 1;\n\t}\n\t\n\t.input-group {\n\t\tmargin-bottom: 60rpx;\n\t}\n\t\n\t.input-item {\n\t\tbackground: rgba(255, 255, 255, 0.9);\n\t\tborder-radius: 50rpx;\n\t\tpadding: 0 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\theight: 100rpx;\n\t}\n\t\n\t.input-icon {\n\t\tfont-size: 32rpx;\n\t\tmargin-right: 20rpx;\n\t\twidth: 40rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.input-field {\n\t\tflex: 1;\n\t\tfont-size: 32rpx;\n\t\tcolor: #333;\n\t}\n\t\n\t.password-toggle {\n\t\tfont-size: 32rpx;\n\t\tpadding: 10rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.login-btn {\n\t\twidth: 100%;\n\t\theight: 100rpx;\n\t\tbackground: linear-gradient(45deg, #4CAF50, #45a049);\n\t\tcolor: white;\n\t\tborder: none;\n\t\tborder-radius: 50rpx;\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 40rpx;\n\t}\n\t\n\t.login-btn:disabled {\n\t\tbackground: #ccc;\n\t\tcolor: #999;\n\t}\n\t\n\t.login-options {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\t\n\t.register-link,\n\t.forgot-link {\n\t\tcolor: rgba(255, 255, 255, 0.9);\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.login-footer {\n\t\ttext-align: center;\n\t\tmargin-top: 60rpx;\n\t}\n\t\n\t.agreement-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: rgba(255, 255, 255, 0.7);\n\t\tline-height: 1.5;\n\t}\n\t\n\t.link-text {\n\t\tcolor: rgba(255, 255, 255, 0.9);\n\t\ttext-decoration: underline;\n\t}\n</style>\n", "import MiniProgramPage from 'D:/桌面/Medicine-uniapp/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAyDA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,MACd,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EAED,UAAU;AAAA,IACT,WAAW;AACV,aAAO,KAAK,MAAM,WAAW,MAAM,KAAK,SAAS,UAAU,KAAK,CAAC,KAAK;AAAA,IACvE;AAAA,EACA;AAAA,EAED,SAAS;AAAA;AAAA,IAER,iBAAiB;AAChB,WAAK,eAAe,CAAC,KAAK;AAAA,IAC1B;AAAA;AAAA,IAGD,MAAM,cAAc;AACnB,UAAI,CAAC,KAAK;AAAU;AAGpB,YAAM,WAAW;AACjB,UAAI,CAAC,SAAS,KAAK,KAAK,KAAK,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,WAAK,YAAY;AAEjB,UAAI;AAEH,cAAM,KAAK;AAGX,cAAM,WAAW;AAAA,UAChB,OAAO,KAAK;AAAA,UACZ,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,WAAW,KAAK,IAAI;AAAA;AAGrBA,sBAAAA,MAAI,eAAe,YAAY,QAAQ;AACvCA,sBAAG,MAAC,eAAe,SAAS,gBAAgB,KAAK,IAAG,CAAE;AAEtDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAGD,mBAAW,MAAM;AAChBA,wBAAG,MAAC,aAAY;AAAA,QAChB,GAAE,IAAI;AAAA,MAEN,SAAO,OAAO;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACT,aAAK,YAAY;AAAA,MAClB;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB;AACf,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,mBAAW,MAAM;AAEhB,cAAI,KAAK,SAAS,UAAU,GAAG;AAC9B;iBACM;AACN,mBAAO,IAAI,MAAM,MAAM,CAAC;AAAA,UACzB;AAAA,QACA,GAAE,GAAI;AAAA,MACR,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;AC/KA,GAAG,WAAW,eAAe;"}