<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-left">
				<text class="leaf-icon">🍃</text>
				<text class="app-title">药食同源</text>
			</view>
			<view class="header-right">
				<text class="search-icon">🔍</text>
				<text class="notification-icon">🔔</text>
				<image class="avatar" src="/static/logo.png" mode="aspectFill"></image>
			</view>
		</view>

		<!-- 轮播图 -->
		<view class="carousel-section fade-in">
			<swiper class="carousel-swiper"
					:indicator-dots="true"
					:autoplay="true"
					:interval="3000"
					:duration="500"
					indicator-color="rgba(255, 255, 255, 0.5)"
					indicator-active-color="#ffffff"
					circular>
				<swiper-item v-for="(item, index) in carouselList" :key="item.id" @click="onCarouselClick(item)">
					<view class="carousel-item">
						<image class="carousel-image" :src="item.imageUrl" mode="aspectFill" @error="onCarouselImageError(item, index)"></image>
						<view class="carousel-overlay">
							<text class="carousel-title">{{ item.title }}</text>
						</view>
					</view>
				</swiper-item>
				<!-- 如果没有轮播图数据，显示默认横幅 -->
				<swiper-item v-if="carouselList.length === 0">
					<view class="carousel-item default-banner">
						<view class="banner-bg-placeholder"></view>
						<view class="banner-overlay">
							<text class="banner-title">传统智慧 · 现代健康</text>
							<text class="banner-subtitle">探索药食同源的养生之道</text>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 功能导航 -->
		<view class="nav-section">
			<view class="nav-grid">
				<view class="nav-item fade-in card-hover" @click="goToIngredients" style="animation-delay: 0.1s">
					<text class="nav-icon">�</text>
					<text class="nav-text">药材食材</text>
				</view>
				<view class="nav-item fade-in card-hover" @click="goToRecipe" style="animation-delay: 0.2s">
					<text class="nav-icon">📋</text>
					<text class="nav-text">食疗方案</text>
				</view>
				<view class="nav-item fade-in card-hover" @click="showComingSoon" style="animation-delay: 0.3s">
					<text class="nav-icon">📊</text>
					<text class="nav-text">营养分析</text>
				</view>
				<view class="nav-item fade-in card-hover" @click="goToTest" style="animation-delay: 0.4s">
					<text class="nav-icon">💬</text>
					<text class="nav-text">互动问答</text>
				</view>
			</view>
		</view>

		<!-- 体质测试卡片 -->
		<view class="test-section">
			<view class="test-card fade-in card-hover" @click="goToTest" style="animation-delay: 0.5s">
				<view class="test-content">
					<text class="test-title">不知道自己的体质？</text>
					<text class="test-subtitle">3分钟体质测试，获取专属食疗推荐</text>
				</view>
				<view class="test-button">
					<text class="test-btn-text">开始测试</text>
				</view>
			</view>
		</view>

		<!-- 药材与食材推荐 -->
		<view class="ingredients-section">
			<view class="section-header">
				<text class="section-title">药材与食材</text>
				<text class="section-more" @click="goToIngredients">查看全部 ></text>
			</view>

			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<text class="loading-text">正在加载推荐药材...</text>
			</view>

			<!-- 药材网格 -->
			<view v-else class="ingredients-grid">
				<view class="ingredient-card fade-in card-hover" v-for="(item, index) in featuredIngredients" :key="item.id" @click="goToIngredientDetail(item)" :style="{ 'animation-delay': (0.1 * index) + 's' }">
					<!-- 显示真实图片，失败时显示图标 -->
					<view class="ingredient-image-container">
						<image v-if="item.image && item.image !== '/static/logo.png'"
							   class="ingredient-image"
							   :src="item.image"
							   mode="aspectFill"
							   @error="onImageError(item, index)"></image>
						<view v-else class="ingredient-icon">
							<text class="icon-text">{{ getIconText(item.name) }}</text>
						</view>
					</view>
					<view class="ingredient-info">
						<text class="ingredient-name">{{ item.name }}</text>
						<text class="ingredient-nature">性味：{{ item.nature }}</text>
						<text class="ingredient-effect">归经：{{ item.meridian }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 营养与功效分析 -->
		<view class="analysis-section">
			<view class="section-header">
				<text class="section-title">营养与功效分析</text>
				<text class="section-more" @click="showComingSoon">分析工具 ></text>
			</view>

			<!-- 食材营养成分对比 -->
			<view class="nutrition-card">
				<text class="card-title">食材营养成分对比</text>
				<text class="card-subtitle">蛋白质含量</text>
				<view class="chart-container">
					<view class="bar-chart">
						<view class="bar-item">
							<view class="bar green" style="height: 160rpx;"></view>
							<text class="bar-label">山药</text>
						</view>
						<view class="bar-item">
							<view class="bar orange" style="height: 120rpx;"></view>
							<text class="bar-label">莲子</text>
						</view>
						<view class="bar-item">
							<view class="bar green" style="height: 140rpx;"></view>
							<text class="bar-label">百合</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 食疗方案功效分析 -->
			<view class="efficacy-card">
				<text class="card-title">食疗方案功效分析</text>
				<view class="efficacy-list">
					<view class="efficacy-item">
						<text class="efficacy-name">补气功效</text>
						<view class="progress-container">
							<view class="progress-bar">
								<view class="progress-fill" style="width: 85%;"></view>
							</view>
							<text class="progress-text">85%</text>
						</view>
					</view>
					<view class="efficacy-item">
						<text class="efficacy-name">养血功效</text>
						<view class="progress-container">
							<view class="progress-bar">
								<view class="progress-fill" style="width: 70%;"></view>
							</view>
							<text class="progress-text">70%</text>
						</view>
					</view>
					<view class="efficacy-item">
						<text class="efficacy-name">滋阴功效</text>
						<view class="progress-container">
							<view class="progress-bar">
								<view class="progress-fill" style="width: 40%;"></view>
							</view>
							<text class="progress-text">40%</text>
						</view>
					</view>
					<view class="efficacy-item">
						<text class="efficacy-name">润燥功效</text>
						<view class="progress-container">
							<view class="progress-bar">
								<view class="progress-fill" style="width: 60%;"></view>
							</view>
							<text class="progress-text">60%</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部导航 -->
		<view class="bottom-nav">
			<view class="nav-item active" @click="goToPage('home')">
				<text class="nav-icon">🏠</text>
				<text class="nav-text">首页</text>
			</view>
			<view class="nav-item" @click="goToPage('ingredients')">
				<text class="nav-icon">🌿</text>
				<text class="nav-text">药材食材</text>
			</view>
			<view class="nav-item add-btn">
				<text class="nav-icon add-icon">+</text>
			</view>
			<view class="nav-item" @click="goToPage('recipe')">
				<text class="nav-icon">📋</text>
				<text class="nav-text">食疗方案</text>
			</view>
			<view class="nav-item" @click="goToPage('profile')">
				<text class="nav-icon">👤</text>
				<text class="nav-text">我的</text>
			</view>
		</view>
	</view>
</template>

<script>
	import api from '@/utils/api.js';

	export default {
		data() {
			return {
				title: '药食同源',
				featuredIngredients: [],
				carouselList: [],
				loading: false
			}
		},
		onLoad() {
			this.loadCarouselList();
			this.loadFeaturedIngredients();
		},
		methods: {
			// 加载轮播图列表
			async loadCarouselList() {
				try {
					console.log('开始加载轮播图...');
					const response = await api.getCarouselList();
					console.log('轮播图API响应:', response);

					if (response && response.code === 200 && response.data) {
						// 过滤启用状态的轮播图
						this.carouselList = response.data.filter(item => item.status === 1);
						console.log('轮播图加载成功:', this.carouselList);
					} else {
						console.log('没有获取到轮播图数据或数据格式错误');
						this.carouselList = [];
					}
				} catch (error) {
					console.error('加载轮播图失败:', error);
					this.carouselList = [];
				}
			},

			// 轮播图点击事件
			onCarouselClick(item) {
				console.log('点击轮播图:', item);
				if (item.redirectUrl) {
					// 如果有跳转链接，可以在这里处理跳转逻辑
					// 例如：uni.navigateTo({ url: item.redirectUrl });
					uni.showToast({
						title: '跳转功能开发中...',
						icon: 'none'
					});
				}
			},

			// 轮播图图片加载错误处理
			onCarouselImageError(item, index) {
				console.error('轮播图图片加载失败:', item);
				// 可以设置默认图片或移除该项
				this.carouselList[index].imageUrl = '/static/logo.png';
			},

			// 加载推荐药材食材（前4条）
			async loadFeaturedIngredients() {
				try {
					this.loading = true;
					console.log('开始加载首页推荐药材...');

					const response = await api.getMedicinalList();
					console.log('API响应:', response);

					if (response && response.length > 0) {
						// 只取前4条数据
						const ingredients = response.slice(0, 4).map(item => ({
							id: item.id,
							name: item.name,
							nature: this.formatProperty(item.property),
							meridian: this.formatMeridian(item.meridian),
							image: item.avatar || '/static/logo.png',
							// 保存原始数据用于跳转详情页
							originalData: item
						}));

						this.featuredIngredients = ingredients;
						console.log('首页推荐药材加载成功:', this.featuredIngredients);
					} else {
						console.log('没有获取到药材数据');
					}
				} catch (error) {
					console.error('加载推荐药材失败:', error);
					// 如果API失败，使用默认数据
					this.featuredIngredients = this.getDefaultIngredients();
				} finally {
					this.loading = false;
				}
			},

			// 格式化性味信息
			formatProperty(property) {
				if (!property) return '甘、平';
				// 清理换行符并截取前面部分
				const cleaned = property.replace(/\r\n/g, '').replace(/\n/g, '').trim();
				return cleaned.length > 10 ? cleaned.substring(0, 10) + '...' : cleaned;
			},

			// 格式化归经信息
			formatMeridian(meridian) {
				if (!meridian) return '归脾、肺经';
				// 清理换行符并提取归经信息
				const cleaned = meridian.replace(/\r\n/g, '').replace(/\n/g, '').trim();

				// 如果包含归经信息，直接返回
				if (cleaned.includes('归') && cleaned.includes('经')) {
					return cleaned.length > 15 ? cleaned.substring(0, 15) + '...' : cleaned;
				}

				// 否则根据功效推断归经
				if (cleaned.includes('补气') || cleaned.includes('脾')) return '归脾、肺经';
				if (cleaned.includes('补血') || cleaned.includes('肝')) return '归肝、心经';
				if (cleaned.includes('补肾') || cleaned.includes('肾')) return '归肾经';

				return '归脾、肺经';
			},

			// 获取默认数据（API失败时使用）
			getDefaultIngredients() {
				return [
					{
						id: 1,
						name: '人参',
						nature: '甘、微苦、平',
						meridian: '归脾、肺、心经',
						image: '/static/logo.png'
					},
					{
						id: 2,
						name: '枸杞',
						nature: '甘、平',
						meridian: '归肝、肾经',
						image: '/static/logo.png'
					},
					{
						id: 3,
						name: '山药',
						nature: '甘、平',
						meridian: '归脾、肺、肾经',
						image: '/static/logo.png'
					},
					{
						id: 4,
						name: '莲子',
						nature: '甘、平',
						meridian: '归脾、肾、心经',
						image: '/static/logo.png'
					}
				];
			},

			goToRecipe() {
				uni.navigateTo({
					url: '/pages/recipe/recipe'
				});
			},

			goToIngredients() {
				uni.navigateTo({
					url: '/pages/ingredients/ingredients'
				});
			},

			goToProfile() {
				uni.navigateTo({
					url: '/pages/profile/profile'
				});
			},
			goToIngredientDetail(item) {
				uni.navigateTo({
					url: `/pages/ingredient-detail/ingredient-detail?id=${item.id}&name=${item.name}`
				});
			},
			goToPage(page) {
				switch(page) {
					case 'home':
						// 当前页面，不需要跳转
						break;
					case 'ingredients':
						this.goToIngredients();
						break;
					case 'recipe':
						this.goToRecipe();
						break;
					case 'profile':
						this.goToProfile();
						break;
				}
			},
			goToTest() {
				uni.navigateTo({
					url: '/pages/test/test'
				});
			},

			showComingSoon() {
				uni.showToast({
					title: '功能开发中...',
					icon: 'none'
				});
			},

			// 药材图片加载错误处理
			onImageError(item, index) {
				console.error('药材图片加载失败:', item);
				// 设置为默认图片
				this.featuredIngredients[index].image = '/static/logo.png';
			},

			// 获取图标文字（当图片加载失败时显示）
			getIconText(name) {
				if (!name) return '药';
				return name.charAt(0);
			}
		}
	}
</script>

<style scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}

	/* 顶部导航栏 */
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.header-left {
		display: flex;
		align-items: center;
	}

	.leaf-icon {
		font-size: 40rpx;
		margin-right: 15rpx;
		color: #52c41a;
	}

	.app-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.header-right {
		display: flex;
		align-items: center;
		gap: 25rpx;
	}

	.search-icon, .notification-icon {
		font-size: 36rpx;
		color: #666;
	}

	.avatar {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
	}

	/* 轮播图样式 */
	.carousel-section {
		margin: 20rpx 30rpx;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.carousel-swiper {
		height: 300rpx;
		border-radius: 20rpx;
	}

	.carousel-item {
		position: relative;
		width: 100%;
		height: 100%;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.carousel-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.carousel-overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
		padding: 30rpx 40rpx 40rpx;
	}

	.carousel-title {
		font-size: 42rpx;
		font-weight: bold;
		color: #ffffff;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
		line-height: 1.2;
	}

	/* 默认横幅样式（当没有轮播图时显示） */
	.default-banner {
		position: relative;
	}

	.banner-bg-placeholder {
		width: 100%;
		height: 100%;
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 50%, #95de64 100%);
		background-image:
			radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
			radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
	}

	.banner-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(45deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.3));
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 40rpx;
	}

	.banner-title {
		font-size: 42rpx;
		font-weight: bold;
		color: #ffffff;
		margin-bottom: 15rpx;
	}

	.banner-subtitle {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.9);
	}

	/* 功能导航 */
	.nav-section {
		padding: 30rpx;
	}

	.nav-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 30rpx;
	}

	.nav-item {
		background-color: #ffffff;
		border-radius: 15rpx;
		padding: 30rpx 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;
	}

	.nav-icon {
		display: block;
		font-size: 48rpx;
		margin-bottom: 15rpx;
		color: #52c41a;
	}

	.nav-text {
		font-size: 24rpx;
		color: #666;
	}

	/* 体质测试卡片样式 */
	.test-section {
		padding: 20rpx 30rpx;
		margin-top: 20rpx;
	}

	.test-card {
		background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
		border-radius: 16rpx;
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 6rpx 24rpx rgba(76, 175, 80, 0.3);
		transition: all 0.3s ease;
	}

	.test-content {
		flex: 1;
	}

	.test-title {
		font-size: 32rpx;
		color: white;
		font-weight: 600;
		display: block;
		margin-bottom: 8rpx;
	}

	.test-subtitle {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.9);
		line-height: 1.4;
	}

	.test-button {
		background: rgba(255, 255, 255, 0.2);
		border: 2rpx solid rgba(255, 255, 255, 0.3);
		border-radius: 50rpx;
		padding: 16rpx 32rpx;
		backdrop-filter: blur(10rpx);
	}

	.test-btn-text {
		color: white;
		font-size: 28rpx;
		font-weight: 500;
	}

	/* 药材与食材推荐 */
	.ingredients-section {
		padding: 0 30rpx;
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.section-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.section-more {
		font-size: 28rpx;
		color: #52c41a;
	}

	.ingredients-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
	}

	.ingredient-card {
		background-color: #ffffff;
		border-radius: 15rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;
	}

	.ingredient-image-container {
		position: relative;
		width: 100%;
		height: 200rpx;
	}

	.ingredient-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.ingredient-icon {
		width: 100%;
		height: 100%;
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.icon-text {
		font-size: 60rpx;
		font-weight: bold;
		color: #ffffff;
	}

	.ingredient-info {
		padding: 20rpx;
	}

	.ingredient-name {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}

	.ingredient-nature, .ingredient-effect {
		display: block;
		font-size: 24rpx;
		color: #666;
		margin-bottom: 5rpx;
	}

	/* 加载状态样式 */
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 200rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #999;
	}

	/* 营养与功效分析 */
	.analysis-section {
		padding: 0 30rpx;
		margin-top: 40rpx;
	}

	.nutrition-card, .efficacy-card {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	}

	.card-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 10rpx;
		display: block;
	}

	.card-subtitle {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 30rpx;
		display: block;
	}

	/* 柱状图样式 */
	.chart-container {
		padding: 20rpx 0;
	}

	.bar-chart {
		display: flex;
		justify-content: space-around;
		align-items: flex-end;
		height: 200rpx;
	}

	.bar-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.bar {
		width: 60rpx;
		border-radius: 8rpx 8rpx 0 0;
		margin-bottom: 15rpx;
		transition: all 0.3s ease;
	}

	.bar.green {
		background: linear-gradient(to top, #4CAF50, #66BB6A);
	}

	.bar.orange {
		background: linear-gradient(to top, #FF9800, #FFB74D);
	}

	.bar-label {
		font-size: 24rpx;
		color: #666;
	}

	/* 功效分析样式 */
	.efficacy-list {
		margin-top: 20rpx;
	}

	.efficacy-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 25rpx;
	}

	.efficacy-name {
		font-size: 28rpx;
		color: #333;
		width: 160rpx;
	}

	.progress-container {
		display: flex;
		align-items: center;
		flex: 1;
		margin-left: 20rpx;
	}

	.progress-bar {
		flex: 1;
		height: 12rpx;
		background: #f0f0f0;
		border-radius: 6rpx;
		overflow: hidden;
		margin-right: 15rpx;
	}

	.progress-fill {
		height: 100%;
		background: linear-gradient(to right, #4CAF50, #66BB6A);
		border-radius: 6rpx;
		transition: width 0.8s ease;
	}

	.progress-text {
		font-size: 24rpx;
		color: #4CAF50;
		font-weight: 600;
		width: 60rpx;
		text-align: right;
	}

	/* 底部导航 */
	.bottom-nav {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 120rpx;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: space-around;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		z-index: 1000;
	}

	.bottom-nav .nav-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 10rpx;
		background: none;
		box-shadow: none;
		transition: all 0.3s ease;
	}

	.bottom-nav .nav-item.active .nav-icon {
		color: #52c41a;
	}

	.bottom-nav .nav-item.active .nav-text {
		color: #52c41a;
	}

	.bottom-nav .nav-icon {
		font-size: 40rpx;
		margin-bottom: 5rpx;
		color: #999;
	}

	.bottom-nav .nav-text {
		font-size: 20rpx;
		color: #999;
	}

	.add-btn {
		position: relative;
	}

	.add-icon {
		background: linear-gradient(45deg, #52c41a, #73d13d);
		color: #ffffff;
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 48rpx;
		font-weight: bold;
		box-shadow: 0 4rpx 15rpx rgba(82, 196, 26, 0.3);
	}

	/* 动画效果 */
	.fade-in {
		opacity: 0;
		transform: translateY(30rpx);
		animation: fadeInUp 0.8s ease forwards;
	}

	@keyframes fadeInUp {
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.card-hover:active {
		transform: scale(0.95);
	}
</style>
