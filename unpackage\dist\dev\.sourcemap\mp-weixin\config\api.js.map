{"version": 3, "file": "api.js", "sources": ["config/api.js"], "sourcesContent": ["/**\r\n * API配置文件\r\n * 管理不同环境的API地址\r\n */\r\n\r\n// 环境配置\r\nconst ENV = {\r\n  // 开发环境\r\n  development: {\r\n    baseUrl: 'http://localhost:8083',\r\n    timeout: 10000\r\n  },\r\n  // 生产环境\r\n  production: {\r\n    baseUrl: 'http://localhost:8083',\r\n    timeout: 15000\r\n  }\r\n};\r\n\r\n// 当前环境（可以根据实际部署情况调整）\r\nconst currentEnv = 'development';\r\n\r\n// 导出配置\r\nexport const API_CONFIG = ENV[currentEnv];\r\n\r\n// 接口路径配置\r\nexport const API_PATHS = {\r\n  // 食疗方案相关\r\n  RECIPE: {\r\n    LIST: '/recipe/list',\r\n    DETAIL: '/recipe/detail',\r\n    FAVORITE: '/recipe/favorite',\r\n    VIEW_COUNT: '/recipe/view',\r\n    SEARCH_LOG: '/recipe/search-log',\r\n    LIKE: '/recipe/like',\r\n    BATCH_LIKE_STATUS: '/recipe/like/batch-status'\r\n  },\r\n  // 测试接口\r\n  TEST: {\r\n    HEALTH: '/test/health'\r\n  }\r\n}; "], "names": [], "mappings": ";AAMA,MAAM,MAAM;AAAA;AAAA,EAEV,aAAa;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,EACV;AAAA;AAAA,EAED,YAAY;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,EACV;AACH;AAGA,MAAM,aAAa;AAGP,MAAC,aAAa,IAAI,UAAU;AAG5B,MAAC,YAAY;AAAA;AAAA,EAEvB,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,mBAAmB;AAAA,EACpB;AAAA;AAAA,EAED,MAAM;AAAA,IACJ,QAAQ;AAAA,EACT;AACH;;;"}