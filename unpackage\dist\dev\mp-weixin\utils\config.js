"use strict";
const common_vendor = require("../common/vendor.js");
const config = {
  // 开发环境
  development: {
    baseURL: "http://localhost:8083",
    timeout: 15e3
  },
  // 生产环境
  production: {
    baseURL: "http://localhost:8083",
    timeout: 1e4
  }
};
const getEnvironment = () => {
  if (typeof window !== "undefined" && window.location) {
    const hostname = window.location.hostname;
    if (hostname === "localhost" || hostname === "127.0.0.1") {
      return "development";
    }
  }
  return "production";
};
const getCurrentConfig = () => {
  const env = getEnvironment();
  common_vendor.index.__f__("log", "at utils/config.js:31", "当前环境:", env);
  return config[env];
};
const config$1 = getCurrentConfig();
exports.config = config$1;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/config.js.map
