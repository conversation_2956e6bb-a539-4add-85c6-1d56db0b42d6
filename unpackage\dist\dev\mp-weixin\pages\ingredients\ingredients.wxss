
.container.data-v-9d78cbe1 {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 搜索栏 */
.search-section.data-v-9d78cbe1 {
	background-color: #fff;
	padding: 30rpx 40rpx;
}
.search-box.data-v-9d78cbe1 {
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
}
.search-icon.data-v-9d78cbe1 {
	font-size: 32rpx;
	color: #999;
	margin-right: 20rpx;
}
.search-input.data-v-9d78cbe1 {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

/* 分类标签 */
.category-section.data-v-9d78cbe1 {
	background-color: #fff;
	padding: 20rpx 0;
	margin-bottom: 20rpx;
}
.category-scroll.data-v-9d78cbe1 {
	white-space: nowrap;
}
.category-list.data-v-9d78cbe1 {
	display: flex;
	padding: 0 40rpx;
}
.category-item.data-v-9d78cbe1 {
	flex-shrink: 0;
	padding: 15rpx 30rpx;
	margin-right: 20rpx;
	background-color: #f5f5f5;
	border-radius: 40rpx;
}
.category-item.active.data-v-9d78cbe1 {
	background-color: #4CAF50;
}
.category-text.data-v-9d78cbe1 {
	font-size: 26rpx;
	color: #666;
}
.category-item.active .category-text.data-v-9d78cbe1 {
	color: #fff;
}

/* 药材食材列表 */
.ingredients-list.data-v-9d78cbe1 {
	padding: 0 20rpx;
}

/* 加载状态 */
.loading-container.data-v-9d78cbe1 {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 100rpx 0;
}
.loading-text.data-v-9d78cbe1 {
	color: #999;
	font-size: 28rpx;
}

/* 搜索无结果提示 */
.no-result-container.data-v-9d78cbe1 {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 100rpx 0;
}
.no-result-text.data-v-9d78cbe1 {
	color: #666;
	font-size: 32rpx;
	margin-bottom: 20rpx;
}
.no-result-tip.data-v-9d78cbe1 {
	color: #999;
	font-size: 26rpx;
}
.ingredient-card.data-v-9d78cbe1 {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.ingredient-image.data-v-9d78cbe1 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 15rpx;
	margin-right: 30rpx;
}
.ingredient-info.data-v-9d78cbe1 {
	flex: 1;
}
.ingredient-name.data-v-9d78cbe1 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}
.ingredient-alias.data-v-9d78cbe1 {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 8rpx;
}
.ingredient-effect.data-v-9d78cbe1 {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}
.ingredient-tags.data-v-9d78cbe1 {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
}
.tag.data-v-9d78cbe1 {
	background-color: #E8F5E8;
	color: #4CAF50;
	font-size: 20rpx;
	padding: 5rpx 15rpx;
	border-radius: 20rpx;
}
.ingredient-meta.data-v-9d78cbe1 {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
}
.ingredient-type.data-v-9d78cbe1,
.ingredient-nature.data-v-9d78cbe1 {
	font-size: 22rpx;
	color: #666;
	background-color: #f5f5f5;
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
}

/* 底部导航 */
.bottom-nav.data-v-9d78cbe1 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-around;
	border-top: 1rpx solid #eee;
	z-index: 999;
}
.nav-item.data-v-9d78cbe1 {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
}
.nav-item.active .nav-text.data-v-9d78cbe1 {
	color: #4CAF50;
}
.nav-item.add-btn.data-v-9d78cbe1 {
	position: relative;
}
.nav-icon.data-v-9d78cbe1 {
	font-size: 40rpx;
	margin-bottom: 8rpx;
}
.add-icon.data-v-9d78cbe1 {
	background-color: #4CAF50;
	color: #fff;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 50rpx;
	font-weight: bold;
}
.nav-text.data-v-9d78cbe1 {
	font-size: 20rpx;
	color: #666;
}
