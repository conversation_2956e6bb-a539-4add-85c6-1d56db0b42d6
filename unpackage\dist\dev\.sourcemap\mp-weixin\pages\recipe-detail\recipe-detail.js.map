{"version": 3, "file": "recipe-detail.js", "sources": ["pages/recipe-detail/recipe-detail.vue", "../产出物/安装包/HBuilderX/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcmVjaXBlLWRldGFpbC9yZWNpcGUtZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 加载状态 -->\n\t\t<view v-if=\"loading\" class=\"loading-container\">\n\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t</view>\n\t\t\n\t\t<!-- 食疗方案内容 -->\n\t\t<view v-else>\n\t\t\t<!-- 食疗方案图片 -->\n\t\t\t<view class=\"recipe-header\">\n\t\t\t\t<image class=\"recipe-image\" :src=\"recipeDetail.imageUrl\" mode=\"aspectFill\" @error=\"onImageError\" @load=\"onImageLoad\"></image>\n\t\t\t\t<view class=\"favorite-btn\" @click=\"toggleFavorite\">\n\t\t\t\t\t<text class=\"favorite-icon\" :class=\"recipeDetail.isFavorite ? 'favorited' : ''\">♥</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\n\t\t\t<!-- 食疗方案信息 -->\n\t\t\t<view class=\"recipe-info\">\n\t\t\t\t<text class=\"recipe-title\">{{ recipeDetail.title }}</text>\n\t\t\t\t<text class=\"recipe-suitable\">适用人群：{{ recipeDetail.suitableFor }}</text>\n\t\t\t\t\n\t\t\t\t<!-- 医生信息和评分 -->\n\t\t\t\t<view class=\"doctor-info\" v-if=\"recipeDetail.doctor && recipeDetail.doctor.name\">\n\t\t\t\t\t<view class=\"doctor-avatar\">\n\t\t\t\t\t\t<image :src=\"recipeDetail.doctor.avatar || '/static/logo.png'\" mode=\"aspectFill\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"doctor-details\">\n\t\t\t\t\t\t<text class=\"doctor-name\">{{ recipeDetail.doctor.name }}</text>\n\t\t\t\t\t\t<text class=\"doctor-title\">{{ recipeDetail.doctor.title || '中医师' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"rating-info\">\n\t\t\t\t\t\t<view class=\"rating\">\n\t\t\t\t\t\t\t<text class=\"star\">★</text>\n\t\t\t\t\t\t\t<text class=\"rating-score\">{{ recipeDetail.rating || 0 }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"view-count\">\n\t\t\t\t\t\t\t<text class=\"view-icon\">👁</text>\n\t\t\t\t\t\t\t<text class=\"view-number\">{{ recipeDetail.viewCount || 0 }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 详细描述 -->\n\t\t\t<view class=\"recipe-content\">\n\t\t\t\t<view class=\"section\">\n\t\t\t\t\t<text class=\"section-title\">方案介绍</text>\n\t\t\t\t\t<text class=\"section-content\">{{ recipeDetail.description }}</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"section\">\n\t\t\t\t\t<text class=\"section-title\">主要功效</text>\n\t\t\t\t\t<text class=\"section-content\">{{ recipeDetail.effects }}</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"section\">\n\t\t\t\t\t<text class=\"section-title\">制作方法</text>\n\t\t\t\t\t<text class=\"section-content\">{{ recipeDetail.method }}</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"section\">\n\t\t\t\t\t<text class=\"section-title\">注意事项</text>\n\t\t\t\t\t<text class=\"section-content\">{{ recipeDetail.precautions }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 底部操作按钮 -->\n\t\t\t<view class=\"bottom-actions\">\n\t\t\t\t<button class=\"action-btn secondary\" @click=\"shareRecipe\">分享方案</button>\n\t\t\t\t<button class=\"action-btn primary\" @click=\"startCooking\">开始制作</button>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { getRecipeDetail, toggleFavorite } from '../../api/recipe.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\trecipeId: '',\n\t\t\trecipeDetail: {\n\t\t\t\tid: null,\n\t\t\t\ttitle: '',\n\t\t\t\tsuitableFor: '',\n\t\t\t\tdescription: '',\n\t\t\t\teffects: '',\n\t\t\t\tmethod: '',\n\t\t\t\tprecautions: '',\n\t\t\t\timageUrl: '/static/logo.png',\n\t\t\t\tdoctor: {\n\t\t\t\t\tname: '',\n\t\t\t\t\ttitle: '',\n\t\t\t\t\tavatar: '/static/logo.png'\n\t\t\t\t},\n\t\t\t\trating: 0,\n\t\t\t\tviewCount: 0,\n\t\t\t\tisFavorite: false\n\t\t\t},\n\t\t\tloading: false,\n\t\t\tuserId: 2 // 临时用户ID，实际应该从登录状态获取\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.id) {\n\t\t\tthis.recipeId = options.id;\n\t\t\tthis.loadRecipeDetail();\n\t\t}\n\t},\n\tmethods: {\n\t\t/**\n\t\t * 加载食疗方案详情\n\t\t */\n\t\tasync loadRecipeDetail() {\n\t\t\tif (this.loading) return;\n\t\t\t\n\t\t\tthis.loading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst response = await getRecipeDetail(this.recipeId);\n\t\t\t\t\n\t\t\t\tif (response.code === 200 && response.data) {\n\t\t\t\t\tconsole.log('获取到的详情数据:', response.data);\n\t\t\t\t\tconsole.log('图片URL:', response.data.imageUrl);\n\t\t\t\t\t\n\t\t\t\t\t// 更新详情数据\n\t\t\t\t\tthis.recipeDetail = {\n\t\t\t\t\t\tid: response.data.id,\n\t\t\t\t\t\ttitle: response.data.title || '',\n\t\t\t\t\t\tsuitableFor: response.data.suitableFor || '',\n\t\t\t\t\t\tdescription: response.data.description || '',\n\t\t\t\t\t\teffects: response.data.efficacy || '', // 使用功效字段\n\t\t\t\t\t\tmethod: response.data.steps || '', // 使用制作步骤字段\n\t\t\t\t\t\tprecautions: '请根据个人体质和医生建议使用', // 注意事项\n\t\t\t\t\t\timageUrl: response.data.imageUrl || '/static/logo.png',\n\t\t\t\t\t\tdoctor: response.data.doctor || {\n\t\t\t\t\t\t\tname: '',\n\t\t\t\t\t\t\ttitle: '',\n\t\t\t\t\t\t\tavatar: '/static/logo.png'\n\t\t\t\t\t\t},\n\t\t\t\t\t\trating: response.data.rating || 0,\n\t\t\t\t\t\tviewCount: response.data.viewCount || 0,\n\t\t\t\t\t\tisFavorite: response.data.isFavorite || false\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 测试图片URL是否可访问\n\t\t\t\t\tif (this.recipeDetail.imageUrl && this.recipeDetail.imageUrl !== '/static/logo.png') {\n\t\t\t\t\t\tthis.testImageUrl(this.recipeDetail.imageUrl);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('设置后的详情数据:', this.recipeDetail);\n\t\t\t\t\tconsole.log('最终图片URL:', this.recipeDetail.imageUrl);\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.message || '获取详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取食疗方案详情失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误，请稍后重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t/**\n\t\t * 图片加载错误处理\n\t\t */\n\t\tonImageError(e) {\n\t\t\tconsole.error('图片加载失败:', e);\n\t\t\tconsole.log('当前图片URL:', this.recipeDetail.imageUrl);\n\t\t\t// 设置默认图片\n\t\t\tthis.recipeDetail.imageUrl = '/static/logo.png';\n\t\t},\n\t\t\n\t\t/**\n\t\t * 图片加载成功处理\n\t\t */\n\t\tonImageLoad(e) {\n\t\t\tconsole.log('图片加载成功:', e);\n\t\t\tconsole.log('图片URL:', this.recipeDetail.imageUrl);\n\t\t},\n\t\t\n\t\t/**\n\t\t * 测试图片URL是否可访问\n\t\t */\n\t\ttestImageUrl(url) {\n\t\t\tconsole.log('测试图片URL:', url);\n\t\t\tuni.request({\n\t\t\t\turl: url,\n\t\t\t\tmethod: 'HEAD',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('图片URL测试成功:', res);\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('图片URL测试失败:', err);\n\t\t\t\t\t// 如果图片URL不可访问，使用默认图片\n\t\t\t\t\tthis.recipeDetail.imageUrl = '/static/logo.png';\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t/**\n\t\t * 切换收藏状态\n\t\t */\n\t\tasync toggleFavorite() {\n\t\t\ttry {\n\t\t\t\tconst response = await toggleFavorite({\n\t\t\t\t\tplanId: this.recipeDetail.id,\n\t\t\t\t\tuserId: this.userId\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (response.code === 200) {\n\t\t\t\t\t// 更新本地状态\n\t\t\t\t\tthis.recipeDetail.isFavorite = response.data.isFavorite;\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.data.isFavorite ? '已收藏' : '已取消收藏',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.message || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('收藏操作失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误，请稍后重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 分享方案\n\t\tshareRecipe() {\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: ['分享到微信', '分享到朋友圈', '复制链接'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '分享成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 开始制作\n\t\tstartCooking() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '开始制作指导',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.container {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n\tpadding-bottom: 120rpx;\n}\n\n/* 食疗方案头部图片 */\n.recipe-header {\n\tposition: relative;\n\theight: 500rpx;\n}\n\n.recipe-image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.favorite-btn {\n\tposition: absolute;\n\ttop: 30rpx;\n\tright: 30rpx;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tbackground-color: rgba(255, 255, 255, 0.9);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.favorite-icon {\n\tfont-size: 40rpx;\n\tcolor: #ccc;\n}\n\n.favorite-icon.favorited {\n\tcolor: #4CAF50;\n}\n\n/* 食疗方案信息 */\n.recipe-info {\n\tbackground-color: #fff;\n\tpadding: 40rpx;\n}\n\n.recipe-title {\n\tfont-size: 40rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 20rpx;\n}\n\n.recipe-suitable {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tdisplay: block;\n\tmargin-bottom: 30rpx;\n}\n\n.doctor-info {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n}\n\n.doctor-avatar {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\toverflow: hidden;\n}\n\n.doctor-avatar image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.doctor-details {\n\tflex: 1;\n}\n\n.doctor-name {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n\tdisplay: block;\n\tmargin-bottom: 8rpx;\n}\n\n.doctor-title {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.rating-info {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: flex-end;\n\tgap: 10rpx;\n}\n\n.rating {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.star {\n\tcolor: #FFD700;\n\tfont-size: 28rpx;\n\tmargin-right: 8rpx;\n}\n\n.rating-score {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n}\n\n.view-count {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.view-icon {\n\tfont-size: 24rpx;\n\tmargin-right: 8rpx;\n}\n\n.view-number {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n/* 详细内容 */\n.recipe-content {\n\tmargin-top: 20rpx;\n}\n\n.section {\n\tbackground-color: #fff;\n\tpadding: 40rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 20rpx;\n}\n\n.section-content {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.8;\n\twhite-space: pre-line;\n}\n\n/* 底部操作按钮 */\n.bottom-actions {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground-color: #fff;\n\tpadding: 30rpx 40rpx;\n\tdisplay: flex;\n\tgap: 20rpx;\n\tborder-top: 1rpx solid #eee;\n}\n\n.action-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tfont-size: 28rpx;\n\tborder: none;\n}\n\n.action-btn.primary {\n\tbackground-color: #4CAF50;\n\tcolor: #fff;\n}\n\n.action-btn.secondary {\n\tbackground-color: #f5f5f5;\n\tcolor: #666;\n}\n\n/* 加载状态样式 */\n.loading-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\theight: 100vh;\n}\n\n.loading-text {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n</style>\n", "import MiniProgramPage from 'D:/桌面/Medicine-uniapp/pages/recipe-detail/recipe-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getRecipeDetail", "uni", "toggleFavorite"], "mappings": ";;;AA+EA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,MACV,cAAc;AAAA,QACb,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,UAAU;AAAA,QACV,QAAQ;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,QACR;AAAA,QACD,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,YAAY;AAAA,MACZ;AAAA,MACD,SAAS;AAAA,MACT,QAAQ;AAAA;AAAA,IACT;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,QAAI,QAAQ,IAAI;AACf,WAAK,WAAW,QAAQ;AACxB,WAAK,iBAAgB;AAAA,IACtB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,MAAM,mBAAmB;AACxB,UAAI,KAAK;AAAS;AAElB,WAAK,UAAU;AAEf,UAAI;AACH,cAAM,WAAW,MAAMA,WAAAA,gBAAgB,KAAK,QAAQ;AAEpD,YAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAC3CC,2FAAY,aAAa,SAAS,IAAI;AACtCA,8BAAA,MAAA,OAAA,gDAAY,UAAU,SAAS,KAAK,QAAQ;AAG5C,eAAK,eAAe;AAAA,YACnB,IAAI,SAAS,KAAK;AAAA,YAClB,OAAO,SAAS,KAAK,SAAS;AAAA,YAC9B,aAAa,SAAS,KAAK,eAAe;AAAA,YAC1C,aAAa,SAAS,KAAK,eAAe;AAAA,YAC1C,SAAS,SAAS,KAAK,YAAY;AAAA;AAAA,YACnC,QAAQ,SAAS,KAAK,SAAS;AAAA;AAAA,YAC/B,aAAa;AAAA;AAAA,YACb,UAAU,SAAS,KAAK,YAAY;AAAA,YACpC,QAAQ,SAAS,KAAK,UAAU;AAAA,cAC/B,MAAM;AAAA,cACN,OAAO;AAAA,cACP,QAAQ;AAAA,YACR;AAAA,YACD,QAAQ,SAAS,KAAK,UAAU;AAAA,YAChC,WAAW,SAAS,KAAK,aAAa;AAAA,YACtC,YAAY,SAAS,KAAK,cAAc;AAAA;AAIzC,cAAI,KAAK,aAAa,YAAY,KAAK,aAAa,aAAa,oBAAoB;AACpF,iBAAK,aAAa,KAAK,aAAa,QAAQ;AAAA,UAC7C;AAEAA,wBAAA,MAAA,MAAA,OAAA,gDAAY,aAAa,KAAK,YAAY;AAC1CA,2FAAY,YAAY,KAAK,aAAa,QAAQ;AAAA,eAC5C;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,WAAW;AAAA,YAC3B,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,gDAAA,eAAe,KAAK;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACT,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,aAAa,GAAG;AACfA,oBAAA,MAAA,MAAA,SAAA,gDAAc,WAAW,CAAC;AAC1BA,0BAAA,MAAA,OAAA,gDAAY,YAAY,KAAK,aAAa,QAAQ;AAElD,WAAK,aAAa,WAAW;AAAA,IAC7B;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY,GAAG;AACdA,oBAAA,MAAA,MAAA,OAAA,gDAAY,WAAW,CAAC;AACxBA,0BAAA,MAAA,OAAA,gDAAY,UAAU,KAAK,aAAa,QAAQ;AAAA,IAChD;AAAA;AAAA;AAAA;AAAA,IAKD,aAAa,KAAK;AACjBA,oBAAA,MAAA,MAAA,OAAA,gDAAY,YAAY,GAAG;AAC3BA,oBAAAA,MAAI,QAAQ;AAAA,QACX;AAAA,QACA,QAAQ;AAAA,QACR,SAAS,CAAC,QAAQ;AACjBA,2FAAY,cAAc,GAAG;AAAA,QAC7B;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,6FAAc,cAAc,GAAG;AAE/B,eAAK,aAAa,WAAW;AAAA,QAC9B;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,iBAAiB;AACtB,UAAI;AACH,cAAM,WAAW,MAAMC,0BAAe;AAAA,UACrC,QAAQ,KAAK,aAAa;AAAA,UAC1B,QAAQ,KAAK;AAAA,QACd,CAAC;AAED,YAAI,SAAS,SAAS,KAAK;AAE1B,eAAK,aAAa,aAAa,SAAS,KAAK;AAE7CD,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,aAAa,QAAQ;AAAA,YAC1C,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,eACK;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,WAAW;AAAA,YAC3B,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gDAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,gBAAgB;AAAA,QACnB,UAAU,CAAC,SAAS,UAAU,MAAM;AAAA,QACpC,SAAS,CAAC,QAAQ;AACjBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtQA,GAAG,WAAW,eAAe;"}