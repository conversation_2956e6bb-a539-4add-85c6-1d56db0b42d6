"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      isLoggedIn: false,
      userInfo: {
        name: "养生达人",
        description: "关注健康，享受生活",
        avatar: "/static/logo.png",
        favoriteCount: 28,
        viewCount: 156
      },
      recentFavorites: [
        {
          id: 1,
          title: "补气养生汤",
          type: "食疗方案",
          image: "/static/logo.png"
        },
        {
          id: 2,
          title: "黄芪",
          type: "中药材",
          image: "/static/logo.png"
        },
        {
          id: 3,
          title: "清热解暑粥",
          type: "食疗方案",
          image: "/static/logo.png"
        }
      ]
    };
  },
  onLoad() {
    this.checkLoginStatus();
  },
  onShow() {
    this.checkLoginStatus();
  },
  methods: {
    // 检查登录状态
    checkLoginStatus() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      const token = common_vendor.index.getStorageSync("token");
      if (userInfo && token) {
        this.isLoggedIn = true;
        this.userInfo = {
          ...this.userInfo,
          ...userInfo
        };
      } else {
        this.isLoggedIn = false;
      }
    },
    // 处理头部点击事件
    handleHeaderClick() {
      if (!this.isLoggedIn) {
        this.goToLogin();
      }
    },
    // 跳转到登录页面
    goToLogin() {
      common_vendor.index.navigateTo({
        url: "/pages/login/login"
      });
    },
    // 跳转到收藏页面
    goToFavorites() {
      common_vendor.index.showToast({
        title: "跳转到收藏页面",
        icon: "none"
      });
    },
    // 跳转到历史记录
    goToHistory() {
      common_vendor.index.showToast({
        title: "跳转到浏览历史",
        icon: "none"
      });
    },
    // 跳转到笔记
    goToNotes() {
      common_vendor.index.showToast({
        title: "跳转到我的笔记",
        icon: "none"
      });
    },
    // 跳转到设置
    goToSettings() {
      common_vendor.index.showToast({
        title: "跳转到设置页面",
        icon: "none"
      });
    },
    // 跳转到帮助
    goToHelp() {
      common_vendor.index.showToast({
        title: "跳转到帮助页面",
        icon: "none"
      });
    },
    // 跳转到关于
    goToAbout() {
      common_vendor.index.showToast({
        title: "跳转到关于页面",
        icon: "none"
      });
    },
    // 退出登录
    logout() {
      common_vendor.index.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        confirmText: "退出",
        cancelText: "取消",
        confirmColor: "#ff4757",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.removeStorageSync("userInfo");
            common_vendor.index.removeStorageSync("token");
            this.isLoggedIn = false;
            common_vendor.index.showToast({
              title: "已退出登录",
              icon: "success"
            });
          }
        }
      });
    },
    // 跳转到详情
    goToDetail(item) {
      if (item.type === "食疗方案") {
        common_vendor.index.navigateTo({
          url: `/pages/recipe-detail/recipe-detail?id=${item.id}`
        });
      } else if (item.type === "中药材") {
        common_vendor.index.navigateTo({
          url: `/pages/ingredient-detail/ingredient-detail?id=${item.id}`
        });
      }
    },
    // 导航跳转
    goToPage(page) {
      switch (page) {
        case "home":
          common_vendor.index.navigateTo({
            url: "/pages/index/index"
          });
          break;
        case "ingredients":
          common_vendor.index.navigateTo({
            url: "/pages/ingredients/ingredients"
          });
          break;
        case "recipe":
          common_vendor.index.navigateTo({
            url: "/pages/recipe/recipe"
          });
          break;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isLoggedIn
  }, $data.isLoggedIn ? {
    b: $data.userInfo.avatar
  } : {}, {
    c: common_vendor.t($data.isLoggedIn ? $data.userInfo.name : "立即登录"),
    d: common_vendor.t($data.isLoggedIn ? $data.userInfo.description : "登录后享受更多功能"),
    e: $data.isLoggedIn
  }, $data.isLoggedIn ? {
    f: common_vendor.t($data.userInfo.favoriteCount),
    g: common_vendor.t($data.userInfo.viewCount)
  } : {}, {
    h: common_vendor.o((...args) => $options.handleHeaderClick && $options.handleHeaderClick(...args)),
    i: common_vendor.o((...args) => $options.goToFavorites && $options.goToFavorites(...args)),
    j: common_vendor.o((...args) => $options.goToHistory && $options.goToHistory(...args)),
    k: common_vendor.o((...args) => $options.goToNotes && $options.goToNotes(...args)),
    l: common_vendor.o((...args) => $options.goToSettings && $options.goToSettings(...args)),
    m: common_vendor.o((...args) => $options.goToHelp && $options.goToHelp(...args)),
    n: common_vendor.o((...args) => $options.goToAbout && $options.goToAbout(...args)),
    o: $data.isLoggedIn
  }, $data.isLoggedIn ? {
    p: common_vendor.o((...args) => $options.logout && $options.logout(...args))
  } : {}, {
    q: common_vendor.o((...args) => $options.goToFavorites && $options.goToFavorites(...args)),
    r: common_vendor.f($data.recentFavorites, (item, k0, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.type),
        d: item.id,
        e: common_vendor.o(($event) => $options.goToDetail(item), item.id)
      };
    }),
    s: common_vendor.o(($event) => $options.goToPage("home")),
    t: common_vendor.o(($event) => $options.goToPage("ingredients")),
    v: common_vendor.o(($event) => $options.goToPage("recipe")),
    w: common_vendor.o(($event) => $options.goToPage("profile"))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-dd383ca2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/profile.js.map
