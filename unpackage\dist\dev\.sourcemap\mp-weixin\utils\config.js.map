{"version": 3, "file": "config.js", "sources": ["utils/config.js"], "sourcesContent": ["// 环境配置文件\r\nconst config = {\r\n\t// 开发环境\r\n\tdevelopment: {\r\n\t\tbaseURL: 'http://localhost:8083',\r\n\t\ttimeout: 15000\r\n\t},\r\n\t// 生产环境\r\n\tproduction: {\r\n\t\tbaseURL: 'http://localhost:8083',\r\n\t\ttimeout: 10000\r\n\t}\r\n};\r\n\r\n// 获取当前环境\r\nconst getEnvironment = () => {\r\n\t// 在uni-app中，可以通过process.env.NODE_ENV判断环境\r\n\t// 但在某些情况下可能不可用，所以我们用其他方式判断\r\n\tif (typeof window !== 'undefined' && window.location) {\r\n\t\tconst hostname = window.location.hostname;\r\n\t\tif (hostname === 'localhost' || hostname === '127.0.0.1') {\r\n\t\t\treturn 'development';\r\n\t\t}\r\n\t}\r\n\treturn 'production';\r\n};\r\n\r\n// 获取当前配置\r\nconst getCurrentConfig = () => {\r\n\tconst env = getEnvironment();\r\n\tconsole.log('当前环境:', env);\r\n\treturn config[env];\r\n};\r\n\r\nexport default getCurrentConfig();\r\n"], "names": ["uni"], "mappings": ";;AACA,MAAM,SAAS;AAAA;AAAA,EAEd,aAAa;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,EACV;AAAA;AAAA,EAEA,YAAY;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,EACV;AACD;AAGA,MAAM,iBAAiB,MAAM;AAG5B,MAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AAC/C,UAAA,WAAW,OAAO,SAAS;AAC7B,QAAA,aAAa,eAAe,aAAa,aAAa;AAClD,aAAA;AAAA,IACR;AAAA,EACD;AACO,SAAA;AACR;AAGA,MAAM,mBAAmB,MAAM;AAC9B,QAAM,MAAM;AACZA,gBAAA,MAAY,MAAA,OAAA,yBAAA,SAAS,GAAG;AACxB,SAAO,OAAO,GAAG;AAClB;AAEA,MAAA,WAAe,iBAAiB;;"}