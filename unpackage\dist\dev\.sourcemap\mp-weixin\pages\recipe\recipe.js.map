{"version": 3, "file": "recipe.js", "sources": ["pages/recipe/recipe.vue", "../产出物/安装包/HBuilderX/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcmVjaXBlL3JlY2lwZS52dWU"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 页面标题 -->\n\t\t<view class=\"header\">\n\t\t\t<text class=\"header-title\">食疗方案</text>\n\t\t\t<text class=\"header-subtitle\">更多方案 ></text>\n\t\t</view>\n\n\t\t<!-- 搜索栏 -->\n\t\t<view class=\"search-section\">\n\t\t\t<view class=\"search-box\">\n\t\t\t\t<text class=\"search-icon\">🔍</text>\n\t\t\t\t<input class=\"search-input\" placeholder=\"搜索食疗方案\" v-model=\"searchKeyword\" @input=\"onSearch\" />\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 分类筛选 -->\n\t\t<view class=\"filter-section\">\n\t\t\t<scroll-view class=\"filter-scroll\" scroll-x=\"true\">\n\t\t\t\t<view class=\"filter-list\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"filter-item\"\n\t\t\t\t\t\t:class=\"activeFilter === item.id ? 'active' : ''\"\n\t\t\t\t\t\tv-for=\"item in filterList\"\n\t\t\t\t\t\t:key=\"item.id\"\n\t\t\t\t\t\t@click=\"selectFilter(item.id)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text class=\"filter-text\">{{ item.name }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t\t\n\t\t<!-- 食疗方案列表 -->\n\t\t<view class=\"recipe-list\">\n\t\t\t<!-- 加载状态 -->\n\t\t\t<view v-if=\"loading && recipeList.length === 0\" class=\"loading-container\">\n\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 空状态 -->\n\t\t\t<view v-else-if=\"!loading && recipeList.length === 0\" class=\"empty-container\">\n\t\t\t\t<text class=\"empty-text\">暂无食疗方案</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 方案列表 -->\n\t\t\t<view v-else>\n\t\t\t\t<view class=\"recipe-card fade-in card-hover\" v-for=\"(item, index) in filteredRecipes\" :key=\"item.id\" @click=\"goToDetail(item)\" :style=\"{ 'animation-delay': (index * 0.1) + 's' }\">\n\t\t\t\t\t<!-- 食疗方案图片 -->\n\t\t\t\t\t<image class=\"recipe-image\" :src=\"item.imageUrl || '/static/logo.png'\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 收藏按钮 -->\n\t\t\t\t\t<view class=\"favorite-btn btn-hover scale-in\" @click.stop=\"toggleFavorite(item, index)\">\n\t\t\t\t\t\t<text class=\"favorite-icon\" :class=\"item.isFavorite ? 'favorited' : ''\">♥</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 食疗方案信息 -->\n\t\t\t\t\t<view class=\"recipe-info\">\n\t\t\t\t\t\t<text class=\"recipe-title\">{{ item.title }}</text>\n\t\t\t\t\t\t<text class=\"recipe-suitable\">适用人群：{{ item.suitableFor }}</text>\n\t\t\t\t\t\t<text class=\"recipe-description\">{{ item.description }}</text>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 医生信息 -->\n\t\t\t\t\t\t<view class=\"doctor-section\" v-if=\"item.doctor\">\n\t\t\t\t\t\t\t<view class=\"doctor-avatar\">\n\t\t\t\t\t\t\t\t<image :src=\"item.doctor.avatar || '/static/logo.png'\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"doctor-name\">{{ item.doctor.name }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 方案统计信息 -->\n\t\t\t\t\t\t<view class=\"stats-info\">\n\t\t\t\t\t\t\t<view class=\"rating\">\n\t\t\t\t\t\t\t\t<text class=\"star\">★</text>\n\t\t\t\t\t\t\t\t<text class=\"rating-score\">{{ item.rating || 4.5 }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"view-count\">\n\t\t\t\t\t\t\t\t<text class=\"view-icon\">👁</text>\n\t\t\t\t\t\t\t\t<text class=\"view-number\">{{ item.viewCount || 0 }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"like-count\" @click.stop=\"toggleRecipeLike(item, index)\">\n\t\t\t\t\t\t\t\t<text class=\"like-icon\" :class=\"item.isLiked ? 'liked' : ''\">❤️</text>\n\t\t\t\t\t\t\t\t<text class=\"like-number\">{{ item.likeCount || 0 }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 加载更多 -->\n\t\t\t\t<view v-if=\"loading && recipeList.length > 0\" class=\"loading-more\">\n\t\t\t\t\t<text class=\"loading-text\">加载更多...</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 没有更多数据 -->\n\t\t\t\t<view v-if=\"!hasMore && recipeList.length > 0\" class=\"no-more\">\n\t\t\t\t\t<text class=\"no-more-text\">没有更多数据了</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 底部导航 -->\n\t\t<view class=\"bottom-nav\">\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('home')\">\n\t\t\t\t<text class=\"nav-icon\">🏠</text>\n\t\t\t\t<text class=\"nav-text\">首页</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('ingredients')\">\n\t\t\t\t<text class=\"nav-icon\">🌿</text>\n\t\t\t\t<text class=\"nav-text\">药材食材</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-item add-btn\">\n\t\t\t\t<text class=\"nav-icon add-icon\">+</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-item active\" @click=\"goToPage('recipe')\">\n\t\t\t\t<text class=\"nav-icon\">📋</text>\n\t\t\t\t<text class=\"nav-text\">食疗方案</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('profile')\">\n\t\t\t\t<text class=\"nav-icon\">👤</text>\n\t\t\t\t<text class=\"nav-text\">我的</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { getRecipeList, toggleFavorite, incrementViewCount, recordSearchLog, toggleLike, getBatchLikeStatus } from '../../api/recipe.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tsearchKeyword: '',\n\t\t\tactiveFilter: 'all',\n\t\t\tfilterList: [\n\t\t\t\t{ id: 'all', name: '全部' },\n\t\t\t\t{ id: 'tonify', name: '补益类' },\n\t\t\t\t{ id: 'clear', name: '清热类' },\n\t\t\t\t{ id: 'regulate', name: '调理类' },\n\t\t\t\t{ id: 'beauty', name: '美容类' },\n\t\t\t\t{ id: 'weight', name: '减肥类' }\n\t\t\t],\n\t\t\trecipeList: [], // 改为空数组，从后端获取数据\n\t\t\tloading: false, // 加载状态\n\t\t\tpage: 1, // 当前页码\n\t\t\tsize: 10, // 每页大小\n\t\t\thasMore: true, // 是否还有更多数据\n\t\t\tuserId: 2 // 临时用户ID，实际应该从登录状态获取\n\t\t}\n\t},\n\tcomputed: {\n\t\tfilteredRecipes() {\n\t\t\t// 由于现在从后端获取数据，筛选逻辑移到后端处理\n\t\t\treturn this.recipeList;\n\t\t}\n\t},\n\t/**\n\t * 页面加载时获取数据\n\t */\n\tasync onLoad() {\n\t\tconsole.log('页面onLoad - 开始加载数据');\n\t\tawait this.loadRecipeList();\n\t},\n\t\n\t/**\n\t * 页面显示时刷新数据（如果需要）\n\t */\n\tasync onShow() {\n\t\tconsole.log('页面onShow - 页面显示');\n\t\t// 如果还没有数据，则加载数据\n\t\tif (this.recipeList.length === 0 && !this.loading) {\n\t\t\tconsole.log('页面onShow - 数据为空，重新加载');\n\t\t\tawait this.loadRecipeList();\n\t\t}\n\t},\n\tmethods: {\n\t\t\n\t\t/**\n\t\t * 加载食疗方案列表\n\t\t */\n\t\tasync loadRecipeList(isRefresh = true) {\n\t\t\tif (this.loading) return;\n\t\t\t\n\t\t\tconsole.log('loadRecipeList - 开始加载', { isRefresh, activeFilter: this.activeFilter, searchKeyword: this.searchKeyword });\n\t\t\t\n\t\t\tthis.loading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 如果是刷新，重置页码\n\t\t\t\tif (isRefresh) {\n\t\t\t\t\tthis.page = 1;\n\t\t\t\t\tthis.recipeList = [];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst params = {\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tsize: this.size,\n\t\t\t\t\tuserId: this.userId\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 添加搜索关键词\n\t\t\t\tif (this.searchKeyword) {\n\t\t\t\t\tparams.keyword = this.searchKeyword;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 添加分类筛选\n\t\t\t\tif (this.activeFilter !== 'all') {\n\t\t\t\t\tparams.category = this.activeFilter;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log('loadRecipeList - 请求参数', params);\n\t\t\t\t\n\t\t\t\tconst response = await getRecipeList(params);\n\t\t\t\t\n\t\t\t\tconsole.log('loadRecipeList - 响应结果', response);\n\t\t\t\t\n\t\t\t\tif (response.code === 200 && response.data) {\n\t\t\t\t\tconst newData = response.data.records || [];\n\t\t\t\t\t\n\t\t\t\t\tif (isRefresh) {\n\t\t\t\t\t\tthis.recipeList = newData;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.recipeList = [...this.recipeList, ...newData];\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 判断是否还有更多数据\n\t\t\t\t\tthis.hasMore = newData.length === this.size;\n\t\t\t\t\t\n\t\t\t\t\t// 批量获取点赞状态\n\t\t\t\t\tawait this.loadLikeStatus();\n\t\t\t\t\t\n\t\t\t\t\t// 记录搜索日志\n\t\t\t\t\tif (this.searchKeyword) {\n\t\t\t\t\t\tthis.recordSearchLog();\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.message || '获取数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取食疗方案列表失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误，请稍后重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t/**\n\t\t * 搜索功能\n\t\t */\n\t\tasync onSearch() {\n\t\t\t// 重置页码并重新加载数据\n\t\t\tthis.page = 1;\n\t\t\tawait this.loadRecipeList(true);\n\t\t},\n\n\t\t/**\n\t\t * 选择筛选条件\n\t\t */\n\t\tasync selectFilter(filterId) {\n\t\t\tthis.activeFilter = filterId;\n\t\t\t// 重置页码并重新加载数据\n\t\t\tthis.page = 1;\n\t\t\tawait this.loadRecipeList(true);\n\t\t},\n\t\t\n\t\t/**\n\t\t * 切换收藏状态\n\t\t */\n\t\tasync toggleFavorite(item, index) {\n\t\t\ttry {\n\t\t\t\tconst response = await toggleFavorite({\n\t\t\t\t\tplanId: item.id,\n\t\t\t\t\tuserId: this.userId\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (response.code === 200) {\n\t\t\t\t\t// 更新本地状态\n\t\t\t\t\tthis.recipeList[index].isFavorite = response.data.isFavorite;\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.data.isFavorite ? '已收藏' : '已取消收藏',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.message || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('收藏操作失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误，请稍后重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t/**\n\t\t * 跳转到详情页\n\t\t */\n\t\tasync goToDetail(item) {\n\t\t\ttry {\n\t\t\t\t// 增加浏览量（每个用户只能贡献1次）\n\t\t\t\tconst response = await incrementViewCount(item.id, this.userId);\n\t\t\t\t\n\t\t\t\tif (response && response.code === 200) {\n\t\t\t\t\tconsole.log('浏览量统计结果:', response.data);\n\t\t\t\t\tif (response.data.isFirstView) {\n\t\t\t\t\t\tconsole.log('首次浏览，浏览量+1');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('已浏览过，不重复计算');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('增加浏览量失败:', error);\n\t\t\t}\n\t\t\t\n\t\t\t// 跳转到详情页\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/recipe-detail/recipe-detail?id=${item.id}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t/**\n\t\t * 记录搜索日志\n\t\t */\n\t\tasync recordSearchLog() {\n\t\t\ttry {\n\t\t\t\tawait recordSearchLog({\n\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\tqueryText: this.searchKeyword,\n\t\t\t\t\tresultCount: this.recipeList.length\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('记录搜索日志失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t/**\n\t\t * 下拉刷新\n\t\t */\n\t\tasync onPullDownRefresh() {\n\t\t\tawait this.loadRecipeList(true);\n\t\t\tuni.stopPullDownRefresh();\n\t\t},\n\t\t\n\t\t/**\n\t\t * 上拉加载更多\n\t\t */\n\t\tasync onReachBottom() {\n\t\t\tif (this.hasMore && !this.loading) {\n\t\t\t\tthis.page++;\n\t\t\t\tawait this.loadRecipeList(false);\n\t\t\t}\n\t\t},\n\t\t\n\t\t/**\n\t\t * 点赞/取消点赞食疗方案\n\t\t */\n\t\tasync toggleRecipeLike(item, index) {\n\t\t\ttry {\n\t\t\t\t// 防止快速点击\n\t\t\t\tif (item.liking) return;\n\t\t\t\titem.liking = true;\n\t\t\t\t\n\t\t\t\tconst response = await toggleLike(item.id, this.userId);\n\t\t\t\t\n\t\t\t\tif (response.code === 200) {\n\t\t\t\t\t// 更新本地状态\n\t\t\t\t\tthis.recipeList[index].isLiked = response.data.isLiked;\n\t\t\t\t\tthis.recipeList[index].likeCount = response.data.likeCount;\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.data.message || (response.data.isLiked ? '点赞成功' : '取消点赞'),\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.message || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('点赞操作失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误，请稍后重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\titem.liking = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t/**\n\t\t * 批量获取点赞状态\n\t\t */\n\t\tasync loadLikeStatus() {\n\t\t\ttry {\n\t\t\t\tif (this.recipeList.length === 0) return;\n\t\t\t\t\n\t\t\t\tconst planIds = this.recipeList.map(item => item.id);\n\t\t\t\tconst response = await getBatchLikeStatus(this.userId, planIds);\n\t\t\t\t\n\t\t\t\tif (response.code === 200) {\n\t\t\t\t\t// 更新点赞状态\n\t\t\t\t\tthis.recipeList.forEach(item => {\n\t\t\t\t\t\tconst likeStatus = response.data[item.id.toString()];\n\t\t\t\t\t\tif (likeStatus !== undefined) {\n\t\t\t\t\t\t\titem.isLiked = likeStatus;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取点赞状态失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t/**\n\t\t * 导航跳转\n\t\t */\n\t\tgoToPage(page) {\n\t\t\tswitch(page) {\n\t\t\t\tcase 'home':\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'ingredients':\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/ingredients/ingredients'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'recipe':\n\t\t\t\t\t// 当前页面\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'profile':\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/profile/profile'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.container {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n\tpadding-bottom: 120rpx;\n}\n\n/* 页面头部 */\n.header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx 40rpx 20rpx;\n\tbackground-color: #fff;\n}\n\n.header-title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.header-subtitle {\n\tfont-size: 28rpx;\n\tcolor: #4CAF50;\n}\n\n/* 搜索栏 */\n.search-section {\n\tbackground-color: #fff;\n\tpadding: 30rpx 40rpx;\n}\n\n.search-box {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 50rpx;\n\tpadding: 20rpx 30rpx;\n}\n\n.search-icon {\n\tfont-size: 32rpx;\n\tcolor: #999;\n\tmargin-right: 20rpx;\n}\n\n.search-input {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n/* 筛选标签 */\n.filter-section {\n\tbackground-color: #fff;\n\tpadding: 20rpx 0;\n\tmargin-bottom: 20rpx;\n}\n\n.filter-scroll {\n\twhite-space: nowrap;\n}\n\n.filter-list {\n\tdisplay: flex;\n\tpadding: 0 40rpx;\n}\n\n.filter-item {\n\tflex-shrink: 0;\n\tpadding: 15rpx 30rpx;\n\tmargin-right: 20rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 40rpx;\n}\n\n.filter-item.active {\n\tbackground-color: #4CAF50;\n}\n\n.filter-text {\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.filter-item.active .filter-text {\n\tcolor: #fff;\n}\n\n/* 食疗方案列表 */\n.recipe-list {\n\tpadding: 20rpx;\n}\n\n.recipe-card {\n\tbackground-color: #fff;\n\tborder-radius: 20rpx;\n\tmargin-bottom: 30rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\tposition: relative;\n}\n\n.recipe-image {\n\twidth: 100%;\n\theight: 400rpx;\n}\n\n.favorite-btn {\n\tposition: absolute;\n\ttop: 20rpx;\n\tright: 20rpx;\n\twidth: 60rpx;\n\theight: 60rpx;\n\tbackground-color: rgba(255, 255, 255, 0.9);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.favorite-icon {\n\tfont-size: 32rpx;\n\tcolor: #ccc;\n}\n\n.favorite-icon.favorited {\n\tcolor: #4CAF50;\n}\n\n.recipe-info {\n\tpadding: 30rpx;\n}\n\n.recipe-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 15rpx;\n}\n\n.recipe-suitable {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tdisplay: block;\n\tmargin-bottom: 15rpx;\n}\n\n.recipe-description {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n\tdisplay: block;\n\tmargin-bottom: 25rpx;\n}\n\n/* 医生信息区域 */\n.doctor-section {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 15rpx;\n\tmargin-bottom: 15rpx;\n}\n\n.doctor-avatar {\n\twidth: 50rpx;\n\theight: 50rpx;\n\tborder-radius: 50%;\n\toverflow: hidden;\n}\n\n.doctor-avatar image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.doctor-name {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n/* 统计信息区域 */\n.stats-info {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n}\n\n.rating {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.star {\n\tcolor: #FFD700;\n\tfont-size: 24rpx;\n\tmargin-right: 5rpx;\n}\n\n.rating-score {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.view-count {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.view-icon {\n\tfont-size: 24rpx;\n\tmargin-right: 5rpx;\n}\n\n.view-number {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.like-count {\n\tdisplay: flex;\n\talign-items: center;\n\tcursor: pointer;\n}\n\n.like-icon {\n\tfont-size: 24rpx;\n\tmargin-right: 5rpx;\n\tcolor: #ccc;\n\ttransition: all 0.3s ease;\n}\n\n.like-icon.liked {\n\tcolor: #ff4757;\n\ttransform: scale(1.2);\n}\n\n.like-number {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n/* 底部导航 */\n.bottom-nav {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 120rpx;\n\tbackground-color: #fff;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-around;\n\tborder-top: 1rpx solid #eee;\n\tz-index: 999;\n}\n\n.nav-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tflex: 1;\n}\n\n.nav-item.active .nav-text {\n\tcolor: #4CAF50;\n}\n\n.nav-item.add-btn {\n\tposition: relative;\n}\n\n.nav-icon {\n\tfont-size: 40rpx;\n\tmargin-bottom: 8rpx;\n}\n\n.add-icon {\n\tbackground-color: #4CAF50;\n\tcolor: #fff;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 50rpx;\n\tfont-weight: bold;\n}\n\n.nav-text {\n\tfont-size: 20rpx;\n\tcolor: #666;\n}\n\n/* 加载状态样式 */\n.loading-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\theight: 400rpx;\n}\n\n.loading-text {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n/* 空状态样式 */\n.empty-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\theight: 400rpx;\n}\n\n.empty-text {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n/* 加载更多样式 */\n.loading-more {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 30rpx 0;\n}\n\n.no-more {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 30rpx 0;\n}\n\n.no-more-text {\n\tfont-size: 24rpx;\n\tcolor: #ccc;\n}\n</style>\n", "import MiniProgramPage from 'D:/桌面/Medicine-uniapp/pages/recipe/recipe.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getRecipeList", "toggleFavorite", "incrementViewCount", "recordSearchLog", "toggleLike", "getBatchLikeStatus"], "mappings": ";;;AAgIA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,eAAe;AAAA,MACf,cAAc;AAAA,MACd,YAAY;AAAA,QACX,EAAE,IAAI,OAAO,MAAM,KAAM;AAAA,QACzB,EAAE,IAAI,UAAU,MAAM,MAAO;AAAA,QAC7B,EAAE,IAAI,SAAS,MAAM,MAAO;AAAA,QAC5B,EAAE,IAAI,YAAY,MAAM,MAAO;AAAA,QAC/B,EAAE,IAAI,UAAU,MAAM,MAAO;AAAA,QAC7B,EAAE,IAAI,UAAU,MAAM,MAAM;AAAA,MAC5B;AAAA,MACD,YAAY,CAAE;AAAA;AAAA,MACd,SAAS;AAAA;AAAA,MACT,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,SAAS;AAAA;AAAA,MACT,QAAQ;AAAA;AAAA,IACT;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,kBAAkB;AAEjB,aAAO,KAAK;AAAA,IACb;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAID,MAAM,SAAS;AACdA,kBAAAA,qDAAY,mBAAmB;AAC/B,UAAM,KAAK;EACX;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,SAAS;AACdA,kBAAAA,qDAAY,iBAAiB;AAE7B,QAAI,KAAK,WAAW,WAAW,KAAK,CAAC,KAAK,SAAS;AAClDA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,sBAAsB;AAClC,YAAM,KAAK;IACZ;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAKR,MAAM,eAAe,YAAY,MAAM;AACtC,UAAI,KAAK;AAAS;AAElBA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,yBAAyB,EAAE,WAAW,cAAc,KAAK,cAAc,eAAe,KAAK,cAAe,CAAA;AAEtH,WAAK,UAAU;AAEf,UAAI;AAEH,YAAI,WAAW;AACd,eAAK,OAAO;AACZ,eAAK,aAAa;QACnB;AAEA,cAAM,SAAS;AAAA,UACd,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA;AAId,YAAI,KAAK,eAAe;AACvB,iBAAO,UAAU,KAAK;AAAA,QACvB;AAGA,YAAI,KAAK,iBAAiB,OAAO;AAChC,iBAAO,WAAW,KAAK;AAAA,QACxB;AAEAA,sBAAY,MAAA,MAAA,OAAA,kCAAA,yBAAyB,MAAM;AAE3C,cAAM,WAAW,MAAMC,yBAAc,MAAM;AAE3CD,sBAAY,MAAA,MAAA,OAAA,kCAAA,yBAAyB,QAAQ;AAE7C,YAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAC3C,gBAAM,UAAU,SAAS,KAAK,WAAW,CAAA;AAEzC,cAAI,WAAW;AACd,iBAAK,aAAa;AAAA,iBACZ;AACN,iBAAK,aAAa,CAAC,GAAG,KAAK,YAAY,GAAG,OAAO;AAAA,UAClD;AAGA,eAAK,UAAU,QAAQ,WAAW,KAAK;AAGvC,gBAAM,KAAK;AAGX,cAAI,KAAK,eAAe;AACvB,iBAAK,gBAAe;AAAA,UACrB;AAAA,eACM;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,WAAW;AAAA,YAC3B,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,kCAAA,eAAe,KAAK;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACT,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,WAAW;AAEhB,WAAK,OAAO;AACZ,YAAM,KAAK,eAAe,IAAI;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,aAAa,UAAU;AAC5B,WAAK,eAAe;AAEpB,WAAK,OAAO;AACZ,YAAM,KAAK,eAAe,IAAI;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,eAAe,MAAM,OAAO;AACjC,UAAI;AACH,cAAM,WAAW,MAAME,0BAAe;AAAA,UACrC,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,QACd,CAAC;AAED,YAAI,SAAS,SAAS,KAAK;AAE1B,eAAK,WAAW,KAAK,EAAE,aAAa,SAAS,KAAK;AAElDF,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,aAAa,QAAQ;AAAA,YAC1C,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,eACK;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,WAAW;AAAA,YAC3B,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,WAAW,MAAM;AACtB,UAAI;AAEH,cAAM,WAAW,MAAMG,8BAAmB,KAAK,IAAI,KAAK,MAAM;AAE9D,YAAI,YAAY,SAAS,SAAS,KAAK;AACtCH,wBAAY,MAAA,MAAA,OAAA,kCAAA,YAAY,SAAS,IAAI;AACrC,cAAI,SAAS,KAAK,aAAa;AAC9BA,0BAAAA,MAAA,MAAA,OAAA,kCAAY,YAAY;AAAA,iBAClB;AACNA,0BAAAA,MAAA,MAAA,OAAA,kCAAY,YAAY;AAAA,UACzB;AAAA,QACD;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,kCAAc,YAAY,KAAK;AAAA,MAChC;AAGAA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,yCAAyC,KAAK,EAAE;AAAA,MACtD,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,kBAAkB;AACvB,UAAI;AACH,cAAMI,2BAAgB;AAAA,UACrB,QAAQ,KAAK;AAAA,UACb,WAAW,KAAK;AAAA,UAChB,aAAa,KAAK,WAAW;AAAA,QAC9B,CAAC;AAAA,MACA,SAAO,OAAO;AACfJ,sBAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,oBAAoB;AACzB,YAAM,KAAK,eAAe,IAAI;AAC9BA,oBAAG,MAAC,oBAAmB;AAAA,IACvB;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,gBAAgB;AACrB,UAAI,KAAK,WAAW,CAAC,KAAK,SAAS;AAClC,aAAK;AACL,cAAM,KAAK,eAAe,KAAK;AAAA,MAChC;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,iBAAiB,MAAM,OAAO;AACnC,UAAI;AAEH,YAAI,KAAK;AAAQ;AACjB,aAAK,SAAS;AAEd,cAAM,WAAW,MAAMK,sBAAW,KAAK,IAAI,KAAK,MAAM;AAEtD,YAAI,SAAS,SAAS,KAAK;AAE1B,eAAK,WAAW,KAAK,EAAE,UAAU,SAAS,KAAK;AAC/C,eAAK,WAAW,KAAK,EAAE,YAAY,SAAS,KAAK;AAEjDL,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,YAAY,SAAS,KAAK,UAAU,SAAS;AAAA,YAClE,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,eACK;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,WAAW;AAAA,YAC3B,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACT,aAAK,SAAS;AAAA,MACf;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,iBAAiB;AACtB,UAAI;AACH,YAAI,KAAK,WAAW,WAAW;AAAG;AAElC,cAAM,UAAU,KAAK,WAAW,IAAI,UAAQ,KAAK,EAAE;AACnD,cAAM,WAAW,MAAMM,WAAkB,mBAAC,KAAK,QAAQ,OAAO;AAE9D,YAAI,SAAS,SAAS,KAAK;AAE1B,eAAK,WAAW,QAAQ,UAAQ;AAC/B,kBAAM,aAAa,SAAS,KAAK,KAAK,GAAG,SAAQ,CAAE;AACnD,gBAAI,eAAe,QAAW;AAC7B,mBAAK,UAAU;AAAA,YAChB;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfN,sBAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,SAAS,MAAM;AACd,cAAO,MAAI;AAAA,QACV,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,UACN,CAAC;AACD;AAAA,QACD,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,UACN,CAAC;AACD;AAAA,QACD,KAAK;AAEJ;AAAA,QACD,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,UACN,CAAC;AACD;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClcA,GAAG,WAAW,eAAe;"}