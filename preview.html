<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>药食同源 - 首页预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
            padding-bottom: 60px;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background-color: #f5f5f5;
            min-height: 100vh;
            position: relative;
        }
        
        /* 顶部导航栏 */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: #ffffff;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .leaf-icon {
            font-size: 20px;
            margin-right: 8px;
            color: #52c41a;
        }
        
        .app-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .search-icon, .notification-icon {
            font-size: 18px;
            color: #666;
            cursor: pointer;
        }
        
        .avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #52c41a;
        }
        
        /* 主横幅 */
        .hero-banner {
            position: relative;
            height: 150px;
            margin: 10px 15px;
            border-radius: 10px;
            overflow: hidden;
            background: linear-gradient(135deg, #52c41a 0%, #73d13d 50%, #95de64 100%);
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        }
        
        .banner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.3));
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 20px;
        }
        
        .banner-title {
            font-size: 21px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 8px;
        }
        
        .banner-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        /* 功能导航 */
        .nav-section {
            padding: 15px;
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }
        
        .nav-item {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 15px 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }
        
        .nav-icon {
            display: block;
            font-size: 24px;
            margin-bottom: 8px;
            color: #52c41a;
        }
        
        .nav-text {
            font-size: 12px;
            color: #666;
        }
        
        /* 药材与食材推荐 */
        .ingredients-section {
            padding: 0 15px;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .section-more {
            font-size: 14px;
            color: #52c41a;
            cursor: pointer;
        }
        
        .ingredients-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .ingredient-card {
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .ingredient-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }
        
        .ingredient-image {
            width: 100%;
            height: 100px;
            background: linear-gradient(135deg, #52c41a, #73d13d);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        
        .ingredient-info {
            padding: 10px;
        }
        
        .ingredient-name {
            display: block;
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .ingredient-nature, .ingredient-effect {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 3px;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 375px;
            height: 60px;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: space-around;
            box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .bottom-nav .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px;
            background: none;
            box-shadow: none;
            cursor: pointer;
        }
        
        .bottom-nav .nav-item.active .nav-icon {
            color: #52c41a;
        }
        
        .bottom-nav .nav-item.active .nav-text {
            color: #52c41a;
        }
        
        .bottom-nav .nav-icon {
            font-size: 20px;
            margin-bottom: 3px;
            color: #999;
        }
        
        .bottom-nav .nav-text {
            font-size: 10px;
            color: #999;
        }
        
        .add-btn .nav-icon {
            background: linear-gradient(45deg, #52c41a, #73d13d);
            color: #ffffff;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
        }
        
        /* 动画效果 */
        .fade-in {
            opacity: 0;
            transform: translateY(15px);
            animation: fadeInUp 0.8s ease forwards;
        }
        
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="header-left">
                <span class="leaf-icon">🍃</span>
                <span class="app-title">药食同源</span>
            </div>
            <div class="header-right">
                <span class="search-icon">🔍</span>
                <span class="notification-icon">🔔</span>
                <div class="avatar"></div>
            </div>
        </div>
        
        <!-- 主横幅 -->
        <div class="hero-banner fade-in">
            <div class="banner-overlay">
                <div class="banner-title">传统智慧 · 现代健康</div>
                <div class="banner-subtitle">探索药食同源的养生之道</div>
            </div>
        </div>
        
        <!-- 功能导航 -->
        <div class="nav-section">
            <div class="nav-grid">
                <div class="nav-item fade-in">
                    <span class="nav-icon">🌿</span>
                    <span class="nav-text">药材食材</span>
                </div>
                <div class="nav-item fade-in">
                    <span class="nav-icon">📋</span>
                    <span class="nav-text">食疗方案</span>
                </div>
                <div class="nav-item fade-in">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">营养分析</span>
                </div>
                <div class="nav-item fade-in">
                    <span class="nav-icon">💬</span>
                    <span class="nav-text">互动交流</span>
                </div>
            </div>
        </div>
        
        <!-- 药材与食材推荐 -->
        <div class="ingredients-section">
            <div class="section-header">
                <span class="section-title">药材与食材</span>
                <span class="section-more">查看全部 ></span>
            </div>
            <div class="ingredients-grid">
                <div class="ingredient-card fade-in">
                    <div class="ingredient-image">🌿</div>
                    <div class="ingredient-info">
                        <span class="ingredient-name">人参</span>
                        <span class="ingredient-nature">性味：甘、微苦、平</span>
                        <span class="ingredient-effect">归经：归脾、肺、心经</span>
                    </div>
                </div>
                <div class="ingredient-card fade-in">
                    <div class="ingredient-image">🔴</div>
                    <div class="ingredient-info">
                        <span class="ingredient-name">枸杞</span>
                        <span class="ingredient-nature">性味：甘、平</span>
                        <span class="ingredient-effect">归经：归肝、肾经</span>
                    </div>
                </div>
                <div class="ingredient-card fade-in">
                    <div class="ingredient-image">🥔</div>
                    <div class="ingredient-info">
                        <span class="ingredient-name">山药</span>
                        <span class="ingredient-nature">性味：甘、平</span>
                        <span class="ingredient-effect">归经：归脾、肺、肾经</span>
                    </div>
                </div>
                <div class="ingredient-card fade-in">
                    <div class="ingredient-image">⚪</div>
                    <div class="ingredient-info">
                        <span class="ingredient-name">莲子</span>
                        <span class="ingredient-nature">性味：甘、平</span>
                        <span class="ingredient-effect">归经：归脾、肾、心经</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active">
                <span class="nav-icon">🏠</span>
                <span class="nav-text">首页</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">🌿</span>
                <span class="nav-text">药材食材</span>
            </div>
            <div class="nav-item add-btn">
                <span class="nav-icon">+</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">📋</span>
                <span class="nav-text">食疗方案</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">👤</span>
                <span class="nav-text">我的</span>
            </div>
        </div>
    </div>
    
    <script>
        // 添加点击效果
        document.querySelectorAll('.nav-item, .ingredient-card').forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
        
        // 添加渐入动画延迟
        document.querySelectorAll('.fade-in').forEach((item, index) => {
            item.style.animationDelay = (index * 0.1) + 's';
        });
    </script>
</body>
</html>
