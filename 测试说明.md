# 食疗方案详情页面测试说明

## 问题描述
在食疗方案页面点击一个食疗方案后进入详情页面，详情页面没有回显图片。

## 解决方案

### 1. 后端接口完善
- ✅ 在 `RecipeController` 中添加了 `/recipe/detail/{planId}` 接口
- ✅ 在 `RecipeService` 接口中添加了 `getRecipeDetail` 方法
- ✅ 在 `RecipeServiceImpl` 中实现了详情查询逻辑
- ✅ 在 `RecipeListResponse` 中添加了 `steps` 和 `efficacy` 字段

### 2. 前端页面更新
- ✅ 修改了 `recipe-detail.vue` 页面，使用真实的后端数据
- ✅ 更新了图片字段映射：`image` → `imageUrl`
- ✅ 添加了加载状态和错误处理
- ✅ 集成了收藏功能

### 3. 数据字段映射

#### 后端返回字段 → 前端显示字段
- `id` → `recipeDetail.id`
- `title` → `recipeDetail.title`
- `description` → `recipeDetail.description`
- `imageUrl` → `recipeDetail.imageUrl` ✅ **关键修复**
- `suitableFor` → `recipeDetail.suitableFor`
- `efficacy` → `recipeDetail.effects`
- `steps` → `recipeDetail.method`
- `rating` → `recipeDetail.rating`
- `viewCount` → `recipeDetail.viewCount`
- `doctor` → `recipeDetail.doctor`

## 测试步骤

### 1. 启动服务
```bash
# 启动 Nacos 服务
# 启动 Order_Demo 服务 (端口1004)
# 启动 Gateway 网关服务 (端口8083)
```

### 2. 测试后端接口
```bash
# 测试健康检查
GET http://localhost:8083/test/health

# 测试获取方案列表
GET http://localhost:8083/recipe/list

# 测试获取方案详情
GET http://localhost:8083/recipe/detail/1
```

### 3. 测试前端页面
1. 在 uni-app 中运行项目
2. 进入食疗方案页面
3. 点击任意一个食疗方案卡片
4. 检查详情页面是否正确显示：
   - ✅ 方案图片
   - ✅ 方案标题
   - ✅ 适用人群
   - ✅ 方案描述
   - ✅ 主要功效
   - ✅ 制作方法
   - ✅ 医生信息
   - ✅ 评分和浏览量

## 数据库测试数据

当前数据库中有3个测试方案：
1. **补气养生汤** (ID: 1)
   - 图片: `http://*************:9000/test/补气养生汤.jpg`
2. **清热解暑粥** (ID: 2)
   - 图片: `http://*************:9000/test/清热解暑粥.jpg`
3. **美容养颜茶** (ID: 3)
   - 图片: `http://*************:9000/test/美容养颜茶.jpg`

## 常见问题排查

### 1. 图片不显示
- 检查网络连接
- 确认图片URL是否可访问
- 查看浏览器控制台是否有错误

### 2. 数据为空
- 检查后端服务是否正常启动
- 确认数据库连接正常
- 查看后端日志是否有错误

### 3. 接口调用失败
- 确认网关服务正常运行
- 检查服务注册是否成功
- 验证接口路径是否正确

## 预期结果

修复后，详情页面应该能够：
1. ✅ 正确显示方案图片
2. ✅ 显示完整的方案信息
3. ✅ 支持收藏功能
4. ✅ 显示加载状态
5. ✅ 处理错误情况 