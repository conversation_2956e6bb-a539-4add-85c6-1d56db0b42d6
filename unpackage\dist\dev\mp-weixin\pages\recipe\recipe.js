"use strict";
const common_vendor = require("../../common/vendor.js");
const api_recipe = require("../../api/recipe.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      activeFilter: "all",
      filterList: [
        { id: "all", name: "全部" },
        { id: "tonify", name: "补益类" },
        { id: "clear", name: "清热类" },
        { id: "regulate", name: "调理类" },
        { id: "beauty", name: "美容类" },
        { id: "weight", name: "减肥类" }
      ],
      recipeList: [],
      // 改为空数组，从后端获取数据
      loading: false,
      // 加载状态
      page: 1,
      // 当前页码
      size: 10,
      // 每页大小
      hasMore: true,
      // 是否还有更多数据
      userId: 2
      // 临时用户ID，实际应该从登录状态获取
    };
  },
  computed: {
    filteredRecipes() {
      return this.recipeList;
    }
  },
  /**
   * 页面加载时获取数据
   */
  async onLoad() {
    common_vendor.index.__f__("log", "at pages/recipe/recipe.vue:160", "页面onLoad - 开始加载数据");
    await this.loadRecipeList();
  },
  /**
   * 页面显示时刷新数据（如果需要）
   */
  async onShow() {
    common_vendor.index.__f__("log", "at pages/recipe/recipe.vue:168", "页面onShow - 页面显示");
    if (this.recipeList.length === 0 && !this.loading) {
      common_vendor.index.__f__("log", "at pages/recipe/recipe.vue:171", "页面onShow - 数据为空，重新加载");
      await this.loadRecipeList();
    }
  },
  methods: {
    /**
     * 加载食疗方案列表
     */
    async loadRecipeList(isRefresh = true) {
      if (this.loading)
        return;
      common_vendor.index.__f__("log", "at pages/recipe/recipe.vue:183", "loadRecipeList - 开始加载", { isRefresh, activeFilter: this.activeFilter, searchKeyword: this.searchKeyword });
      this.loading = true;
      try {
        if (isRefresh) {
          this.page = 1;
          this.recipeList = [];
        }
        const params = {
          page: this.page,
          size: this.size,
          userId: this.userId
        };
        if (this.searchKeyword) {
          params.keyword = this.searchKeyword;
        }
        if (this.activeFilter !== "all") {
          params.category = this.activeFilter;
        }
        common_vendor.index.__f__("log", "at pages/recipe/recipe.vue:210", "loadRecipeList - 请求参数", params);
        const response = await api_recipe.getRecipeList(params);
        common_vendor.index.__f__("log", "at pages/recipe/recipe.vue:214", "loadRecipeList - 响应结果", response);
        if (response.code === 200 && response.data) {
          const newData = response.data.records || [];
          if (isRefresh) {
            this.recipeList = newData;
          } else {
            this.recipeList = [...this.recipeList, ...newData];
          }
          this.hasMore = newData.length === this.size;
          await this.loadLikeStatus();
          if (this.searchKeyword) {
            this.recordSearchLog();
          }
        } else {
          common_vendor.index.showToast({
            title: response.message || "获取数据失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/recipe/recipe.vue:242", "获取食疗方案列表失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请稍后重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    /**
     * 搜索功能
     */
    async onSearch() {
      this.page = 1;
      await this.loadRecipeList(true);
    },
    /**
     * 选择筛选条件
     */
    async selectFilter(filterId) {
      this.activeFilter = filterId;
      this.page = 1;
      await this.loadRecipeList(true);
    },
    /**
     * 切换收藏状态
     */
    async toggleFavorite(item, index) {
      try {
        const response = await api_recipe.toggleFavorite({
          planId: item.id,
          userId: this.userId
        });
        if (response.code === 200) {
          this.recipeList[index].isFavorite = response.data.isFavorite;
          common_vendor.index.showToast({
            title: response.data.isFavorite ? "已收藏" : "已取消收藏",
            icon: "none",
            duration: 1500
          });
        } else {
          common_vendor.index.showToast({
            title: response.message || "操作失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/recipe/recipe.vue:297", "收藏操作失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请稍后重试",
          icon: "none"
        });
      }
    },
    /**
     * 跳转到详情页
     */
    async goToDetail(item) {
      try {
        const response = await api_recipe.incrementViewCount(item.id, this.userId);
        if (response && response.code === 200) {
          common_vendor.index.__f__("log", "at pages/recipe/recipe.vue:314", "浏览量统计结果:", response.data);
          if (response.data.isFirstView) {
            common_vendor.index.__f__("log", "at pages/recipe/recipe.vue:316", "首次浏览，浏览量+1");
          } else {
            common_vendor.index.__f__("log", "at pages/recipe/recipe.vue:318", "已浏览过，不重复计算");
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/recipe/recipe.vue:322", "增加浏览量失败:", error);
      }
      common_vendor.index.navigateTo({
        url: `/pages/recipe-detail/recipe-detail?id=${item.id}`
      });
    },
    /**
     * 记录搜索日志
     */
    async recordSearchLog() {
      try {
        await api_recipe.recordSearchLog({
          userId: this.userId,
          queryText: this.searchKeyword,
          resultCount: this.recipeList.length
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/recipe/recipe.vue:342", "记录搜索日志失败:", error);
      }
    },
    /**
     * 下拉刷新
     */
    async onPullDownRefresh() {
      await this.loadRecipeList(true);
      common_vendor.index.stopPullDownRefresh();
    },
    /**
     * 上拉加载更多
     */
    async onReachBottom() {
      if (this.hasMore && !this.loading) {
        this.page++;
        await this.loadRecipeList(false);
      }
    },
    /**
     * 点赞/取消点赞食疗方案
     */
    async toggleRecipeLike(item, index) {
      try {
        if (item.liking)
          return;
        item.liking = true;
        const response = await api_recipe.toggleLike(item.id, this.userId);
        if (response.code === 200) {
          this.recipeList[index].isLiked = response.data.isLiked;
          this.recipeList[index].likeCount = response.data.likeCount;
          common_vendor.index.showToast({
            title: response.data.message || (response.data.isLiked ? "点赞成功" : "取消点赞"),
            icon: "none",
            duration: 1500
          });
        } else {
          common_vendor.index.showToast({
            title: response.message || "操作失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/recipe/recipe.vue:392", "点赞操作失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请稍后重试",
          icon: "none"
        });
      } finally {
        item.liking = false;
      }
    },
    /**
     * 批量获取点赞状态
     */
    async loadLikeStatus() {
      try {
        if (this.recipeList.length === 0)
          return;
        const planIds = this.recipeList.map((item) => item.id);
        const response = await api_recipe.getBatchLikeStatus(this.userId, planIds);
        if (response.code === 200) {
          this.recipeList.forEach((item) => {
            const likeStatus = response.data[item.id.toString()];
            if (likeStatus !== void 0) {
              item.isLiked = likeStatus;
            }
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/recipe/recipe.vue:422", "获取点赞状态失败:", error);
      }
    },
    /**
     * 导航跳转
     */
    goToPage(page) {
      switch (page) {
        case "home":
          common_vendor.index.navigateTo({
            url: "/pages/index/index"
          });
          break;
        case "ingredients":
          common_vendor.index.navigateTo({
            url: "/pages/ingredients/ingredients"
          });
          break;
        case "recipe":
          break;
        case "profile":
          common_vendor.index.navigateTo({
            url: "/pages/profile/profile"
          });
          break;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearch && $options.onSearch(...args)]),
    b: $data.searchKeyword,
    c: common_vendor.f($data.filterList, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.n($data.activeFilter === item.id ? "active" : ""),
        c: item.id,
        d: common_vendor.o(($event) => $options.selectFilter(item.id), item.id)
      };
    }),
    d: $data.loading && $data.recipeList.length === 0
  }, $data.loading && $data.recipeList.length === 0 ? {} : !$data.loading && $data.recipeList.length === 0 ? {} : common_vendor.e({
    f: common_vendor.f($options.filteredRecipes, (item, index, i0) => {
      return common_vendor.e({
        a: item.imageUrl || "/static/logo.png",
        b: common_vendor.n(item.isFavorite ? "favorited" : ""),
        c: common_vendor.o(($event) => $options.toggleFavorite(item, index), item.id),
        d: common_vendor.t(item.title),
        e: common_vendor.t(item.suitableFor),
        f: common_vendor.t(item.description),
        g: item.doctor
      }, item.doctor ? {
        h: item.doctor.avatar || "/static/logo.png",
        i: common_vendor.t(item.doctor.name)
      } : {}, {
        j: common_vendor.t(item.rating || 4.5),
        k: common_vendor.t(item.viewCount || 0),
        l: common_vendor.n(item.isLiked ? "liked" : ""),
        m: common_vendor.t(item.likeCount || 0),
        n: common_vendor.o(($event) => $options.toggleRecipeLike(item, index), item.id),
        o: item.id,
        p: common_vendor.o(($event) => $options.goToDetail(item), item.id),
        q: index * 0.1 + "s"
      });
    }),
    g: $data.loading && $data.recipeList.length > 0
  }, $data.loading && $data.recipeList.length > 0 ? {} : {}, {
    h: !$data.hasMore && $data.recipeList.length > 0
  }, !$data.hasMore && $data.recipeList.length > 0 ? {} : {}), {
    e: !$data.loading && $data.recipeList.length === 0,
    i: common_vendor.o(($event) => $options.goToPage("home")),
    j: common_vendor.o(($event) => $options.goToPage("ingredients")),
    k: common_vendor.o(($event) => $options.goToPage("recipe")),
    l: common_vendor.o(($event) => $options.goToPage("profile"))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fb437fc6"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/recipe/recipe.js.map
