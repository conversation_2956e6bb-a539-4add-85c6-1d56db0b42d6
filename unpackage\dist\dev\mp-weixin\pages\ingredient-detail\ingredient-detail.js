"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      ingredientId: "",
      loading: true,
      showImage: true,
      // 控制是否显示图片
      ingredientDetail: {}
    };
  },
  computed: {
    parsedTags() {
      return this.getTagsByEffect(this.ingredientDetail.efficacy || "");
    }
  },
  onLoad(options) {
    if (options.id) {
      this.ingredientId = options.id;
      this.loadIngredientDetail();
    }
  },
  methods: {
    // 加载药材详情
    async loadIngredientDetail() {
      try {
        this.loading = true;
        this.showImage = true;
        common_vendor.index.__f__("log", "at pages/ingredient-detail/ingredient-detail.vue:116", "加载药材详情，ID:", this.ingredientId);
        const response = await utils_api.api.getMedicinalById(this.ingredientId);
        common_vendor.index.__f__("log", "at pages/ingredient-detail/ingredient-detail.vue:119", "药材详情API响应:", response);
        if (response && response.code === 200 && response.data) {
          this.ingredientDetail = response.data;
          common_vendor.index.__f__("log", "at pages/ingredient-detail/ingredient-detail.vue:123", "药材详情加载成功:", this.ingredientDetail);
          common_vendor.index.__f__("log", "at pages/ingredient-detail/ingredient-detail.vue:124", "dosage字段:", this.ingredientDetail.dosage);
          common_vendor.index.__f__("log", "at pages/ingredient-detail/ingredient-detail.vue:125", "taboo字段:", this.ingredientDetail.taboo);
          common_vendor.index.__f__("log", "at pages/ingredient-detail/ingredient-detail.vue:126", "applicablepopulation字段:", this.ingredientDetail.applicablepopulation);
          common_vendor.index.__f__("log", "at pages/ingredient-detail/ingredient-detail.vue:127", "avatar图片字段:", this.ingredientDetail.avatar);
        } else {
          throw new Error("获取药材详情失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/ingredient-detail/ingredient-detail.vue:132", "加载药材详情失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 图片加载成功
    onImageLoad() {
      common_vendor.index.__f__("log", "at pages/ingredient-detail/ingredient-detail.vue:144", "图片加载成功:", this.ingredientDetail.avatar);
    },
    // 图片加载错误处理
    onImageError() {
      common_vendor.index.__f__("log", "at pages/ingredient-detail/ingredient-detail.vue:149", "图片加载失败，将显示图标:", this.ingredientDetail.avatar);
      this.showImage = false;
    },
    // 获取图标文字
    getIconText(name) {
      if (!name)
        return "药";
      return name.charAt(0);
    },
    // 格式化性味
    formatNature(meridian) {
      if (!meridian)
        return "甘，微温";
      const cleanMeridian = meridian.replace(/\r\n/g, "").replace(/\n/g, "").trim();
      if (cleanMeridian.includes("甘") && cleanMeridian.includes("温"))
        return "甘，微温";
      if (cleanMeridian.includes("苦") && cleanMeridian.includes("寒"))
        return "苦，寒";
      if (cleanMeridian.includes("辛") && cleanMeridian.includes("温"))
        return "辛，温";
      if (cleanMeridian.includes("酸") && cleanMeridian.includes("平"))
        return "酸，平";
      if (cleanMeridian.includes("咸") && cleanMeridian.includes("寒"))
        return "咸，寒";
      return "甘，微温";
    },
    // 根据功效生成标签
    getTagsByEffect(effect) {
      if (!effect)
        return ["功效"];
      const tags = [];
      if (effect.includes("补气"))
        tags.push("补气");
      if (effect.includes("补血"))
        tags.push("补血");
      if (effect.includes("增强免疫") || effect.includes("免疫"))
        tags.push("增强免疫");
      if (effect.includes("抗疲劳") || effect.includes("疲劳"))
        tags.push("抗疲劳");
      if (effect.includes("利尿"))
        tags.push("利尿");
      if (tags.length === 0) {
        if (effect.includes("补") || effect.includes("益"))
          tags.push("补益");
        if (effect.includes("清") || effect.includes("热"))
          tags.push("清热");
        if (effect.includes("调") || effect.includes("理"))
          tags.push("调理");
      }
      return tags.slice(0, 4);
    },
    // 获取默认描述
    getDefaultDescription() {
      const name = this.ingredientDetail.name || "该药材";
      const efficacy = this.ingredientDetail.efficacy || "具有多种功效";
      return `${name}为常用中药材，${efficacy}。${name}味甘，性微温，归肺、脾经。常用于气虚乏力、食少便溏、中气下陷、久泻脱肛、便血崩漏、表虚自汗、气虚水肿、痈疽难溃、久溃不敛、血虚萎黄、内热消渴等症。`;
    },
    // 获取适用人群
    getSuitableFor() {
      const efficacy = this.ingredientDetail.efficacy || "";
      if (efficacy.includes("补气")) {
        return "适用于气虚体质、免疫力低下、容易感冒、疲劳乏力、食欲不振、消化不良、水肿、自汗盗汗等人群。特别适合体质虚弱、病后康复期的人群使用。";
      } else if (efficacy.includes("补血")) {
        return "适用于血虚体质、面色苍白、头晕心悸、失眠多梦、月经不调等人群。特别适合女性朋友和贫血患者使用。";
      } else if (efficacy.includes("清热")) {
        return "适用于热性体质、口干舌燥、便秘、上火、咽喉肿痛等人群。特别适合夏季使用或体内有热的人群。";
      }
      return "适用于相应体质和症状的人群，建议在专业医师指导下使用。";
    },
    // 获取默认使用方法（当dosage字段为空时使用）
    getDefaultUsageMethod() {
      return `1. 煎汤：常用量9-30g，大剂量可用30-120g
2. 泡茶：3-6g，开水冲泡代茶饮
3. 炖汤：与鸡肉、排骨等一起炖煮
4. 研粉：研成细粉，每次3-6g，温水送服
5. 制膏：与其他药材制成膏方服用`;
    },
    // 获取默认注意事项（当taboo字段为空时使用）
    getDefaultPrecautions() {
      return `1. 表实邪盛、气滞湿阻、食积停滞、痈疽初起或溃后热毒尚盛等实证，以及阴虚阳亢者，均不宜服用
2. 感冒发热期间暂停使用
3. 高血压患者应在医生指导下使用
4. 孕妇慎用
5. 服用期间忌食萝卜、绿豆等`;
    },
    // 加入收藏
    addToCollection() {
      common_vendor.index.showToast({
        title: "已加入收藏",
        icon: "success"
      });
    },
    // 查看相关食疗方案
    viewRecipes() {
      common_vendor.index.navigateTo({
        url: "/pages/recipe/recipe"
      });
    },
    // 跳转到具体食疗方案
    goToRecipe(recipeId) {
      common_vendor.index.navigateTo({
        url: `/pages/recipe-detail/recipe-detail?id=${recipeId}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    b: $data.showImage && $data.ingredientDetail.avatar
  }, $data.showImage && $data.ingredientDetail.avatar ? {
    c: $data.ingredientDetail.avatar,
    d: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args)),
    e: common_vendor.o((...args) => $options.onImageLoad && $options.onImageLoad(...args))
  } : {
    f: common_vendor.t($options.getIconText($data.ingredientDetail.name))
  }, {
    g: common_vendor.t($data.ingredientDetail.name || "未知药材"),
    h: common_vendor.t($data.ingredientDetail.rname || "无别名"),
    i: common_vendor.f($options.parsedTags, (tag, k0, i0) => {
      return {
        a: common_vendor.t(tag),
        b: tag
      };
    }),
    j: common_vendor.t($data.ingredientDetail.name1 || "中药材"),
    k: common_vendor.t($options.formatNature($data.ingredientDetail.meridian))
  }), {
    l: !$data.loading
  }, !$data.loading ? {
    m: common_vendor.t($data.ingredientDetail.efficacy || "功效信息待补充"),
    n: common_vendor.t($data.ingredientDetail.description || $options.getDefaultDescription()),
    o: common_vendor.t($data.ingredientDetail.applicablepopulation || $options.getSuitableFor()),
    p: common_vendor.t($data.ingredientDetail.dosage || $options.getDefaultUsageMethod()),
    q: common_vendor.t($data.ingredientDetail.taboo || $options.getDefaultPrecautions()),
    r: common_vendor.f($data.ingredientDetail.relatedRecipes, (recipe, k0, i0) => {
      return {
        a: recipe.image,
        b: common_vendor.t(recipe.name),
        c: common_vendor.t(recipe.effect),
        d: recipe.id,
        e: common_vendor.o(($event) => $options.goToRecipe(recipe.id), recipe.id)
      };
    })
  } : {}, {
    s: common_vendor.o((...args) => $options.addToCollection && $options.addToCollection(...args)),
    t: common_vendor.o((...args) => $options.viewRecipes && $options.viewRecipes(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4c08f3f1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ingredient-detail/ingredient-detail.js.map
