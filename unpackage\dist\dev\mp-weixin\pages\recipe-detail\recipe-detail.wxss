
.container.data-v-fc6387aa {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 食疗方案头部图片 */
.recipe-header.data-v-fc6387aa {
	position: relative;
	height: 500rpx;
}
.recipe-image.data-v-fc6387aa {
	width: 100%;
	height: 100%;
}
.favorite-btn.data-v-fc6387aa {
	position: absolute;
	top: 30rpx;
	right: 30rpx;
	width: 80rpx;
	height: 80rpx;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.favorite-icon.data-v-fc6387aa {
	font-size: 40rpx;
	color: #ccc;
}
.favorite-icon.favorited.data-v-fc6387aa {
	color: #4CAF50;
}

/* 食疗方案信息 */
.recipe-info.data-v-fc6387aa {
	background-color: #fff;
	padding: 40rpx;
}
.recipe-title.data-v-fc6387aa {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}
.recipe-suitable.data-v-fc6387aa {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 30rpx;
}
.doctor-info.data-v-fc6387aa {
	display: flex;
	align-items: center;
	gap: 20rpx;
}
.doctor-avatar.data-v-fc6387aa {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	overflow: hidden;
}
.doctor-avatar image.data-v-fc6387aa {
	width: 100%;
	height: 100%;
}
.doctor-details.data-v-fc6387aa {
	flex: 1;
}
.doctor-name.data-v-fc6387aa {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}
.doctor-title.data-v-fc6387aa {
	font-size: 24rpx;
	color: #666;
}
.rating-info.data-v-fc6387aa {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 10rpx;
}
.rating.data-v-fc6387aa {
	display: flex;
	align-items: center;
}
.star.data-v-fc6387aa {
	color: #FFD700;
	font-size: 28rpx;
	margin-right: 8rpx;
}
.rating-score.data-v-fc6387aa {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
}
.view-count.data-v-fc6387aa {
	display: flex;
	align-items: center;
}
.view-icon.data-v-fc6387aa {
	font-size: 24rpx;
	margin-right: 8rpx;
}
.view-number.data-v-fc6387aa {
	font-size: 24rpx;
	color: #666;
}

/* 详细内容 */
.recipe-content.data-v-fc6387aa {
	margin-top: 20rpx;
}
.section.data-v-fc6387aa {
	background-color: #fff;
	padding: 40rpx;
	margin-bottom: 20rpx;
}
.section-title.data-v-fc6387aa {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}
.section-content.data-v-fc6387aa {
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
	white-space: pre-line;
}

/* 底部操作按钮 */
.bottom-actions.data-v-fc6387aa {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	border-top: 1rpx solid #eee;
}
.action-btn.data-v-fc6387aa {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
}
.action-btn.primary.data-v-fc6387aa {
	background-color: #4CAF50;
	color: #fff;
}
.action-btn.secondary.data-v-fc6387aa {
	background-color: #f5f5f5;
	color: #666;
}

/* 加载状态样式 */
.loading-container.data-v-fc6387aa {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
}
.loading-text.data-v-fc6387aa {
	font-size: 28rpx;
	color: #999;
}
