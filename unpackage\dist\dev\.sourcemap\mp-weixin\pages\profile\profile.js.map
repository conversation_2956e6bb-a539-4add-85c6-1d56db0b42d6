{"version": 3, "file": "profile.js", "sources": ["pages/profile/profile.vue", "../产出物/安装包/HBuilderX/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZmlsZS9wcm9maWxlLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 用户信息头部 -->\n\t\t<view class=\"user-header\" @click=\"handleHeaderClick\">\n\t\t\t<view class=\"user-avatar\">\n\t\t\t\t<image v-if=\"isLoggedIn\" :src=\"userInfo.avatar\" mode=\"aspectFill\"></image>\n\t\t\t\t<text v-else class=\"avatar-placeholder\">?</text>\n\t\t\t</view>\n\t\t\t<view class=\"user-info\">\n\t\t\t\t<text class=\"user-name\">{{ isLoggedIn ? userInfo.name : '立即登录' }}</text>\n\t\t\t\t<text class=\"user-desc\">{{ isLoggedIn ? userInfo.description : '登录后享受更多功能' }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"user-stats\" v-if=\"isLoggedIn\">\n\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t<text class=\"stat-number\">{{ userInfo.favoriteCount }}</text>\n\t\t\t\t\t<text class=\"stat-label\">收藏</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t<text class=\"stat-number\">{{ userInfo.viewCount }}</text>\n\t\t\t\t\t<text class=\"stat-label\">浏览</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"login-prompt\" v-else>\n\t\t\t\t<text class=\"login-text\">点击登录</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 功能菜单 -->\n\t\t<view class=\"menu-section\">\n\t\t\t<view class=\"menu-group\">\n\t\t\t\t<view class=\"menu-item\" @click=\"goToFavorites\">\n\t\t\t\t\t<text class=\"menu-icon\">❤️</text>\n\t\t\t\t\t<text class=\"menu-text\">我的收藏</text>\n\t\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"menu-item\" @click=\"goToHistory\">\n\t\t\t\t\t<text class=\"menu-icon\">📖</text>\n\t\t\t\t\t<text class=\"menu-text\">浏览历史</text>\n\t\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"menu-item\" @click=\"goToNotes\">\n\t\t\t\t\t<text class=\"menu-icon\">📝</text>\n\t\t\t\t\t<text class=\"menu-text\">我的笔记</text>\n\t\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"menu-group\">\n\t\t\t\t<view class=\"menu-item\" @click=\"goToSettings\">\n\t\t\t\t\t<text class=\"menu-icon\">⚙️</text>\n\t\t\t\t\t<text class=\"menu-text\">设置</text>\n\t\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"menu-item\" @click=\"goToHelp\">\n\t\t\t\t\t<text class=\"menu-icon\">❓</text>\n\t\t\t\t\t<text class=\"menu-text\">帮助与反馈</text>\n\t\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"menu-item\" @click=\"goToAbout\">\n\t\t\t\t\t<text class=\"menu-icon\">ℹ️</text>\n\t\t\t\t\t<text class=\"menu-text\">关于我们</text>\n\t\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"menu-item logout-item\" @click=\"logout\" v-if=\"isLoggedIn\">\n\t\t\t\t\t<text class=\"menu-icon logout-icon\">🚪</text>\n\t\t\t\t\t<text class=\"menu-text logout-text\">退出登录</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 最近收藏 -->\n\t\t<view class=\"recent-section\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<text class=\"section-title\">最近收藏</text>\n\t\t\t\t<text class=\"section-more\" @click=\"goToFavorites\">查看全部 ></text>\n\t\t\t</view>\n\t\t\t<view class=\"recent-list\">\n\t\t\t\t<view class=\"recent-item\" v-for=\"item in recentFavorites\" :key=\"item.id\" @click=\"goToDetail(item)\">\n\t\t\t\t\t<image class=\"recent-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\n\t\t\t\t\t<view class=\"recent-info\">\n\t\t\t\t\t\t<text class=\"recent-title\">{{ item.title }}</text>\n\t\t\t\t\t\t<text class=\"recent-type\">{{ item.type }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 底部导航 -->\n\t\t<view class=\"bottom-nav\">\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('home')\">\n\t\t\t\t<text class=\"nav-icon\">🏠</text>\n\t\t\t\t<text class=\"nav-text\">首页</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('ingredients')\">\n\t\t\t\t<text class=\"nav-icon\">🌿</text>\n\t\t\t\t<text class=\"nav-text\">药材食材</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-item add-btn\">\n\t\t\t\t<text class=\"nav-icon add-icon\">+</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-item\" @click=\"goToPage('recipe')\">\n\t\t\t\t<text class=\"nav-icon\">📋</text>\n\t\t\t\t<text class=\"nav-text\">食疗方案</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-item active\" @click=\"goToPage('profile')\">\n\t\t\t\t<text class=\"nav-icon\">👤</text>\n\t\t\t\t<text class=\"nav-text\">我的</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisLoggedIn: false,\n\t\t\tuserInfo: {\n\t\t\t\tname: '养生达人',\n\t\t\t\tdescription: '关注健康，享受生活',\n\t\t\t\tavatar: '/static/logo.png',\n\t\t\t\tfavoriteCount: 28,\n\t\t\t\tviewCount: 156\n\t\t\t},\n\t\t\trecentFavorites: [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\ttitle: '补气养生汤',\n\t\t\t\t\ttype: '食疗方案',\n\t\t\t\t\timage: '/static/logo.png'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\ttitle: '黄芪',\n\t\t\t\t\ttype: '中药材',\n\t\t\t\t\timage: '/static/logo.png'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 3,\n\t\t\t\t\ttitle: '清热解暑粥',\n\t\t\t\t\ttype: '食疗方案',\n\t\t\t\t\timage: '/static/logo.png'\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t},\n\n\tonLoad() {\n\t\tthis.checkLoginStatus();\n\t},\n\n\tonShow() {\n\t\t// 每次显示页面时检查登录状态\n\t\tthis.checkLoginStatus();\n\t},\n\n\tmethods: {\n\t\t// 检查登录状态\n\t\tcheckLoginStatus() {\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\n\t\t\tconst token = uni.getStorageSync('token');\n\n\t\t\tif (userInfo && token) {\n\t\t\t\tthis.isLoggedIn = true;\n\t\t\t\tthis.userInfo = {\n\t\t\t\t\t...this.userInfo,\n\t\t\t\t\t...userInfo\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tthis.isLoggedIn = false;\n\t\t\t}\n\t\t},\n\n\t\t// 处理头部点击事件\n\t\thandleHeaderClick() {\n\t\t\tif (!this.isLoggedIn) {\n\t\t\t\tthis.goToLogin();\n\t\t\t}\n\t\t},\n\n\t\t// 跳转到登录页面\n\t\tgoToLogin() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/login/login'\n\t\t\t});\n\t\t},\n\t\t// 跳转到收藏页面\n\t\tgoToFavorites() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '跳转到收藏页面',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到历史记录\n\t\tgoToHistory() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '跳转到浏览历史',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到笔记\n\t\tgoToNotes() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '跳转到我的笔记',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到设置\n\t\tgoToSettings() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '跳转到设置页面',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到帮助\n\t\tgoToHelp() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '跳转到帮助页面',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到关于\n\t\tgoToAbout() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '跳转到关于页面',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\n\t\t// 退出登录\n\t\tlogout() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认退出',\n\t\t\t\tcontent: '确定要退出登录吗？',\n\t\t\t\tconfirmText: '退出',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tconfirmColor: '#ff4757',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// 清除本地存储的用户信息\n\t\t\t\t\t\tuni.removeStorageSync('userInfo');\n\t\t\t\t\t\tuni.removeStorageSync('token');\n\n\t\t\t\t\t\t// 更新登录状态\n\t\t\t\t\t\tthis.isLoggedIn = false;\n\n\t\t\t\t\t\t// 显示退出成功提示\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '已退出登录',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到详情\n\t\tgoToDetail(item) {\n\t\t\tif (item.type === '食疗方案') {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/recipe-detail/recipe-detail?id=${item.id}`\n\t\t\t\t});\n\t\t\t} else if (item.type === '中药材') {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/ingredient-detail/ingredient-detail?id=${item.id}`\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 导航跳转\n\t\tgoToPage(page) {\n\t\t\tswitch(page) {\n\t\t\t\tcase 'home':\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'ingredients':\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/ingredients/ingredients'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'recipe':\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/recipe/recipe'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'profile':\n\t\t\t\t\t// 当前页面\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.container {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n\tpadding-bottom: 120rpx;\n}\n\n/* 用户信息头部 */\n.user-header {\n\tbackground: linear-gradient(135deg, #4CAF50, #66BB6A);\n\tpadding: 60rpx 40rpx 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 30rpx;\n\tcursor: pointer;\n}\n\n.user-header:active {\n\topacity: 0.9;\n}\n\n.user-avatar {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 50%;\n\toverflow: hidden;\n\tborder: 4rpx solid rgba(255, 255, 255, 0.3);\n}\n\n.user-avatar image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.avatar-placeholder {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 50%;\n\tbackground: rgba(255, 255, 255, 0.3);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 48rpx;\n\tcolor: white;\n\tfont-weight: bold;\n}\n\n.user-info {\n\tflex: 1;\n}\n\n.user-name {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #fff;\n\tdisplay: block;\n\tmargin-bottom: 10rpx;\n}\n\n.user-desc {\n\tfont-size: 26rpx;\n\tcolor: rgba(255, 255, 255, 0.8);\n}\n\n.user-stats {\n\tdisplay: flex;\n\tgap: 30rpx;\n}\n\n.stat-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.stat-number {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #fff;\n\tmargin-bottom: 5rpx;\n}\n\n.stat-label {\n\tfont-size: 22rpx;\n\tcolor: rgba(255, 255, 255, 0.8);\n}\n\n.login-prompt {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 20rpx 40rpx;\n\tbackground: rgba(255, 255, 255, 0.2);\n\tborder-radius: 30rpx;\n\tborder: 2rpx solid rgba(255, 255, 255, 0.3);\n}\n\n.login-text {\n\tcolor: white;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n}\n\n/* 功能菜单 */\n.menu-section {\n\tmargin-top: 20rpx;\n}\n\n.menu-group {\n\tbackground-color: #fff;\n\tmargin-bottom: 20rpx;\n}\n\n.menu-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx 40rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.menu-item:last-child {\n\tborder-bottom: none;\n}\n\n.menu-icon {\n\tfont-size: 36rpx;\n\tmargin-right: 30rpx;\n}\n\n.menu-text {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.menu-arrow {\n\tfont-size: 28rpx;\n\tcolor: #ccc;\n}\n\n/* 退出登录按钮样式 */\n.logout-item {\n\tmargin-top: 20rpx;\n\tborder-top: 1rpx solid #f0f0f0;\n\tpadding-top: 30rpx;\n}\n\n.logout-icon {\n\tcolor: #ff4757;\n}\n\n.logout-text {\n\tcolor: #ff4757;\n\tfont-weight: 500;\n}\n\n.logout-item:active {\n\tbackground-color: #fff5f5;\n}\n\n/* 最近收藏 */\n.recent-section {\n\tbackground-color: #fff;\n\tmargin-top: 20rpx;\n\tpadding: 40rpx;\n}\n\n.section-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 30rpx;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.section-more {\n\tfont-size: 26rpx;\n\tcolor: #4CAF50;\n}\n\n.recent-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.recent-item {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n}\n\n.recent-image {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 10rpx;\n}\n\n.recent-info {\n\tflex: 1;\n}\n\n.recent-title {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 8rpx;\n}\n\n.recent-type {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n/* 底部导航 */\n.bottom-nav {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 120rpx;\n\tbackground-color: #fff;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-around;\n\tborder-top: 1rpx solid #eee;\n\tz-index: 999;\n}\n\n.nav-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tflex: 1;\n}\n\n.nav-item.active .nav-text {\n\tcolor: #4CAF50;\n}\n\n.nav-item.add-btn {\n\tposition: relative;\n}\n\n.nav-icon {\n\tfont-size: 40rpx;\n\tmargin-bottom: 8rpx;\n}\n\n.add-icon {\n\tbackground-color: #4CAF50;\n\tcolor: #fff;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 50rpx;\n\tfont-weight: bold;\n}\n\n.nav-text {\n\tfont-size: 20rpx;\n\tcolor: #666;\n}\n</style>\n", "import MiniProgramPage from 'D:/桌面/Medicine-uniapp/pages/profile/profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAiHA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,UAAU;AAAA,QACT,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,WAAW;AAAA,MACX;AAAA,MACD,iBAAiB;AAAA,QAChB;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA,EACA;AAAA,EAED,SAAS;AACR,SAAK,iBAAgB;AAAA,EACrB;AAAA,EAED,SAAS;AAER,SAAK,iBAAgB;AAAA,EACrB;AAAA,EAED,SAAS;AAAA;AAAA,IAER,mBAAmB;AAClB,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,YAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AAExC,UAAI,YAAY,OAAO;AACtB,aAAK,aAAa;AAClB,aAAK,WAAW;AAAA,UACf,GAAG,KAAK;AAAA,UACR,GAAG;AAAA;aAEE;AACN,aAAK,aAAa;AAAA,MACnB;AAAA,IACA;AAAA;AAAA,IAGD,oBAAoB;AACnB,UAAI,CAAC,KAAK,YAAY;AACrB,aAAK,UAAS;AAAA,MACf;AAAA,IACA;AAAA;AAAA,IAGD,YAAY;AACXA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAED,gBAAgB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACVA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,SAAS;AACRA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAEhBA,gCAAI,kBAAkB,UAAU;AAChCA,gCAAI,kBAAkB,OAAO;AAG7B,iBAAK,aAAa;AAGlBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,MAAM;AAChB,UAAI,KAAK,SAAS,QAAQ;AACzBA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,yCAAyC,KAAK,EAAE;AAAA,QACtD,CAAC;AAAA,iBACS,KAAK,SAAS,OAAO;AAC/BA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,iDAAiD,KAAK,EAAE;AAAA,QAC9D,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,SAAS,MAAM;AACd,cAAO,MAAI;AAAA,QACV,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,UACN,CAAC;AACD;AAAA,QACD,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,UACN,CAAC;AACD;AAAA,QACD,KAAK;AACJA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,UACN,CAAC;AACD;AAAA,MAIF;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzSA,GAAG,WAAW,eAAe;"}