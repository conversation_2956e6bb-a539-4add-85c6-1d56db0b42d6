/**
 * 食疗方案相关API接口
 * 通过网关访问后端服务
 */

import { API_CONFIG, API_PATHS } from '../config/api.js';

/**
 * 通用请求方法
 * @param {Object} options 请求配置
 * @returns {Promise} 请求结果
 */
function request(options) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: API_CONFIG.baseUrl + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      timeout: API_CONFIG.timeout,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

/**
 * 获取食疗方案列表
 * @param {Object} params 查询参数
 * @param {String} params.keyword 搜索关键词
 * @param {String} params.category 分类筛选
 * @param {Number} params.page 页码
 * @param {Number} params.size 每页大小
 * @param {Number} params.userId 用户ID
 * @returns {Promise} 方案列表
 */
export function getRecipeList(params = {}) {
  console.log('getRecipeList - 原始参数:', params);
  
  // 构建查询字符串
  const queryString = Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null && params[key] !== '')
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
  
  const url = queryString ? `${API_PATHS.RECIPE.LIST}?${queryString}` : API_PATHS.RECIPE.LIST;
  
  console.log('getRecipeList - 最终URL:', API_CONFIG.baseUrl + url);
  
  return request({
    url: url,
    method: 'GET'
  });
}

/**
 * 收藏/取消收藏食疗方案
 * @param {Object} data 请求数据
 * @param {Number} data.planId 方案ID
 * @param {Number} data.userId 用户ID
 * @returns {Promise} 操作结果
 */
export function toggleFavorite(data) {
  return request({
    url: API_PATHS.RECIPE.FAVORITE,
    method: 'POST',
    data: data
  });
}

/**
 * 增加方案浏览量
 * 每个用户对每个方案只能贡献1次浏览量
 * @param {Number} planId 方案ID
 * @param {Number} userId 用户ID
 * @returns {Promise} 操作结果
 */
export function incrementViewCount(planId, userId) {
  return request({
    url: `${API_PATHS.RECIPE.VIEW_COUNT}/${planId}`,
    method: 'POST',
    data: {
      userId: userId
    }
  });
}

/**
 * 记录搜索日志
 * @param {Object} data 搜索数据
 * @param {Number} data.userId 用户ID
 * @param {String} data.queryText 搜索关键词
 * @param {Number} data.resultCount 结果数量
 * @returns {Promise} 操作结果
 */
export function recordSearchLog(data) {
  return request({
    url: API_PATHS.RECIPE.SEARCH_LOG,
    method: 'POST',
    data: data
  });
}

/**
 * 获取食疗方案详情
 * @param {Number} planId 方案ID
 * @returns {Promise} 方案详情
 */
export function getRecipeDetail(planId) {
  return request({
    url: `${API_PATHS.RECIPE.DETAIL}/${planId}`,
    method: 'GET'
  });
}

/**
 * 健康检查
 * @returns {Promise} 服务状态
 */
export function healthCheck() {
  return request({
    url: API_PATHS.TEST.HEALTH,
    method: 'GET'
  });
}

/**
 * 点赞/取消点赞食疗方案
 * @param {Number} planId 方案ID
 * @param {Number} userId 用户ID
 * @returns {Promise} 操作结果
 */
export function toggleLike(planId, userId) {
  return request({
    url: `${API_PATHS.RECIPE.LIKE}/${planId}`,
    method: 'POST',
    data: {
      userId: userId
    }
  });
}

/**
 * 批量获取点赞状态
 * @param {Number} userId 用户ID
 * @param {Array} planIds 方案ID列表
 * @returns {Promise} 点赞状态映射
 */
export function getBatchLikeStatus(userId, planIds) {
  return request({
    url: API_PATHS.RECIPE.BATCH_LIKE_STATUS,
    method: 'POST',
    data: {
      userId: userId,
      planIds: planIds
    }
  });
} 