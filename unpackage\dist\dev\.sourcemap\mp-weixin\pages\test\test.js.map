{"version": 3, "file": "test.js", "sources": ["pages/test/test.vue", "../产出物/安装包/HBuilderX/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGVzdC90ZXN0LnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"header\">\r\n\t\t\t<text class=\"title\">API测试页面</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"info-section\">\r\n\t\t\t<text class=\"info-title\">当前配置信息</text>\r\n\t\t\t<text class=\"info-item\">BASE_URL: {{ baseUrl }}</text>\r\n\t\t\t<text class=\"info-item\">当前页面: {{ currentUrl }}</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"test-section\">\r\n\t\t\t<button class=\"test-btn\" @click=\"testAPI\" :disabled=\"loading\">\r\n\t\t\t\t{{ loading ? '测试中...' : '测试API连接' }}\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"result-section\" v-if=\"result\">\r\n\t\t\t<text class=\"result-title\">测试结果</text>\r\n\t\t\t<text class=\"result-content\">{{ result }}</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"log-section\" v-if=\"logs.length > 0\">\r\n\t\t\t<text class=\"log-title\">日志信息</text>\r\n\t\t\t<text class=\"log-item\" v-for=\"(log, index) in logs\" :key=\"index\">{{ log }}</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport api from '@/utils/api.js';\r\nimport config from '@/utils/config.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloading: false,\r\n\t\t\tresult: '',\r\n\t\t\tlogs: [],\r\n\t\t\tbaseUrl: config.baseURL,\r\n\t\t\tcurrentUrl: ''\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.currentUrl = typeof window !== 'undefined' && window.location ? window.location.href : '未知';\r\n\t\tthis.addLog('页面加载完成');\r\n\t\tthis.addLog(`BASE_URL: ${this.baseUrl}`);\r\n\t\tthis.addLog(`当前页面: ${this.currentUrl}`);\r\n\t},\r\n\tmethods: {\r\n\t\taddLog(message) {\r\n\t\t\tconst timestamp = new Date().toLocaleTimeString();\r\n\t\t\tthis.logs.push(`[${timestamp}] ${message}`);\r\n\t\t},\r\n\t\t\r\n\t\tasync testAPI() {\r\n\t\t\tthis.loading = true;\r\n\t\t\tthis.result = '';\r\n\t\t\tthis.addLog('开始测试API...');\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tthis.addLog('调用 api.getMedicinalList()');\r\n\t\t\t\tconst response = await api.getMedicinalList();\r\n\t\t\t\tthis.addLog('API调用成功');\r\n\t\t\t\tthis.result = `成功！获取到 ${response.length} 条数据`;\r\n\t\t\t\tthis.addLog(`响应数据: ${JSON.stringify(response.slice(0, 2))}`);\r\n\t\t\t} catch (error) {\r\n\t\t\t\tthis.addLog('API调用失败');\r\n\t\t\t\tthis.result = `失败：${error.message || error}`;\r\n\t\t\t\tthis.addLog(`错误详情: ${JSON.stringify(error)}`);\r\n\t\t\t} finally {\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n\tpadding: 20rpx;\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.header {\r\n\ttext-align: center;\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.info-section, .test-section, .result-section, .log-section {\r\n\tbackground: white;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.info-title, .result-title, .log-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.info-item, .result-content, .log-item {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10rpx;\r\n\tdisplay: block;\r\n\tword-break: break-all;\r\n}\r\n\r\n.test-btn {\r\n\twidth: 100%;\r\n\tbackground: #52c41a;\r\n\tcolor: white;\r\n\tborder: none;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 20rpx;\r\n\tfont-size: 32rpx;\r\n}\r\n\r\n.test-btn:disabled {\r\n\tbackground: #ccc;\r\n}\r\n\r\n.log-item {\r\n\tfont-family: monospace;\r\n\tfont-size: 24rpx;\r\n\tbackground: #f8f8f8;\r\n\tpadding: 10rpx;\r\n\tmargin-bottom: 5rpx;\r\n\tborder-radius: 5rpx;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/桌面/Medicine-uniapp/pages/test/test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["config", "api"], "mappings": ";;;;AAkCA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM,CAAE;AAAA,MACR,SAASA,aAAM,OAAC;AAAA,MAChB,YAAY;AAAA,IACb;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,aAAa,OAAO,WAAW,eAAe,OAAO,WAAW,OAAO,SAAS,OAAO;AAC5F,SAAK,OAAO,QAAQ;AACpB,SAAK,OAAO,aAAa,KAAK,OAAO,EAAE;AACvC,SAAK,OAAO,SAAS,KAAK,UAAU,EAAE;AAAA,EACtC;AAAA,EACD,SAAS;AAAA,IACR,OAAO,SAAS;AACf,YAAM,aAAY,oBAAI,KAAM,GAAC,mBAAkB;AAC/C,WAAK,KAAK,KAAK,IAAI,SAAS,KAAK,OAAO,EAAE;AAAA,IAC1C;AAAA,IAED,MAAM,UAAU;AACf,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,OAAO,YAAY;AAExB,UAAI;AACH,aAAK,OAAO,2BAA2B;AACvC,cAAM,WAAW,MAAMC,cAAI;AAC3B,aAAK,OAAO,SAAS;AACrB,aAAK,SAAS,UAAU,SAAS,MAAM;AACvC,aAAK,OAAO,SAAS,KAAK,UAAU,SAAS,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;AAAA,MAC1D,SAAO,OAAO;AACf,aAAK,OAAO,SAAS;AACrB,aAAK,SAAS,MAAM,MAAM,WAAW,KAAK;AAC1C,aAAK,OAAO,SAAS,KAAK,UAAU,KAAK,CAAC,EAAE;AAAA,MAC7C,UAAU;AACT,aAAK,UAAU;AAAA,MAChB;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;AC3EA,GAAG,WAAW,eAAe;"}