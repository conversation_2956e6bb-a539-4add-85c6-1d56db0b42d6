"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      title: "药食同源",
      featuredIngredients: [],
      carouselList: [],
      loading: false
    };
  },
  onLoad() {
    this.loadCarouselList();
    this.loadFeaturedIngredients();
  },
  methods: {
    // 加载轮播图列表
    async loadCarouselList() {
      try {
        common_vendor.index.__f__("log", "at pages/index/index.vue:235", "开始加载轮播图...");
        const response = await utils_api.api.getCarouselList();
        common_vendor.index.__f__("log", "at pages/index/index.vue:237", "轮播图API响应:", response);
        if (response && response.code === 200 && response.data) {
          this.carouselList = response.data.filter((item) => item.status === 1);
          common_vendor.index.__f__("log", "at pages/index/index.vue:242", "轮播图加载成功:", this.carouselList);
        } else {
          common_vendor.index.__f__("log", "at pages/index/index.vue:244", "没有获取到轮播图数据或数据格式错误");
          this.carouselList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:248", "加载轮播图失败:", error);
        this.carouselList = [];
      }
    },
    // 轮播图点击事件
    onCarouselClick(item) {
      common_vendor.index.__f__("log", "at pages/index/index.vue:255", "点击轮播图:", item);
      if (item.redirectUrl) {
        common_vendor.index.showToast({
          title: "跳转功能开发中...",
          icon: "none"
        });
      }
    },
    // 轮播图图片加载错误处理
    onCarouselImageError(item, index) {
      common_vendor.index.__f__("error", "at pages/index/index.vue:268", "轮播图图片加载失败:", item);
      this.carouselList[index].imageUrl = "/static/logo.png";
    },
    // 加载推荐药材食材（前4条）
    async loadFeaturedIngredients() {
      try {
        this.loading = true;
        common_vendor.index.__f__("log", "at pages/index/index.vue:277", "开始加载首页推荐药材...");
        const response = await utils_api.api.getMedicinalList();
        common_vendor.index.__f__("log", "at pages/index/index.vue:280", "API响应:", response);
        if (response && response.length > 0) {
          const ingredients = response.slice(0, 4).map((item) => ({
            id: item.id,
            name: item.name,
            nature: this.formatProperty(item.property),
            meridian: this.formatMeridian(item.meridian),
            image: item.avatar || "/static/logo.png",
            // 保存原始数据用于跳转详情页
            originalData: item
          }));
          this.featuredIngredients = ingredients;
          common_vendor.index.__f__("log", "at pages/index/index.vue:295", "首页推荐药材加载成功:", this.featuredIngredients);
        } else {
          common_vendor.index.__f__("log", "at pages/index/index.vue:297", "没有获取到药材数据");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:300", "加载推荐药材失败:", error);
        this.featuredIngredients = this.getDefaultIngredients();
      } finally {
        this.loading = false;
      }
    },
    // 格式化性味信息
    formatProperty(property) {
      if (!property)
        return "甘、平";
      const cleaned = property.replace(/\r\n/g, "").replace(/\n/g, "").trim();
      return cleaned.length > 10 ? cleaned.substring(0, 10) + "..." : cleaned;
    },
    // 格式化归经信息
    formatMeridian(meridian) {
      if (!meridian)
        return "归脾、肺经";
      const cleaned = meridian.replace(/\r\n/g, "").replace(/\n/g, "").trim();
      if (cleaned.includes("归") && cleaned.includes("经")) {
        return cleaned.length > 15 ? cleaned.substring(0, 15) + "..." : cleaned;
      }
      if (cleaned.includes("补气") || cleaned.includes("脾"))
        return "归脾、肺经";
      if (cleaned.includes("补血") || cleaned.includes("肝"))
        return "归肝、心经";
      if (cleaned.includes("补肾") || cleaned.includes("肾"))
        return "归肾经";
      return "归脾、肺经";
    },
    // 获取默认数据（API失败时使用）
    getDefaultIngredients() {
      return [
        {
          id: 1,
          name: "人参",
          nature: "甘、微苦、平",
          meridian: "归脾、肺、心经",
          image: "/static/logo.png"
        },
        {
          id: 2,
          name: "枸杞",
          nature: "甘、平",
          meridian: "归肝、肾经",
          image: "/static/logo.png"
        },
        {
          id: 3,
          name: "山药",
          nature: "甘、平",
          meridian: "归脾、肺、肾经",
          image: "/static/logo.png"
        },
        {
          id: 4,
          name: "莲子",
          nature: "甘、平",
          meridian: "归脾、肾、心经",
          image: "/static/logo.png"
        }
      ];
    },
    goToRecipe() {
      common_vendor.index.navigateTo({
        url: "/pages/recipe/recipe"
      });
    },
    goToIngredients() {
      common_vendor.index.navigateTo({
        url: "/pages/ingredients/ingredients"
      });
    },
    goToProfile() {
      common_vendor.index.navigateTo({
        url: "/pages/profile/profile"
      });
    },
    goToIngredientDetail(item) {
      common_vendor.index.navigateTo({
        url: `/pages/ingredient-detail/ingredient-detail?id=${item.id}&name=${item.name}`
      });
    },
    goToPage(page) {
      switch (page) {
        case "home":
          break;
        case "ingredients":
          this.goToIngredients();
          break;
        case "recipe":
          this.goToRecipe();
          break;
        case "profile":
          this.goToProfile();
          break;
      }
    },
    goToTest() {
      common_vendor.index.navigateTo({
        url: "/pages/test/test"
      });
    },
    showComingSoon() {
      common_vendor.index.showToast({
        title: "功能开发中...",
        icon: "none"
      });
    },
    // 药材图片加载错误处理
    onImageError(item, index) {
      common_vendor.index.__f__("error", "at pages/index/index.vue:422", "药材图片加载失败:", item);
      this.featuredIngredients[index].image = "/static/logo.png";
    },
    // 获取图标文字（当图片加载失败时显示）
    getIconText(name) {
      if (!name)
        return "药";
      return name.charAt(0);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0,
    b: common_vendor.f($data.carouselList, (item, index, i0) => {
      return {
        a: item.imageUrl,
        b: common_vendor.o(($event) => $options.onCarouselImageError(item, index), item.id),
        c: common_vendor.t(item.title),
        d: item.id,
        e: common_vendor.o(($event) => $options.onCarouselClick(item), item.id)
      };
    }),
    c: $data.carouselList.length === 0
  }, $data.carouselList.length === 0 ? {} : {}, {
    d: common_vendor.o((...args) => $options.goToIngredients && $options.goToIngredients(...args)),
    e: common_vendor.o((...args) => $options.goToRecipe && $options.goToRecipe(...args)),
    f: common_vendor.o((...args) => $options.showComingSoon && $options.showComingSoon(...args)),
    g: common_vendor.o((...args) => $options.goToTest && $options.goToTest(...args)),
    h: common_vendor.o((...args) => $options.goToTest && $options.goToTest(...args)),
    i: common_vendor.o((...args) => $options.goToIngredients && $options.goToIngredients(...args)),
    j: $data.loading
  }, $data.loading ? {} : {
    k: common_vendor.f($data.featuredIngredients, (item, index, i0) => {
      return common_vendor.e({
        a: item.image && item.image !== "/static/logo.png"
      }, item.image && item.image !== "/static/logo.png" ? {
        b: item.image,
        c: common_vendor.o(($event) => $options.onImageError(item, index), item.id)
      } : {
        d: common_vendor.t($options.getIconText(item.name))
      }, {
        e: common_vendor.t(item.name),
        f: common_vendor.t(item.nature),
        g: common_vendor.t(item.meridian),
        h: item.id,
        i: common_vendor.o(($event) => $options.goToIngredientDetail(item), item.id),
        j: 0.1 * index + "s"
      });
    })
  }, {
    l: common_vendor.o((...args) => $options.showComingSoon && $options.showComingSoon(...args)),
    m: common_vendor.o(($event) => $options.goToPage("home")),
    n: common_vendor.o(($event) => $options.goToPage("ingredients")),
    o: common_vendor.o(($event) => $options.goToPage("recipe")),
    p: common_vendor.o(($event) => $options.goToPage("profile"))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
