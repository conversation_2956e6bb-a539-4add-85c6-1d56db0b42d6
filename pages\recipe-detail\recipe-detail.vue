<template>
	<view class="container">
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 食疗方案内容 -->
		<view v-else>
			<!-- 食疗方案图片 -->
			<view class="recipe-header">
				<image class="recipe-image" :src="recipeDetail.imageUrl" mode="aspectFill" @error="onImageError" @load="onImageLoad"></image>
				<view class="favorite-btn" @click="toggleFavorite">
					<text class="favorite-icon" :class="recipeDetail.isFavorite ? 'favorited' : ''">♥</text>
				</view>
			</view>
		
			<!-- 食疗方案信息 -->
			<view class="recipe-info">
				<text class="recipe-title">{{ recipeDetail.title }}</text>
				<text class="recipe-suitable">适用人群：{{ recipeDetail.suitableFor }}</text>
				
				<!-- 医生信息和评分 -->
				<view class="doctor-info" v-if="recipeDetail.doctor && recipeDetail.doctor.name">
					<view class="doctor-avatar">
						<image :src="recipeDetail.doctor.avatar || '/static/logo.png'" mode="aspectFill"></image>
					</view>
					<view class="doctor-details">
						<text class="doctor-name">{{ recipeDetail.doctor.name }}</text>
						<text class="doctor-title">{{ recipeDetail.doctor.title || '中医师' }}</text>
					</view>
					<view class="rating-info">
						<view class="rating">
							<text class="star">★</text>
							<text class="rating-score">{{ recipeDetail.rating || 0 }}</text>
						</view>
						<view class="view-count">
							<text class="view-icon">👁</text>
							<text class="view-number">{{ recipeDetail.viewCount || 0 }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 详细描述 -->
			<view class="recipe-content">
				<view class="section">
					<text class="section-title">方案介绍</text>
					<text class="section-content">{{ recipeDetail.description }}</text>
				</view>
				
				<view class="section">
					<text class="section-title">主要功效</text>
					<text class="section-content">{{ recipeDetail.effects }}</text>
				</view>
				
				<view class="section">
					<text class="section-title">制作方法</text>
					<text class="section-content">{{ recipeDetail.method }}</text>
				</view>
				
				<view class="section">
					<text class="section-title">注意事项</text>
					<text class="section-content">{{ recipeDetail.precautions }}</text>
				</view>
			</view>
			
			<!-- 底部操作按钮 -->
			<view class="bottom-actions">
				<button class="action-btn secondary" @click="shareRecipe">分享方案</button>
				<button class="action-btn primary" @click="startCooking">开始制作</button>
			</view>
		</view>
	</view>
</template>

<script>
import { getRecipeDetail, toggleFavorite } from '../../api/recipe.js';

export default {
	data() {
		return {
			recipeId: '',
			recipeDetail: {
				id: null,
				title: '',
				suitableFor: '',
				description: '',
				effects: '',
				method: '',
				precautions: '',
				imageUrl: '/static/logo.png',
				doctor: {
					name: '',
					title: '',
					avatar: '/static/logo.png'
				},
				rating: 0,
				viewCount: 0,
				isFavorite: false
			},
			loading: false,
			userId: 2 // 临时用户ID，实际应该从登录状态获取
		}
	},
	onLoad(options) {
		if (options.id) {
			this.recipeId = options.id;
			this.loadRecipeDetail();
		}
	},
	methods: {
		/**
		 * 加载食疗方案详情
		 */
		async loadRecipeDetail() {
			if (this.loading) return;
			
			this.loading = true;
			
			try {
				const response = await getRecipeDetail(this.recipeId);
				
				if (response.code === 200 && response.data) {
					console.log('获取到的详情数据:', response.data);
					console.log('图片URL:', response.data.imageUrl);
					
					// 更新详情数据
					this.recipeDetail = {
						id: response.data.id,
						title: response.data.title || '',
						suitableFor: response.data.suitableFor || '',
						description: response.data.description || '',
						effects: response.data.efficacy || '', // 使用功效字段
						method: response.data.steps || '', // 使用制作步骤字段
						precautions: '请根据个人体质和医生建议使用', // 注意事项
						imageUrl: response.data.imageUrl || '/static/logo.png',
						doctor: response.data.doctor || {
							name: '',
							title: '',
							avatar: '/static/logo.png'
						},
						rating: response.data.rating || 0,
						viewCount: response.data.viewCount || 0,
						isFavorite: response.data.isFavorite || false
					};
					
					// 测试图片URL是否可访问
					if (this.recipeDetail.imageUrl && this.recipeDetail.imageUrl !== '/static/logo.png') {
						this.testImageUrl(this.recipeDetail.imageUrl);
					}
					
					console.log('设置后的详情数据:', this.recipeDetail);
					console.log('最终图片URL:', this.recipeDetail.imageUrl);
				} else {
					uni.showToast({
						title: response.message || '获取详情失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取食疗方案详情失败:', error);
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		/**
		 * 图片加载错误处理
		 */
		onImageError(e) {
			console.error('图片加载失败:', e);
			console.log('当前图片URL:', this.recipeDetail.imageUrl);
			// 设置默认图片
			this.recipeDetail.imageUrl = '/static/logo.png';
		},
		
		/**
		 * 图片加载成功处理
		 */
		onImageLoad(e) {
			console.log('图片加载成功:', e);
			console.log('图片URL:', this.recipeDetail.imageUrl);
		},
		
		/**
		 * 测试图片URL是否可访问
		 */
		testImageUrl(url) {
			console.log('测试图片URL:', url);
			uni.request({
				url: url,
				method: 'HEAD',
				success: (res) => {
					console.log('图片URL测试成功:', res);
				},
				fail: (err) => {
					console.error('图片URL测试失败:', err);
					// 如果图片URL不可访问，使用默认图片
					this.recipeDetail.imageUrl = '/static/logo.png';
				}
			});
		},
		
		/**
		 * 切换收藏状态
		 */
		async toggleFavorite() {
			try {
				const response = await toggleFavorite({
					planId: this.recipeDetail.id,
					userId: this.userId
				});
				
				if (response.code === 200) {
					// 更新本地状态
					this.recipeDetail.isFavorite = response.data.isFavorite;
					
					uni.showToast({
						title: response.data.isFavorite ? '已收藏' : '已取消收藏',
						icon: 'none',
						duration: 1500
					});
				} else {
					uni.showToast({
						title: response.message || '操作失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('收藏操作失败:', error);
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				});
			}
		},
		
		// 分享方案
		shareRecipe() {
			uni.showActionSheet({
				itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
				success: (res) => {
					uni.showToast({
						title: '分享成功',
						icon: 'success'
					});
				}
			});
		},
		
		// 开始制作
		startCooking() {
			uni.showToast({
				title: '开始制作指导',
				icon: 'success'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 食疗方案头部图片 */
.recipe-header {
	position: relative;
	height: 500rpx;
}

.recipe-image {
	width: 100%;
	height: 100%;
}

.favorite-btn {
	position: absolute;
	top: 30rpx;
	right: 30rpx;
	width: 80rpx;
	height: 80rpx;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.favorite-icon {
	font-size: 40rpx;
	color: #ccc;
}

.favorite-icon.favorited {
	color: #4CAF50;
}

/* 食疗方案信息 */
.recipe-info {
	background-color: #fff;
	padding: 40rpx;
}

.recipe-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.recipe-suitable {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 30rpx;
}

.doctor-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.doctor-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	overflow: hidden;
}

.doctor-avatar image {
	width: 100%;
	height: 100%;
}

.doctor-details {
	flex: 1;
}

.doctor-name {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.doctor-title {
	font-size: 24rpx;
	color: #666;
}

.rating-info {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 10rpx;
}

.rating {
	display: flex;
	align-items: center;
}

.star {
	color: #FFD700;
	font-size: 28rpx;
	margin-right: 8rpx;
}

.rating-score {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
}

.view-count {
	display: flex;
	align-items: center;
}

.view-icon {
	font-size: 24rpx;
	margin-right: 8rpx;
}

.view-number {
	font-size: 24rpx;
	color: #666;
}

/* 详细内容 */
.recipe-content {
	margin-top: 20rpx;
}

.section {
	background-color: #fff;
	padding: 40rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.section-content {
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
	white-space: pre-line;
}

/* 底部操作按钮 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	border-top: 1rpx solid #eee;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
}

.action-btn.primary {
	background-color: #4CAF50;
	color: #fff;
}

.action-btn.secondary {
	background-color: #f5f5f5;
	color: #666;
}

/* 加载状态样式 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}
</style>
