# 微信小程序环境配置修复说明

## 问题描述

在微信开发者工具中运行小程序时，发现网络请求的地址不正确，显示为 `https://your-production-domain.com/medicinal/TypeList`，而不是期望的开发环境地址 `http://localhost:8083`。

## 问题原因

前端项目中存在两个不同的API配置文件，且环境判断逻辑在微信小程序环境中可能不生效：

1. **`utils/config.js`** - 主要API配置文件
   - 使用 `baseURL` 字段
   - 被 `utils/api.js` 使用（药材、轮播图等API）

2. **`config/api.js`** - 食疗方案API配置文件  
   - 使用 `baseUrl` 字段
   - 被 `api/recipe.js` 使用（食疗方案API）

## 修复内容

### 1. 强制开发环境配置

修改 `utils/config.js` 中的环境判断逻辑：

```javascript
// 获取当前环境
const getEnvironment = () => {
	// 开发阶段强制使用开发环境
	// 在正式发布时，请将此处改为 'production'
	const FORCE_DEVELOPMENT = true; // 开发阶段设置为 true
	
	if (FORCE_DEVELOPMENT) {
		return 'development';
	}
	
	// 其他环境判断逻辑...
};
```

### 2. 修复生产环境配置

将两个配置文件中的生产环境地址都修改为开发环境地址：

**`utils/config.js`**:
```javascript
production: {
	baseURL: 'http://localhost:8083',  // 修改为正确的开发环境地址
	timeout: 10000
}
```

**`config/api.js`**:
```javascript
production: {
	baseUrl: 'http://localhost:8083',  // 修改为正确的开发环境地址
	timeout: 15000
}
```

### 3. 微信小程序环境适配

在环境判断中增加微信小程序环境的处理：

```javascript
// 在微信小程序环境中，也可以通过其他方式判断
// #ifdef MP-WEIXIN
// 微信小程序环境，开发阶段使用开发环境
if (typeof wx !== 'undefined') {
	return 'development';
}
// #endif
```

## 配置文件对应关系

| 配置文件 | 使用的API文件 | 字段名 | 用途 |
|---------|-------------|-------|------|
| `utils/config.js` | `utils/api.js` | `baseURL` | 药材、轮播图等主要API |
| `config/api.js` | `api/recipe.js` | `baseUrl` | 食疗方案相关API |

## 验证方法

1. **重新编译项目**：在微信开发者工具中重新编译项目
2. **查看控制台**：检查网络请求的URL是否为 `http://localhost:8083`
3. **测试API调用**：确认各个API接口能正常调用

## 注意事项

1. **正式发布时**：需要将 `FORCE_DEVELOPMENT` 设置为 `false`，并配置正确的生产环境地址
2. **环境一致性**：确保两个配置文件中的地址保持一致
3. **编译缓存**：修改配置后需要重新编译项目才能生效

## 后续优化建议

1. **统一配置文件**：考虑将两个配置文件合并为一个，避免配置不一致
2. **环境变量**：使用uni-app的环境变量功能来管理不同环境的配置
3. **自动化部署**：在CI/CD流程中自动切换环境配置

## 测试确认

修复后，在微信开发者工具的网络面板中应该看到：
- 请求地址：`http://localhost:8083/medicinal/TypeList`
- 请求地址：`http://localhost:8083/carousel/list`
- 请求地址：`http://localhost:8083/recipe/list`

所有API请求都应该指向正确的开发环境地址。
