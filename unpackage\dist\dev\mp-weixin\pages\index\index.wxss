
.container.data-v-1cf27b2a {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
}

	/* 顶部导航栏 */
.header.data-v-1cf27b2a {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.header-left.data-v-1cf27b2a {
		display: flex;
		align-items: center;
}
.leaf-icon.data-v-1cf27b2a {
		font-size: 40rpx;
		margin-right: 15rpx;
		color: #52c41a;
}
.app-title.data-v-1cf27b2a {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
}
.header-right.data-v-1cf27b2a {
		display: flex;
		align-items: center;
		gap: 25rpx;
}
.search-icon.data-v-1cf27b2a, .notification-icon.data-v-1cf27b2a {
		font-size: 36rpx;
		color: #666;
}
.avatar.data-v-1cf27b2a {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
}

	/* 轮播图样式 */
.carousel-section.data-v-1cf27b2a {
		margin: 20rpx 30rpx;
		border-radius: 20rpx;
		overflow: hidden;
}
.carousel-swiper.data-v-1cf27b2a {
		height: 300rpx;
		border-radius: 20rpx;
}
.carousel-item.data-v-1cf27b2a {
		position: relative;
		width: 100%;
		height: 100%;
		border-radius: 20rpx;
		overflow: hidden;
}
.carousel-image.data-v-1cf27b2a {
		width: 100%;
		height: 100%;
		object-fit: cover;
}
.carousel-overlay.data-v-1cf27b2a {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
		padding: 30rpx 40rpx 40rpx;
}
.carousel-title.data-v-1cf27b2a {
		font-size: 42rpx;
		font-weight: bold;
		color: #ffffff;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
		line-height: 1.2;
}

	/* 默认横幅样式（当没有轮播图时显示） */
.default-banner.data-v-1cf27b2a {
		position: relative;
}
.banner-bg-placeholder.data-v-1cf27b2a {
		width: 100%;
		height: 100%;
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 50%, #95de64 100%);
		background-image:
			radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
			radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}
.banner-overlay.data-v-1cf27b2a {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(45deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.3));
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 40rpx;
}
.banner-title.data-v-1cf27b2a {
		font-size: 42rpx;
		font-weight: bold;
		color: #ffffff;
		margin-bottom: 15rpx;
}
.banner-subtitle.data-v-1cf27b2a {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.9);
}

	/* 功能导航 */
.nav-section.data-v-1cf27b2a {
		padding: 30rpx;
}
.nav-grid.data-v-1cf27b2a {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 30rpx;
}
.nav-item.data-v-1cf27b2a {
		background-color: #ffffff;
		border-radius: 15rpx;
		padding: 30rpx 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;
}
.nav-icon.data-v-1cf27b2a {
		display: block;
		font-size: 48rpx;
		margin-bottom: 15rpx;
		color: #52c41a;
}
.nav-text.data-v-1cf27b2a {
		font-size: 24rpx;
		color: #666;
}

	/* 体质测试卡片样式 */
.test-section.data-v-1cf27b2a {
		padding: 20rpx 30rpx;
		margin-top: 20rpx;
}
.test-card.data-v-1cf27b2a {
		background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
		border-radius: 16rpx;
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 6rpx 24rpx rgba(76, 175, 80, 0.3);
		transition: all 0.3s ease;
}
.test-content.data-v-1cf27b2a {
		flex: 1;
}
.test-title.data-v-1cf27b2a {
		font-size: 32rpx;
		color: white;
		font-weight: 600;
		display: block;
		margin-bottom: 8rpx;
}
.test-subtitle.data-v-1cf27b2a {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.9);
		line-height: 1.4;
}
.test-button.data-v-1cf27b2a {
		background: rgba(255, 255, 255, 0.2);
		border: 2rpx solid rgba(255, 255, 255, 0.3);
		border-radius: 50rpx;
		padding: 16rpx 32rpx;
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
}
.test-btn-text.data-v-1cf27b2a {
		color: white;
		font-size: 28rpx;
		font-weight: 500;
}

	/* 药材与食材推荐 */
.ingredients-section.data-v-1cf27b2a {
		padding: 0 30rpx;
}
.section-header.data-v-1cf27b2a {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
}
.section-title.data-v-1cf27b2a {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
}
.section-more.data-v-1cf27b2a {
		font-size: 28rpx;
		color: #52c41a;
}
.ingredients-grid.data-v-1cf27b2a {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
}
.ingredient-card.data-v-1cf27b2a {
		background-color: #ffffff;
		border-radius: 15rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;
}
.ingredient-image-container.data-v-1cf27b2a {
		position: relative;
		width: 100%;
		height: 200rpx;
}
.ingredient-image.data-v-1cf27b2a {
		width: 100%;
		height: 100%;
		object-fit: cover;
}
.ingredient-icon.data-v-1cf27b2a {
		width: 100%;
		height: 100%;
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
		display: flex;
		align-items: center;
		justify-content: center;
}
.icon-text.data-v-1cf27b2a {
		font-size: 60rpx;
		font-weight: bold;
		color: #ffffff;
}
.ingredient-info.data-v-1cf27b2a {
		padding: 20rpx;
}
.ingredient-name.data-v-1cf27b2a {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
}
.ingredient-nature.data-v-1cf27b2a, .ingredient-effect.data-v-1cf27b2a {
		display: block;
		font-size: 24rpx;
		color: #666;
		margin-bottom: 5rpx;
}

	/* 加载状态样式 */
.loading-container.data-v-1cf27b2a {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 200rpx;
}
.loading-text.data-v-1cf27b2a {
		font-size: 28rpx;
		color: #999;
}

	/* 营养与功效分析 */
.analysis-section.data-v-1cf27b2a {
		padding: 0 30rpx;
		margin-top: 40rpx;
}
.nutrition-card.data-v-1cf27b2a, .efficacy-card.data-v-1cf27b2a {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.card-title.data-v-1cf27b2a {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 10rpx;
		display: block;
}
.card-subtitle.data-v-1cf27b2a {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 30rpx;
		display: block;
}

	/* 柱状图样式 */
.chart-container.data-v-1cf27b2a {
		padding: 20rpx 0;
}
.bar-chart.data-v-1cf27b2a {
		display: flex;
		justify-content: space-around;
		align-items: flex-end;
		height: 200rpx;
}
.bar-item.data-v-1cf27b2a {
		display: flex;
		flex-direction: column;
		align-items: center;
}
.bar.data-v-1cf27b2a {
		width: 60rpx;
		border-radius: 8rpx 8rpx 0 0;
		margin-bottom: 15rpx;
		transition: all 0.3s ease;
}
.bar.green.data-v-1cf27b2a {
		background: linear-gradient(to top, #4CAF50, #66BB6A);
}
.bar.orange.data-v-1cf27b2a {
		background: linear-gradient(to top, #FF9800, #FFB74D);
}
.bar-label.data-v-1cf27b2a {
		font-size: 24rpx;
		color: #666;
}

	/* 功效分析样式 */
.efficacy-list.data-v-1cf27b2a {
		margin-top: 20rpx;
}
.efficacy-item.data-v-1cf27b2a {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 25rpx;
}
.efficacy-name.data-v-1cf27b2a {
		font-size: 28rpx;
		color: #333;
		width: 160rpx;
}
.progress-container.data-v-1cf27b2a {
		display: flex;
		align-items: center;
		flex: 1;
		margin-left: 20rpx;
}
.progress-bar.data-v-1cf27b2a {
		flex: 1;
		height: 12rpx;
		background: #f0f0f0;
		border-radius: 6rpx;
		overflow: hidden;
		margin-right: 15rpx;
}
.progress-fill.data-v-1cf27b2a {
		height: 100%;
		background: linear-gradient(to right, #4CAF50, #66BB6A);
		border-radius: 6rpx;
		transition: width 0.8s ease;
}
.progress-text.data-v-1cf27b2a {
		font-size: 24rpx;
		color: #4CAF50;
		font-weight: 600;
		width: 60rpx;
		text-align: right;
}

	/* 底部导航 */
.bottom-nav.data-v-1cf27b2a {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 120rpx;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: space-around;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		z-index: 1000;
}
.bottom-nav .nav-item.data-v-1cf27b2a {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 10rpx;
		background: none;
		box-shadow: none;
		transition: all 0.3s ease;
}
.bottom-nav .nav-item.active .nav-icon.data-v-1cf27b2a {
		color: #52c41a;
}
.bottom-nav .nav-item.active .nav-text.data-v-1cf27b2a {
		color: #52c41a;
}
.bottom-nav .nav-icon.data-v-1cf27b2a {
		font-size: 40rpx;
		margin-bottom: 5rpx;
		color: #999;
}
.bottom-nav .nav-text.data-v-1cf27b2a {
		font-size: 20rpx;
		color: #999;
}
.add-btn.data-v-1cf27b2a {
		position: relative;
}
.add-icon.data-v-1cf27b2a {
		background: linear-gradient(45deg, #52c41a, #73d13d);
		color: #ffffff;
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 48rpx;
		font-weight: bold;
		box-shadow: 0 4rpx 15rpx rgba(82, 196, 26, 0.3);
}

	/* 动画效果 */
.fade-in.data-v-1cf27b2a {
		opacity: 0;
		transform: translateY(30rpx);
		animation: fadeInUp-1cf27b2a 0.8s ease forwards;
}
@keyframes fadeInUp-1cf27b2a {
to {
			opacity: 1;
			transform: translateY(0);
}
}
.card-hover.data-v-1cf27b2a:active {
		transform: scale(0.95);
}
