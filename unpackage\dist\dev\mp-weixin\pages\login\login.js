"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      phone: "",
      password: "",
      showPassword: false,
      isLoading: false
    };
  },
  computed: {
    canLogin() {
      return this.phone.length === 11 && this.password.length >= 6 && !this.isLoading;
    }
  },
  methods: {
    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword;
    },
    // 处理登录
    async handleLogin() {
      if (!this.canLogin)
        return;
      const phoneReg = /^1[3-9]\d{9}$/;
      if (!phoneReg.test(this.phone)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }
      this.isLoading = true;
      try {
        await this.simulateLogin();
        const userInfo = {
          phone: this.phone,
          nickname: "养生达人",
          avatar: "/static/avatar.png",
          loginTime: Date.now()
        };
        common_vendor.index.setStorageSync("userInfo", userInfo);
        common_vendor.index.setStorageSync("token", "mock_token_" + Date.now());
        common_vendor.index.showToast({
          title: "登录成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } catch (error) {
        common_vendor.index.showToast({
          title: "登录失败，请重试",
          icon: "none"
        });
      } finally {
        this.isLoading = false;
      }
    },
    // 模拟登录请求
    simulateLogin() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (this.password.length >= 6) {
            resolve();
          } else {
            reject(new Error("密码错误"));
          }
        }, 2e3);
      });
    },
    // 跳转到注册页面
    goToRegister() {
      common_vendor.index.showToast({
        title: "注册功能开发中",
        icon: "none"
      });
    },
    // 忘记密码
    forgotPassword() {
      common_vendor.index.showToast({
        title: "找回密码功能开发中",
        icon: "none"
      });
    },
    // 显示用户协议
    showAgreement() {
      common_vendor.index.showToast({
        title: "用户协议",
        icon: "none"
      });
    },
    // 显示隐私政策
    showPrivacy() {
      common_vendor.index.showToast({
        title: "隐私政策",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0,
    b: $data.phone,
    c: common_vendor.o(($event) => $data.phone = $event.detail.value),
    d: $data.showPassword ? "text" : "password",
    e: $data.password,
    f: common_vendor.o(($event) => $data.password = $event.detail.value),
    g: common_vendor.t($data.showPassword ? "👁️" : "👁️‍🗨️"),
    h: common_vendor.o((...args) => $options.togglePassword && $options.togglePassword(...args)),
    i: common_vendor.t($data.isLoading ? "登录中..." : "立即登录"),
    j: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args)),
    k: !$options.canLogin,
    l: common_vendor.o((...args) => $options.goToRegister && $options.goToRegister(...args)),
    m: common_vendor.o((...args) => $options.forgotPassword && $options.forgotPassword(...args)),
    n: common_vendor.o((...args) => $options.showAgreement && $options.showAgreement(...args)),
    o: common_vendor.o((...args) => $options.showPrivacy && $options.showPrivacy(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
